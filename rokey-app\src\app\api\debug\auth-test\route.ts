import { type NextRequest, NextResponse } from 'next/server';
import { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';

export async function POST(request: NextRequest) {
  try {
    console.log('🔍 Debug: Starting auth test');
    
    // Log all headers
    const headers = Object.fromEntries(request.headers.entries());
    console.log('📋 Debug: All headers:', headers);
    
    // Check specific headers
    const authHeader = request.headers.get('authorization');
    const apiKeyHeader = request.headers.get('x-api-key');
    
    console.log('🔑 Debug: Authorization header:', authHeader);
    console.log('🔑 Debug: X-API-Key header:', apiKeyHeader);
    
    // Check environment variables
    console.log('🌍 Debug: NEXT_PUBLIC_SUPABASE_URL exists:', !!process.env.NEXT_PUBLIC_SUPABASE_URL);
    console.log('🌍 Debug: SUPABASE_SERVICE_ROLE_KEY exists:', !!process.env.SUPABASE_SERVICE_ROLE_KEY);
    
    const authMiddleware = new ApiKeyAuthMiddleware();
    const authResult = await authMiddleware.authenticateRequest(request);
    
    console.log('✅ Debug: Auth result:', authResult);
    
    return NextResponse.json({
      debug: {
        headers: {
          authorization: authHeader,
          'x-api-key': apiKeyHeader
        },
        environment: {
          supabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
          serviceRoleKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY
        },
        authResult
      }
    });
    
  } catch (error) {
    console.error('❌ Debug: Error in auth test:', error);
    return NextResponse.json({
      error: 'Debug test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
