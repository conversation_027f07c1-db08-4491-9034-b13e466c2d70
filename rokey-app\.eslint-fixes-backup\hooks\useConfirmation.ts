'use client';

import { useState, useCallback } from 'react';

interface ConfirmationOptions {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'danger' | 'warning' | 'info';
}

interface ConfirmationState extends ConfirmationOptions {
  isOpen: boolean;
  isLoading: boolean;
  onConfirm: () => void;
}

export function useConfirmation() {
  const [state, setState] = useState<ConfirmationState>({
    isOpen: false,
    isLoading: false,
    title: '',
    message: '',
    confirmText: 'Confirm',
    cancelText: 'Cancel',
    type: 'danger',
    onConfirm: () => {}
  });

  const showConfirmation = useCallback((
    options: ConfirmationOptions,
    onConfirm: () => void | Promise<void>
  ) => {
    setState({
      isOpen: true,
      isLoading: false,
      title: options.title,
      message: options.message,
      confirmText: options.confirmText || 'Confirm',
      cancelText: options.cancelText || 'Cancel',
      type: options.type || 'danger',
      onConfirm: async () => {
        setState(prev => ({ ...prev, isLoading: true }));
        try {
          await onConfirm();
          setState(prev => ({ ...prev, isOpen: false, isLoading: false }));
        } catch (error) {
          setState(prev => ({ ...prev, isLoading: false }));
          throw error;
        }
      }
    });
  }, []);

  const hideConfirmation = useCallback(() => {
    setState(prev => ({ ...prev, isOpen: false, isLoading: false }));
  }, []);

  return {
    ...state,
    showConfirmation,
    hideConfirmation
  };
}
