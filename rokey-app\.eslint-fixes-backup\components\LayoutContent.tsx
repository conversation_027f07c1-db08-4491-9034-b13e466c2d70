'use client';

import { Suspense } from "react";
import Navbar from "@/components/Navbar";
import Sidebar from "@/components/Sidebar";
import { useSidebar } from "@/contexts/SidebarContext";
import { useNavigationSafe } from "@/contexts/NavigationContext";
import OptimisticLoadingScreen from "@/components/OptimisticLoadingScreen";
import OptimisticPageLoader from "@/components/OptimisticPageLoader";
import { useAdvancedPreloading } from "@/hooks/useAdvancedPreloading";

function LayoutContentInner({ children }: { children: React.ReactNode }) {
  const { isCollapsed, collapseSidebar } = useSidebar();
  const navigationContext = useNavigationSafe();
  const { isNavigating, targetRoute, isPageCached } = navigationContext || {
    isNavigating: false,
    targetRoute: null,
    isPageCached: () => false
  };

  // Initialize advanced preloading system
  useAdvancedPreloading({
    maxConcurrent: 2,
    idleTimeout: 1500,
    backgroundDelay: 3000
  });

  return (
    <div className="flex flex-1 overflow-hidden">
      {/* Desktop Sidebar */}
      <div className="hidden lg:block">
        <Sidebar />
      </div>

      {/* Mobile Sidebar Overlay */}
      <div className={`lg:hidden fixed inset-0 z-50 ${isCollapsed ? 'pointer-events-none' : ''}`}>
        {/* Backdrop */}
        <div
          onClick={collapseSidebar}
          className={`absolute inset-0 bg-black transition-opacity duration-200 ease-out cursor-pointer ${
            isCollapsed ? 'opacity-0' : 'opacity-50'
          }`}
        />

        {/* Sidebar */}
        <div className={`absolute left-0 top-0 h-full transform transition-transform duration-200 ease-out ${
          isCollapsed ? '-translate-x-full' : 'translate-x-0'
        }`}>
          <Sidebar />
        </div>
      </div>

      {/* Main content area */}
      <div className="flex-1 flex flex-col overflow-hidden min-w-0">
        <Navbar />
        <main className="flex-1 overflow-y-auto content-area">
          <div className="p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto w-full">
            <div className="page-transition">
              {isNavigating && targetRoute ? (
                <OptimisticPageLoader targetRoute={targetRoute}>
                  {children}
                </OptimisticPageLoader>
              ) : (
                children
              )}
            </div>
          </div>
        </main>
      </div>


    </div>
  );
}

export default function LayoutContent({ children }: { children: React.ReactNode }) {
  return (
    <Suspense fallback={
      <div className="flex flex-1 overflow-hidden">
        <div className="hidden lg:block w-64 bg-gray-50 animate-pulse"></div>
        <div className="flex-1 flex flex-col overflow-hidden min-w-0">
          <div className="h-16 bg-white border-b border-gray-200 animate-pulse"></div>
          <main className="flex-1 overflow-y-auto">
            <div className="p-4 sm:p-6 lg:p-8 max-w-7xl mx-auto w-full">
              <div className="space-y-4">
                <div className="h-8 bg-gray-200 rounded w-1/3 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
                  ))}
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
    }>
      <LayoutContentInner>{children}</LayoutContentInner>
    </Suspense>
  );
}
