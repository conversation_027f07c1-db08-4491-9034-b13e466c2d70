import { createClient } from '@supabase/supabase-js';

export interface AsyncJob {
  id: string;
  user_id: string;
  api_key_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'timeout';
  request_data: any;
  response_data?: any;
  error_message?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  estimated_completion?: string;
  progress_percentage?: number;
  roles_detected?: string[];
  webhook_url?: string;
}

export class AsyncJobManager {
  private supabase;

  constructor() {
    this.supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
  }

  /**
   * Creates a new async job
   */
  async createJob(
    userId: string,
    apiKeyId: string,
    requestData: any,
    webhookUrl?: string
  ): Promise<AsyncJob> {
    const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Estimate completion time based on request complexity
    const estimatedMinutes = this.estimateCompletionTime(requestData);
    const estimatedCompletion = new Date(Date.now() + estimatedMinutes * 60000).toISOString();

    const job: Partial<AsyncJob> = {
      id: jobId,
      user_id: userId,
      api_key_id: apiKeyId,
      status: 'pending',
      request_data: requestData,
      created_at: new Date().toISOString(),
      estimated_completion: estimatedCompletion,
      webhook_url: webhookUrl,
      progress_percentage: 0
    };

    const { data, error } = await this.supabase
      .from('async_jobs')
      .insert(job)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create async job: ${error.message}`);
    }

    return data as AsyncJob;
  }

  /**
   * Updates job status and progress
   */
  async updateJob(
    jobId: string,
    updates: Partial<AsyncJob>
  ): Promise<void> {
    const { error } = await this.supabase
      .from('async_jobs')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', jobId);

    if (error) {
      throw new Error(`Failed to update job ${jobId}: ${error.message}`);
    }
  }

  /**
   * Gets job by ID
   */
  async getJob(jobId: string): Promise<AsyncJob | null> {
    const { data, error } = await this.supabase
      .from('async_jobs')
      .select('*')
      .eq('id', jobId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw new Error(`Failed to get job ${jobId}: ${error.message}`);
    }

    return data as AsyncJob;
  }

  /**
   * Gets jobs for a user
   */
  async getUserJobs(
    userId: string,
    limit: number = 50,
    status?: AsyncJob['status']
  ): Promise<AsyncJob[]> {
    let query = this.supabase
      .from('async_jobs')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to get user jobs: ${error.message}`);
    }

    return data as AsyncJob[];
  }

  /**
   * Marks job as started
   */
  async startJob(jobId: string, rolesDetected?: string[]): Promise<void> {
    await this.updateJob(jobId, {
      status: 'processing',
      started_at: new Date().toISOString(),
      roles_detected: rolesDetected,
      progress_percentage: 10
    });
  }

  /**
   * Marks job as completed
   */
  async completeJob(jobId: string, responseData: any, rolesUsed?: string[]): Promise<void> {
    await this.updateJob(jobId, {
      status: 'completed',
      completed_at: new Date().toISOString(),
      response_data: responseData,
      roles_detected: rolesUsed,
      progress_percentage: 100
    });
  }

  /**
   * Marks job as failed
   */
  async failJob(jobId: string, errorMessage: string): Promise<void> {
    await this.updateJob(jobId, {
      status: 'failed',
      completed_at: new Date().toISOString(),
      error_message: errorMessage,
      progress_percentage: 0
    });
  }

  /**
   * Updates job progress
   */
  async updateProgress(jobId: string, percentage: number, message?: string): Promise<void> {
    const updates: Partial<AsyncJob> = {
      progress_percentage: Math.min(100, Math.max(0, percentage))
    };

    if (message) {
      updates.error_message = message; // Reuse for progress messages
    }

    await this.updateJob(jobId, updates);
  }

  /**
   * Estimates completion time based on request complexity
   */
  private estimateCompletionTime(requestData: any): number {
    let baseMinutes = 2; // Base 2 minutes

    // Add time based on message length
    const totalLength = requestData.messages?.reduce((sum: number, msg: any) => 
      sum + (msg.content?.length || 0), 0) || 0;
    baseMinutes += Math.ceil(totalLength / 1000); // 1 minute per 1000 chars

    // Add time for max_tokens
    if (requestData.max_tokens > 1000) {
      baseMinutes += Math.ceil(requestData.max_tokens / 500); // 1 minute per 500 tokens
    }

    // Add time for role complexity
    if (requestData.role) {
      baseMinutes += 3; // Role-based routing adds complexity
    }

    // Cap at reasonable maximum
    return Math.min(baseMinutes, 15);
  }

  /**
   * Cleanup old jobs (call periodically)
   */
  async cleanupOldJobs(olderThanDays: number = 7): Promise<number> {
    const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000).toISOString();

    const { data, error } = await this.supabase
      .from('async_jobs')
      .delete()
      .lt('created_at', cutoffDate)
      .select('id');

    if (error) {
      throw new Error(`Failed to cleanup old jobs: ${error.message}`);
    }

    return data?.length || 0;
  }
}
