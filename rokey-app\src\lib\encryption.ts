// Web Crypto API compatible encryption for Edge Runtime
const ALGORITHM = 'AES-GCM';
const IV_LENGTH = 12; // Recommended for GCM

// Ensure your ROKEY_ENCRYPTION_KEY is a 64-character hex string (32 bytes)
const ROKEY_ENCRYPTION_KEY_FROM_ENV = process.env.ROKEY_ENCRYPTION_KEY;

console.log('[DEBUG] ROKEY_ENCRYPTION_KEY from process.env:', ROKEY_ENCRYPTION_KEY_FROM_ENV);
console.log('[DEBUG] Length:', ROKEY_ENCRYPTION_KEY_FROM_ENV?.length);

if (!ROKEY_ENCRYPTION_KEY_FROM_ENV || ROKEY_ENCRYPTION_KEY_FROM_ENV.length !== 64) {
  throw new Error('Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.');
}

// Convert hex string to Uint8Array for Web Crypto API
function hexToUint8Array(hex: string): Uint8Array {
  const bytes = new Uint8Array(hex.length / 2);
  for (let i = 0; i < hex.length; i += 2) {
    bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
  }
  return bytes;
}

// Convert Uint8Array to hex string
function uint8ArrayToHex(bytes: Uint8Array): string {
  return Array.from(bytes, byte => byte.toString(16).padStart(2, '0')).join('');
}

const keyBytes = hexToUint8Array(ROKEY_ENCRYPTION_KEY_FROM_ENV);

export async function encrypt(text: string): Promise<string> {
  if (typeof text !== 'string' || text.length === 0) {
    throw new Error('Encryption input must be a non-empty string.');
  }

  // Generate random IV
  const iv = crypto.getRandomValues(new Uint8Array(IV_LENGTH));

  // Import the key for Web Crypto API
  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyBytes,
    { name: ALGORITHM },
    false,
    ['encrypt']
  );

  // Encrypt the text
  const encodedText = new TextEncoder().encode(text);
  const encryptedBuffer = await crypto.subtle.encrypt(
    {
      name: ALGORITHM,
      iv: iv
    },
    cryptoKey,
    encodedText
  );

  const encryptedArray = new Uint8Array(encryptedBuffer);

  // For AES-GCM, the auth tag is included in the encrypted data (last 16 bytes)
  const encryptedData = encryptedArray.slice(0, -16);
  const authTag = encryptedArray.slice(-16);

  // Return IV:authTag:encryptedData format
  return `${uint8ArrayToHex(iv)}:${uint8ArrayToHex(authTag)}:${uint8ArrayToHex(encryptedData)}`;
}

export async function decrypt(encryptedText: string): Promise<string> {
  if (typeof encryptedText !== 'string' || encryptedText.length === 0) {
    throw new Error('Decryption input must be a non-empty string.');
  }

  const parts = encryptedText.split(':');
  if (parts.length !== 3) {
    throw new Error('Invalid encrypted text format. Expected iv:authTag:encryptedData');
  }

  const iv = hexToUint8Array(parts[0]);
  const authTag = hexToUint8Array(parts[1]);
  const encryptedData = hexToUint8Array(parts[2]);

  if (iv.length !== IV_LENGTH) {
    throw new Error(`Invalid IV length. Expected ${IV_LENGTH} bytes.`);
  }
  if (authTag.length !== 16) {
    throw new Error(`Invalid authTag length. Expected 16 bytes.`);
  }

  // Import the key for Web Crypto API
  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyBytes,
    { name: ALGORITHM },
    false,
    ['decrypt']
  );

  // Combine encrypted data and auth tag for Web Crypto API
  const combinedData = new Uint8Array(encryptedData.length + authTag.length);
  combinedData.set(encryptedData);
  combinedData.set(authTag, encryptedData.length);

  // Decrypt the data
  const decryptedBuffer = await crypto.subtle.decrypt(
    {
      name: ALGORITHM,
      iv: iv
    },
    cryptoKey,
    combinedData
  );

  return new TextDecoder().decode(decryptedBuffer);
}