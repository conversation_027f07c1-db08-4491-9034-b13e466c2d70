import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    message: 'Webhook test endpoint is active',
    timestamp: new Date().toISOString(),
    instructions: [
      '1. Go to your Stripe Dashboard',
      '2. Navigate to Developers > Webhooks',
      '3. Find your webhook endpoint',
      '4. Click "Send test webhook"',
      '5. Select "customer.subscription.updated" event',
      '6. Check the webhook logs in your billing page debug section'
    ]
  });
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.text();
    const headers = Object.fromEntries(req.headers.entries());
    
    console.log('🧪 TEST WEBHOOK RECEIVED:');
    console.log('Headers:', headers);
    console.log('Body:', body);

    // Try to parse as JSON
    let parsedBody;
    try {
      parsedBody = JSON.parse(body);
    } catch (e) {
      parsedBody = { raw: body };
    }

    // Log to webhook logs
    try {
      await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/debug/webhook-logs`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          eventType: 'test.webhook.received',
          details: {
            headers: {
              'stripe-signature': headers['stripe-signature'],
              'user-agent': headers['user-agent'],
              'content-type': headers['content-type']
            },
            bodySize: body.length,
            parsedBody
          }
        })
      }).catch(err => console.log('Failed to log test webhook:', err.message));
    } catch (err) {
      console.log('Failed to log test webhook:', err);
    }

    return NextResponse.json({
      success: true,
      message: 'Test webhook received successfully',
      timestamp: new Date().toISOString(),
      bodySize: body.length,
      hasStripeSignature: !!headers['stripe-signature']
    });

  } catch (error) {
    console.error('Test webhook error:', error);
    return NextResponse.json(
      { error: 'Test webhook failed', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
