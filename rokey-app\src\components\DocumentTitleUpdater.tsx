'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';

export default function DocumentTitleUpdater() {
  const pathname = usePathname();

  useEffect(() => {
    if (typeof document !== 'undefined') {
      // Simple title generation without using the problematic hook
      const getPageTitle = (path: string) => {
        switch (path) {
          case '/dashboard':
            return 'Dashboard - RouKey';
          case '/playground':
            return 'Playground - RouKey';
          case '/my-models':
            return 'My Models - RouKey';
          case '/routing-setup':
            return 'Routing Setup - RouKey';
          case '/logs':
            return 'Logs - RouKey';
          case '/training':
            return 'Prompt Engineering - RouKey';
          case '/analytics':
            return 'Analytics - RouKey';
          case '/add-keys':
            return 'Add Keys - RouKey';
          case '/features':
            return 'Features - RouKey';
          case '/routing-strategies':
            return 'Routing Strategies - RouKey';
          case '/contact':
            return 'Contact - RouKey';
          case '/about':
            return 'About - RouKey';
          case '/pricing':
            return 'Pricing - RouKey';
          default:
            return 'RouKey - AI Gateway';
        }
      };

      document.title = getPageTitle(pathname);
    }
  }, [pathname]);

  return null; // This component doesn't render anything
}
