export default function LogsLoading() {
  return (
    <div className="space-y-8 animate-fade-in">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <div className="animate-pulse bg-gray-200 h-10 w-64 rounded mb-2"></div>
          <div className="animate-pulse bg-gray-200 h-4 w-80 rounded"></div>
        </div>
        <div className="animate-pulse bg-gray-200 h-10 w-32 rounded"></div>
      </div>

      {/* Filters */}
      <div className="card p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="space-y-2">
              <div className="animate-pulse bg-gray-200 h-4 w-20 rounded"></div>
              <div className="animate-pulse bg-gray-200 h-10 w-full rounded"></div>
            </div>
          ))}
        </div>
        <div className="flex gap-3 mt-6">
          <div className="animate-pulse bg-gray-200 h-10 w-24 rounded"></div>
          <div className="animate-pulse bg-gray-200 h-10 w-20 rounded"></div>
        </div>
      </div>

      {/* Table */}
      <div className="card overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                {Array.from({ length: 11 }).map((_, i) => (
                  <th key={i} className="px-6 py-4">
                    <div className="animate-pulse bg-gray-200 h-4 w-20 rounded"></div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {Array.from({ length: 8 }).map((_, i) => (
                <tr key={i} className="hover:bg-gray-50">
                  {Array.from({ length: 11 }).map((_, j) => (
                    <td key={j} className="px-6 py-4">
                      <div className="animate-pulse bg-gray-200 h-4 w-16 rounded"></div>
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="animate-pulse bg-gray-200 h-4 w-32 rounded"></div>
        <div className="flex space-x-2">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="animate-pulse bg-gray-200 h-8 w-8 rounded"></div>
          ))}
        </div>
      </div>
    </div>
  );
}
