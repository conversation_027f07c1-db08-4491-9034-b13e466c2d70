'use client';

import { useEffect, useState, FormEvent, useCallback } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  Cog6ToothIcon,
  BoltIcon,
  CircleStackIcon,
  ListBulletIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
// Temporarily disable DnD Kit to avoid potential conflicts
// import {
//   DndContext,
//   closestCenter,
//   KeyboardSensor,
//   PointerSensor,
//   useSensor,
//   useSensors,
//   DragEndEvent,
// } from '@dnd-kit/core';
// import {
//   arrayMove,
//   SortableContext,
//   sortableKeyboardCoordinates,
//   verticalListSortingStrategy,
// } from '@dnd-kit/sortable';
// import {
//   useSortable,
// } from '@dnd-kit/sortable';
// import { CSS } from '@dnd-kit/utilities';
import { type Display<PERSON>pi<PERSON><PERSON> } from '@/types/apiKeys'; // Assuming this type is defined
import { useRoutingSetupPrefetch } from '@/hooks/useRoutingSetupPrefetch';
import RoutingSetupLoadingSkeleton, { CompactRoutingSetupLoadingSkeleton } from '@/components/RoutingSetupLoadingSkeleton';

interface CustomApiConfig {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  routing_strategy: string | null;
  routing_strategy_params: any | null; 
}

type RoutingStrategyType = 'none' | 'intelligent_role' | 'complexity_round_robin' | 'strict_fallback';

const ROUTING_STRATEGIES: Array<{
  id: RoutingStrategyType;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  shortDescription: string;
}> = [
  {
    id: 'none',
    name: 'Default Behavior',
    shortDescription: 'Automatic load balancing',
    description: "RoKey will automatically load balance across all keys assigned to this configuration with intra-request retries. No extra setup needed.",
    icon: Cog6ToothIcon
  },
  {
    id: 'intelligent_role',
    name: 'Intelligent Role Routing',
    shortDescription: 'AI-powered role classification',
    description: "RoKey uses an LLM to classify the user's prompt and routes to a key associated with that role. If no match, uses the 'Default General Chat Model'.",
    icon: BoltIcon
  },
  {
    id: 'complexity_round_robin',
    name: 'Complexity-Based Round-Robin',
    shortDescription: 'Route by prompt complexity',
    description: 'RoKey classifies prompt complexity (1-5) and round-robins among active keys assigned to that complexity. Searches proximal levels if no exact match.',
    icon: CircleStackIcon
  },
  {
    id: 'strict_fallback',
    name: 'Strict Fallback',
    shortDescription: 'Ordered failover sequence',
    description: 'Define an ordered list of API keys. RoKey will try them in sequence until one succeeds.',
    icon: ListBulletIcon
  },
];

// Simplified API Key Item Component (temporarily disabled DnD)
function SimpleApiKeyItem({ apiKey, index, onMoveUp, onMoveDown }: {
  apiKey: DisplayApiKey;
  index: number;
  onMoveUp?: () => void;
  onMoveDown?: () => void;
}) {
  return (
    <li className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm transition-all duration-200 hover:shadow-md hover:border-gray-300">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-orange-50 border border-orange-200 rounded-lg flex items-center justify-center">
            <span className="text-sm font-semibold text-orange-600">{index + 1}</span>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-900">{apiKey.label}</span>
            <p className="text-xs text-gray-600">{apiKey.provider} - {apiKey.predefined_model_id}</p>
          </div>
        </div>
        <div className="flex items-center space-x-1">
          {onMoveUp && (
            <button
              onClick={onMoveUp}
              className="p-1 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded transition-all duration-200"
              title="Move up"
            >
              <ArrowUpIcon className="w-4 h-4" />
            </button>
          )}
          {onMoveDown && (
            <button
              onClick={onMoveDown}
              className="p-1 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded transition-all duration-200"
              title="Move down"
            >
              <ArrowDownIcon className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>
    </li>
  );
}

export default function RoutingSetupConfigPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const configId = params.configId as string;

  // Prefetch hook
  const { getCachedData, isCached } = useRoutingSetupPrefetch();

  // Smart back navigation using search params and fallback to referrer
  const getBackUrl = () => {
    // First, check if we have a 'from' parameter in the URL
    const fromParam = searchParams.get('from');

    if (fromParam === 'routing-setup') {
      return '/routing-setup';
    }

    if (fromParam === 'model-config') {
      return `/my-models/${configId}`;
    }

    // Fallback to referrer detection for backward compatibility
    if (typeof window !== 'undefined') {
      const referrer = document.referrer;
      const currentHost = window.location.host;

      if (referrer && referrer.includes(currentHost)) {
        try {
          const referrerUrl = new URL(referrer);
          const referrerPath = referrerUrl.pathname;

          // If they came from the main routing setup page (exact match)
          if (referrerPath === '/routing-setup') {
            return '/routing-setup';
          }

          // If they came from the model configuration page
          if (referrerPath === `/my-models/${configId}`) {
            return `/my-models/${configId}`;
          }

          // If they came from the my-models list page
          if (referrerPath === '/my-models') {
            return `/my-models/${configId}`;
          }

          // If they came from any other my-models page, go to this config's page
          if (referrerPath.startsWith('/my-models/') && !referrerPath.includes('/routing-setup')) {
            return `/my-models/${configId}`;
          }
        } catch (e) {
          // Silently handle URL parsing errors
        }
      }
    }

    // Default fallback to model configuration page
    return `/my-models/${configId}`;
  };

  const getBackButtonText = () => {
    // First, check if we have a 'from' parameter in the URL
    const fromParam = searchParams.get('from');

    if (fromParam === 'routing-setup') {
      return 'Back to Routing Setup';
    }

    if (fromParam === 'model-config') {
      return 'Back to Configuration';
    }

    // Fallback to referrer detection
    if (typeof window !== 'undefined') {
      const referrer = document.referrer;
      const currentHost = window.location.host;

      if (referrer && referrer.includes(currentHost)) {
        try {
          const referrerUrl = new URL(referrer);
          const referrerPath = referrerUrl.pathname;

          if (referrerPath === '/routing-setup') {
            return 'Back to Routing Setup';
          }

          if (referrerPath === `/my-models/${configId}` || referrerPath === '/my-models' || referrerPath.startsWith('/my-models/')) {
            return 'Back to Configuration';
          }
        } catch (e) {
          // Silently handle URL parsing errors
        }
      }
    }

    // Default fallback text
    return 'Back to Configuration';
  };

  const handleBackNavigation = () => {
    if (typeof window !== 'undefined' && window.history.length > 1) {
      // Try to use browser back if there's history
      const backUrl = getBackUrl();
      const referrer = document.referrer;
      const currentHost = window.location.host;

      // If referrer is from our app, use browser back for better UX
      if (referrer && referrer.includes(currentHost)) {
        router.back();
        return;
      }
    }

    // Fallback to programmatic navigation
    router.push(getBackUrl());
  };

  const [configDetails, setConfigDetails] = useState<CustomApiConfig | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [showOptimisticLoading, setShowOptimisticLoading] = useState<boolean>(false);

  const [selectedStrategy, setSelectedStrategy] = useState<RoutingStrategyType>('none');
  const [strategyParams, setStrategyParams] = useState<any>({});

  const [availableApiKeys, setAvailableApiKeys] = useState<DisplayApiKey[]>([]);
  const [isLoadingApiKeys, setIsLoadingApiKeys] = useState<boolean>(false);

  // State specifically for strict_fallback strategy
  const [orderedFallbackKeys, setOrderedFallbackKeys] = useState<DisplayApiKey[]>([]);

  // New State for Complexity-Based Round-Robin
  const [selectedApiKeyForComplexity, setSelectedApiKeyForComplexity] = useState<string | null>(null);
  // Stores fetched assignments: { apiKeyId1: [1,2], apiKeyId2: [3] }
  const [complexityAssignments, setComplexityAssignments] = useState<Record<string, number[]>>({}); 
  // Levels selected in UI for the current selectedApiKeyForComplexity
  const [currentKeyComplexityLevels, setCurrentKeyComplexityLevels] = useState<number[]>([]); 
  const [isFetchingAssignments, setIsFetchingAssignments] = useState<boolean>(false);
  const [isSavingAssignments, setIsSavingAssignments] = useState<boolean>(false);
  const [assignmentsError, setAssignmentsError] = useState<string | null>(null);
  const [assignmentsSuccessMessage, setAssignmentsSuccessMessage] = useState<string | null>(null);

  // Temporarily disabled DnD sensors and handlers
  // const sensors = useSensors(
  //   useSensor(PointerSensor),
  //   useSensor(KeyboardSensor, {
  //     coordinateGetter: sortableKeyboardCoordinates,
  //   })
  // );

  // const handleDragEnd = (event: DragEndEvent) => {
  //   const { active, over } = event;

  //   if (active.id !== over?.id) {
  //     const oldIndex = orderedFallbackKeys.findIndex((key) => key.id === active.id);
  //     const newIndex = orderedFallbackKeys.findIndex((key) => key.id === over?.id);

  //     const newOrderedKeys = arrayMove(orderedFallbackKeys, oldIndex, newIndex);
  //     setOrderedFallbackKeys(newOrderedKeys);
  //     // Update strategyParams immediately for saving
  //     setStrategyParams({ ordered_api_key_ids: newOrderedKeys.map(k => k.id) });
  //   }
  // };

  const fetchConfigAndKeys = useCallback(async () => {
    if (!configId) {
      setError('Configuration ID is missing.');
      setIsLoading(false);
      return;
    }

    // Check for cached data first
    const cachedData = getCachedData(configId);
    if (cachedData && cachedData.configDetails && cachedData.apiKeys) {
      console.log(`⚡ [ROUTING SETUP] Using cached data for config: ${configId}`);

      setConfigDetails(cachedData.configDetails);
      setAvailableApiKeys(cachedData.apiKeys);

      const currentSelectedStrategy = (cachedData.routingStrategy as RoutingStrategyType) || 'none';
      setSelectedStrategy(currentSelectedStrategy);
      setStrategyParams(cachedData.routingParams || {});

      // Initialize orderedFallbackKeys based on cached data
      if (currentSelectedStrategy === 'strict_fallback' && cachedData.routingParams?.ordered_api_key_ids) {
        const savedOrder = cachedData.routingParams.ordered_api_key_ids as string[];
        const reorderedKeys = savedOrder.map(id => cachedData.apiKeys.find(k => k.id === id)).filter(Boolean) as DisplayApiKey[];
        const remainingKeys = cachedData.apiKeys.filter(k => !savedOrder.includes(k.id));
        setOrderedFallbackKeys([...reorderedKeys, ...remainingKeys]);
      } else {
        setOrderedFallbackKeys([...cachedData.apiKeys]);
      }

      setIsLoading(false);
      setIsLoadingApiKeys(false);
      return;
    }

    // Show optimistic loading for first-time visits
    if (!isCached(configId)) {
      setShowOptimisticLoading(true);
    }

    setIsLoading(true);
    setIsLoadingApiKeys(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // Fetch config details
      const configRes = await fetch(`/api/custom-configs/${configId}`);
      if (!configRes.ok) {
        const errData = await configRes.json();
        throw new Error(errData.error || 'Failed to fetch configuration');
      }
      const currentConfig: CustomApiConfig = await configRes.json();
      setConfigDetails(currentConfig);
      const currentSelectedStrategy = (currentConfig.routing_strategy as RoutingStrategyType) || 'none';
      setSelectedStrategy(currentSelectedStrategy);
      const currentStrategyParams = currentConfig.routing_strategy_params || {};
      setStrategyParams(currentStrategyParams); // General params state

      // Fetch API keys for this configuration
      const keysRes = await fetch(`/api/keys?custom_config_id=${configId}`);
      if (!keysRes.ok) {
        const errData = await keysRes.json();
        throw new Error(errData.error || 'Failed to fetch API keys for this configuration');
      }
      const keys: DisplayApiKey[] = await keysRes.json();
      setAvailableApiKeys(keys);

      // Initialize orderedFallbackKeys based on fetched keys and saved params
      if (currentSelectedStrategy === 'strict_fallback' && currentStrategyParams.ordered_api_key_ids) {
        const savedOrder = currentStrategyParams.ordered_api_key_ids as string[];
        const reorderedKeys = savedOrder.map(id => keys.find(k => k.id === id)).filter(Boolean) as DisplayApiKey[];
        // Add any keys present in `keys` but not in `savedOrder` to the end
        const remainingKeys = keys.filter(k => !savedOrder.includes(k.id));
        setOrderedFallbackKeys([...reorderedKeys, ...remainingKeys]);
      } else {
        setOrderedFallbackKeys([...keys]); // Default order or for other strategies initially
      }

    } catch (err: any) {
      setError(`Error loading data: ${err.message}`);
      setConfigDetails(null);
      setAvailableApiKeys([]);
    } finally {
      setIsLoading(false);
      setIsLoadingApiKeys(false);
      setShowOptimisticLoading(false);
    }
  }, [configId, getCachedData, isCached]);

  useEffect(() => {
    fetchConfigAndKeys();
  }, [fetchConfigAndKeys]);

  // Fetch complexity assignments for the selected API key
  const fetchComplexityAssignmentsForKey = useCallback(async (apiKeyId: string) => {
    if (!configId || !apiKeyId) return;
    setIsFetchingAssignments(true);
    setAssignmentsError(null);
    setAssignmentsSuccessMessage(null);
    try {
      const response = await fetch(`/api/custom-configs/${configId}/keys/${apiKeyId}/complexity-assignments`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch complexity assignments');
      }
      const fetchedLevels: number[] = await response.json();
      setComplexityAssignments(prev => ({ ...prev, [apiKeyId]: fetchedLevels }));
      setCurrentKeyComplexityLevels(fetchedLevels);
    } catch (err: any) {
      setAssignmentsError(`Error fetching assignments for key: ${err.message}`);
      setCurrentKeyComplexityLevels([]); // Reset on error
    } finally {
      setIsFetchingAssignments(false);
    }
  }, [configId]);

  // Effect to fetch assignments when selectedApiKeyForComplexity changes
  useEffect(() => {
    if (selectedApiKeyForComplexity) {
      fetchComplexityAssignmentsForKey(selectedApiKeyForComplexity);
    } else {
      // Clear levels if no key is selected
      setCurrentKeyComplexityLevels([]);
      setAssignmentsError(null);
    }
  }, [selectedApiKeyForComplexity, fetchComplexityAssignmentsForKey]);

  // Handle checkbox change for complexity levels
  const handleComplexityLevelChange = (level: number, checked: boolean) => {
    setCurrentKeyComplexityLevels(prevLevels => {
      if (checked) {
        return [...prevLevels, level].sort((a, b) => a - b);
      } else {
        return prevLevels.filter(l => l !== level);
      }
    });
  };

  // Save complexity assignments for the selected API key
  const handleSaveComplexityAssignments = useCallback(async () => {
    if (!configId || !selectedApiKeyForComplexity) {
      setAssignmentsError('No API key selected to save assignments for.');
      return;
    }
    setIsSavingAssignments(true);
    setAssignmentsError(null);
    setAssignmentsSuccessMessage(null);
    try {
      const response = await fetch(`/api/custom-configs/${configId}/keys/${selectedApiKeyForComplexity}/complexity-assignments`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ complexity_levels: currentKeyComplexityLevels }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save complexity assignments');
      }
      const result = await response.json();
      // Update the main store of assignments for this key
      setComplexityAssignments(prev => ({ ...prev, [selectedApiKeyForComplexity]: [...currentKeyComplexityLevels] }));
      setAssignmentsSuccessMessage(result.message || 'Complexity assignments saved successfully!');
    } catch (err: any) {
      setAssignmentsError(`Error saving assignments: ${err.message}`);
    } finally {
      setIsSavingAssignments(false);
    }
  }, [configId, selectedApiKeyForComplexity, currentKeyComplexityLevels]);

  const moveFallbackKey = (index: number, direction: 'up' | 'down') => {
    const newOrderedKeys = [...orderedFallbackKeys];
    const keyToMove = newOrderedKeys[index];
    if (direction === 'up' && index > 0) {
      newOrderedKeys.splice(index, 1);
      newOrderedKeys.splice(index - 1, 0, keyToMove);
    } else if (direction === 'down' && index < newOrderedKeys.length - 1) {
      newOrderedKeys.splice(index, 1);
      newOrderedKeys.splice(index + 1, 0, keyToMove);
    }
    setOrderedFallbackKeys(newOrderedKeys);
    // Update strategyParams immediately for saving
    setStrategyParams({ ordered_api_key_ids: newOrderedKeys.map(k => k.id) }); 
  };

  const handleSaveRoutingSettings = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!configId || !configDetails) {
      setError('Configuration details not loaded.');
      return;
    }
    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);

    let paramsToSave = strategyParams;
    if (selectedStrategy === 'strict_fallback') {
      paramsToSave = { ordered_api_key_ids: orderedFallbackKeys.map(k => k.id) };
    }
    // Add similar logic for other strategies to structure their paramsToSave

    try {
      const response = await fetch(`/api/custom-configs/${configId}/routing`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          routing_strategy: selectedStrategy,
          routing_strategy_params: paramsToSave, 
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save routing settings');
      }
      const result = await response.json();
      setSuccessMessage(result.message || 'Routing settings saved successfully!');
      setConfigDetails(prev => prev ? { ...prev, routing_strategy: selectedStrategy, routing_strategy_params: paramsToSave } : null);
      // Update local params to reflect saved state if necessary, e.g. if backend transforms it
      setStrategyParams(paramsToSave); 

    } catch (err: any) {
      setError(`Error saving settings: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const renderComplexityAssignmentUI = () => {
    if (selectedStrategy !== 'complexity_round_robin') return null;

    return (
      <div className="mt-8 pt-6 border-t border-gray-200">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">Complexity-Based Key Assignments</h3>
        <p className="text-sm text-gray-600 mb-6">
          Assign API keys to handle prompts of specific complexity levels (1-5). The system will classify incoming prompts and round-robin requests among keys assigned to that complexity.
        </p>

        {isLoadingApiKeys && (
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"></div>
            <p className="text-sm text-gray-600 ml-2">Loading API keys...</p>
          </div>
        )}
        {!isLoadingApiKeys && availableApiKeys.length === 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <p className="text-sm text-yellow-800">No API keys found for this configuration. Please add API keys first on the model configuration page.</p>
          </div>
        )}

        {availableApiKeys.length > 0 && (
          <div className="mb-6">
            <label htmlFor="apiKeyForComplexity" className="block text-sm font-medium text-gray-700 mb-2">Select API Key to Assign Complexities:</label>
            <select
              id="apiKeyForComplexity"
              value={selectedApiKeyForComplexity || ''}
              onChange={(e) => setSelectedApiKeyForComplexity(e.target.value || null)}
              className="form-select max-w-md"
            >
              <option value="" disabled>-- Select an API Key --</option>
              {availableApiKeys.map(key => (
                <option key={key.id} value={key.id}>{key.label} ({key.provider} - {key.predefined_model_id})</option>
              ))}
            </select>
          </div>
        )}

        {selectedApiKeyForComplexity && (
          <div className="card p-6">
            <h4 className="text-md font-medium text-gray-900 mb-4">Assign Complexity Levels for: <span className="text-orange-600">{availableApiKeys.find(k => k.id === selectedApiKeyForComplexity)?.label}</span></h4>

            {isFetchingAssignments && (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"></div>
                <p className="text-sm text-gray-600 ml-2">Loading current assignments...</p>
              </div>
            )}

            {assignmentsError && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                <p className="text-red-800 text-sm">{assignmentsError}</p>
              </div>
            )}

            {assignmentsSuccessMessage && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                <p className="text-green-800 text-sm">{assignmentsSuccessMessage}</p>
              </div>
            )}

            {!isFetchingAssignments && (
              <div className="space-y-3 mb-6">
                {[1, 2, 3, 4, 5].map(level => (
                  <label key={level} className="flex items-center space-x-3 p-3 bg-gray-50 border border-gray-200 rounded-lg hover:border-gray-300 cursor-pointer transition-colors duration-200">
                    <input
                      type="checkbox"
                      checked={currentKeyComplexityLevels.includes(level)}
                      onChange={(e) => handleComplexityLevelChange(level, e.target.checked)}
                      className="h-4 w-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500 focus:ring-2"
                    />
                    <span className="text-sm font-medium text-gray-900">Complexity Level {level}</span>
                  </label>
                ))}
              </div>
            )}

            <button
              onClick={handleSaveComplexityAssignments}
              disabled={isSavingAssignments || isFetchingAssignments}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSavingAssignments ? 'Saving Assignments...' : 'Save Assignments for this Key'}
            </button>
          </div>
        )}
      </div>
    );
  };

  // Render configuration content based on selected strategy
  const renderConfigurationContent = () => {
    const selectedStrategyData = ROUTING_STRATEGIES.find(s => s.id === selectedStrategy);

    if (selectedStrategy === 'none') {
      return (
        <div className="text-center py-16">
          <div className="w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Cog6ToothIcon className="w-10 h-10 text-orange-600" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-4">Default Behavior</h3>
          <p className="text-gray-600 max-w-md mx-auto leading-relaxed">
            {selectedStrategyData?.description}
          </p>
          <div className="mt-8 p-4 bg-green-50 border border-green-200 rounded-xl">
            <div className="flex items-center justify-center space-x-2">
              <CheckCircleIcon className="w-5 h-5 text-green-600" />
              <span className="text-green-800 font-medium">No additional setup required</span>
            </div>
          </div>

          {/* Save Button for Default */}
          <div className="mt-12 pt-6 border-t border-gray-200">
            <button
              type="submit"
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <svg className="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Saving...
                </>
              ) : (
                <>
                  <CheckCircleIcon className="w-4 h-4 mr-2" />
                  Save Routing Settings
                </>
              )}
            </button>
          </div>
        </div>
      );
    }

    if (selectedStrategy === 'intelligent_role') {
      return (
        <div>
          <div className="mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <BoltIcon className="w-7 h-7 mr-3 text-orange-600" />
              Intelligent Role Routing
            </h3>
            <p className="text-gray-600 leading-relaxed">
              {selectedStrategyData?.description}
            </p>
          </div>

          <div className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
              <h4 className="font-semibold text-blue-900 mb-3">How it works:</h4>
              <div className="space-y-3 text-sm text-blue-800">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0">
                    <span className="text-xs font-bold text-blue-600">1</span>
                  </div>
                  <p>System analyzes your prompt to understand the main task</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0">
                    <span className="text-xs font-bold text-blue-600">2</span>
                  </div>
                  <p>Matches task to relevant RoKey operational roles (e.g., 'Coding - Frontend', 'Copywriting')</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0">
                    <span className="text-xs font-bold text-blue-600">3</span>
                  </div>
                  <p>Routes to assigned API key or falls back to 'Default General Chat Model'</p>
                </div>
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded-xl p-6">
              <div className="flex items-start space-x-3">
                <CheckCircleIcon className="w-6 h-6 text-green-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="font-semibold text-green-900 mb-2">Ready to use!</h4>
                  <p className="text-sm text-green-800 leading-relaxed">
                    No additional setup required. Future enhancements may allow further customization.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="mt-12 pt-6 border-t border-gray-200 flex justify-end">
            <button
              type="submit"
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <svg className="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Saving...
                </>
              ) : (
                <>
                  <CheckCircleIcon className="w-4 h-4 mr-2" />
                  Save Routing Settings
                </>
              )}
            </button>
          </div>
        </div>
      );
    }

    if (selectedStrategy === 'strict_fallback') {
      return (
        <div>
          <div className="mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <ListBulletIcon className="w-7 h-7 mr-3 text-orange-600" />
              Strict Fallback Configuration
            </h3>
            <p className="text-gray-600 leading-relaxed">
              {selectedStrategyData?.description}
            </p>
          </div>

          {isLoadingApiKeys && (
            <div className="flex items-center justify-center py-12">
              <div className="w-8 h-8 border-2 border-orange-600/20 border-t-orange-600 rounded-full animate-spin"></div>
              <p className="text-gray-600 ml-3">Loading API keys...</p>
            </div>
          )}

          {!isLoadingApiKeys && availableApiKeys.length === 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-8 text-center">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-yellow-900 mb-2">No API Keys Found</h4>
              <p className="text-yellow-800 leading-relaxed">
                Please add API keys on the main configuration page to set up fallback order.
              </p>
            </div>
          )}

          {!isLoadingApiKeys && availableApiKeys.length > 0 && (
            <div className="space-y-6">
              <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0">
                    <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <p className="text-sm text-blue-800 leading-relaxed">
                    Drag and drop to arrange the API keys in the desired order of execution. The router will try the first key, then the second if the first fails, and so on.
                  </p>
                </div>
              </div>

              {/* Temporarily using simple list instead of DnD */}
              <ul className="space-y-3">
                {orderedFallbackKeys.map((apiKey, index) => (
                  <SimpleApiKeyItem
                    key={apiKey.id}
                    apiKey={apiKey}
                    index={index}
                    onMoveUp={index > 0 ? () => moveFallbackKey(index, 'up') : undefined}
                    onMoveDown={index < orderedFallbackKeys.length - 1 ? () => moveFallbackKey(index, 'down') : undefined}
                  />
                ))}
              </ul>
            </div>
          )}

          {/* Save Button */}
          <div className="mt-12 pt-6 border-t border-gray-200 flex justify-end">
            <button
              type="submit"
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isLoading || availableApiKeys.length === 0}
            >
              {isLoading ? (
                <>
                  <svg className="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Saving...
                </>
              ) : (
                <>
                  <CheckCircleIcon className="w-4 h-4 mr-2" />
                  Save Routing Settings
                </>
              )}
            </button>
          </div>
        </div>
      );
    }

    if (selectedStrategy === 'complexity_round_robin') {
      return (
        <div>
          <div className="mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
              <CircleStackIcon className="w-7 h-7 mr-3 text-orange-600" />
              Complexity-Based Round-Robin
            </h3>
            <p className="text-gray-600 leading-relaxed">
              {selectedStrategyData?.description}
            </p>
          </div>

          {renderComplexityAssignmentUI()}

          {/* Save Button */}
          <div className="mt-12 pt-6 border-t border-gray-200 flex justify-end">
            <button
              type="submit"
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <svg className="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Saving...
                </>
              ) : (
                <>
                  <CheckCircleIcon className="w-4 h-4 mr-2" />
                  Save Routing Settings
                </>
              )}
            </button>
          </div>
        </div>
      );
    }

    return null;
  };

  // Main render logic with optimistic loading
  if (showOptimisticLoading && !isCached(configId)) {
    return <RoutingSetupLoadingSkeleton />;
  }

  if (isLoading && !configDetails) { // Initial full load
    return <CompactRoutingSetupLoadingSkeleton />;
  }

  if (error && !configDetails && !isLoading) { // Critical error, cannot load config
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">Routing Setup Error</h1>
        <div className="card border-red-200 bg-red-50 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            <p className="text-red-800">{error}</p>
          </div>
          <Link href="/my-models" className="mt-4 btn-primary inline-block">
            Back to My Models
          </Link>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-cream">
      <div className="container mx-auto px-6 py-8">
        {/* Clean Header Section */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBackNavigation}
                className="btn-secondary inline-flex items-center text-sm"
              >
                <ArrowLeftIcon className="w-4 h-4 mr-2" />
                {getBackButtonText()}
              </button>
            </div>
            <div className="text-right">
              <h1 className="text-h1 text-gray-900">
                Advanced Routing Setup
              </h1>
              {configDetails && (
                <p className="text-body-sm text-gray-600 mt-1">
                  Configuration: <span className="text-orange-600 font-semibold">{configDetails.name}</span>
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Status Messages */}
        {(error && !successMessage) && (
          <div className="card border-red-200 bg-red-50 p-6 mb-8 max-w-4xl mx-auto">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-red-800">Configuration Error</h3>
                <p className="text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {successMessage && (
          <div className="card border-green-200 bg-green-50 p-6 mb-8 max-w-4xl mx-auto">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <h3 className="font-semibold text-green-800">Settings Saved</h3>
                <p className="text-green-700 mt-1">{successMessage}</p>
              </div>
            </div>
          </div>
        )}

        {/* Main Two-Panel Layout */}
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Panel - Strategy Cards */}
            <div className="lg:col-span-1">
              <div className="card p-6 sticky top-8">
                <h2 className="text-xl font-bold text-gray-900 mb-6">Routing Strategy</h2>
                <div className="space-y-3">
                  {ROUTING_STRATEGIES.map((strategy) => {
                    const IconComponent = strategy.icon;
                    const isSelected = selectedStrategy === strategy.id;

                    return (
                      <button
                        key={strategy.id}
                        onClick={() => {
                          setSelectedStrategy(strategy.id);
                          // Reset/initialize params based on new strategy
                          if (strategy.id === 'strict_fallback') {
                            const existingFallbackParams = strategyParams.ordered_api_key_ids as string[];
                            if (existingFallbackParams && Array.isArray(existingFallbackParams)) {
                              const reordered = existingFallbackParams.map(id => availableApiKeys.find(k => k.id === id)).filter(Boolean) as DisplayApiKey[];
                              const remaining = availableApiKeys.filter(k => !existingFallbackParams.includes(k.id));
                              setOrderedFallbackKeys([...reordered, ...remaining]);
                            } else {
                              setOrderedFallbackKeys([...availableApiKeys]);
                            }
                            setStrategyParams({ ordered_api_key_ids: orderedFallbackKeys.map(k => k.id) });
                          } else {
                            setStrategyParams({});
                            setOrderedFallbackKeys([...availableApiKeys]);
                          }
                          // Reset complexity assignment states if strategy changes
                          setSelectedApiKeyForComplexity(null);
                          setCurrentKeyComplexityLevels([]);
                          setAssignmentsError(null);
                          setAssignmentsSuccessMessage(null);
                        }}
                        disabled={isLoading}
                        className={`w-full text-left p-4 rounded-xl border-2 transition-all duration-300 group ${
                          isSelected
                            ? 'border-orange-500 bg-orange-50 shadow-lg transform scale-[1.02]'
                            : 'border-gray-200 bg-white hover:border-orange-300 hover:bg-orange-25 hover:shadow-md'
                        }`}
                      >
                        <div className="flex items-start space-x-4">
                          <div className={`p-3 rounded-lg transition-colors duration-300 ${
                            isSelected
                              ? 'bg-orange-100 text-orange-600'
                              : 'bg-gray-100 text-gray-600 group-hover:bg-orange-100 group-hover:text-orange-600'
                          }`}>
                            <IconComponent className="w-6 h-6" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-1">
                              <h3 className={`font-semibold text-sm transition-colors duration-300 ${
                                isSelected ? 'text-orange-900' : 'text-gray-900'
                              }`}>
                                {strategy.name}
                              </h3>
                              {isSelected && (
                                <CheckCircleIcon className="w-4 h-4 text-orange-600 animate-in fade-in duration-300" />
                              )}
                            </div>
                            <p className={`text-xs leading-relaxed transition-colors duration-300 ${
                              isSelected ? 'text-orange-700' : 'text-gray-600'
                            }`}>
                              {strategy.shortDescription}
                            </p>
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Right Panel - Configuration Options */}
            <div className="lg:col-span-2">
              <form onSubmit={handleSaveRoutingSettings}>
                <div className="card p-8 min-h-[600px]">
                  {/* Configuration Content with Animation */}
                  <div className="animate-in fade-in slide-in-from-right-4 duration-500">
                    {renderConfigurationContent()}
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}