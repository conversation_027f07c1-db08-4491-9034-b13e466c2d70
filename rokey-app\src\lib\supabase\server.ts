import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';

// This is the standard setup for creating a Supabase server client
// in Next.js App Router (Server Components, Route Handlers, Server Actions).
// Updated for Next.js 15 async cookies requirement
export async function createSupabaseServerClientOnRequest() {
  const cookieStore = await cookies();

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          try {
            cookieStore.set({ name, value, ...options });
          } catch (error) {
            // This error can be ignored if running in a Server Component
            // where cookies can't be set directly. Cookie setting should be
            // handled in Server Actions or Route Handlers.
            console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);
          }
        },
        remove(name: string, options: CookieOptions) {
          try {
            // To remove a cookie using the `set` method from `next/headers`,
            // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.
            cookieStore.set({ name, value: '', ...options });
          } catch (error) {
            // Similar to set, this might fail in a Server Component.
            console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);
          }
        },
      },
    }
  );
}

// Alternative method for API routes that need to handle cookies from request
export function createSupabaseServerClientFromRequest(request: NextRequest) {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          // In API routes, we can't set cookies directly on the request
          // This will be handled by the response
        },
        remove(name: string, options: CookieOptions) {
          // In API routes, we can't remove cookies directly on the request
          // This will be handled by the response
        },
      },
    }
  );
}
