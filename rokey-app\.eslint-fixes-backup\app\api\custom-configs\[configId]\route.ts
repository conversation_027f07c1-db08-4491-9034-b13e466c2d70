import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';

interface RouteParams {
  params: Promise<{
    configId: string; // Changed from id to configId
  }>;
}

// DELETE /api/custom-configs/:configId
// Deletes a specific custom API configuration
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  const supabase = await createSupabaseServerClientOnRequest();
  const { configId } = await params; // Use (await params).configId

  if (!configId) {
    return NextResponse.json({ error: 'Configuration ID is required' }, { status: 400 });
  }

  // TODO: Add user authentication check in Milestone 13
  // const { data: { user } } = await supabase.auth.getUser();
  // if (!user) {
  //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  // }

  try {
    // Optional: Check if there are any api_keys associated with this config before deleting.
    // If you want to prevent deletion if keys exist, you'd query `api_keys` table first.
    // For now, relying on `ON DELETE CASCADE` on the `fk_custom_api_config` foreign key
    // in `api_keys` table, which will delete associated keys.
    // If you change the FK to `ON DELETE RESTRICT`, this delete will fail if keys exist.

    const { error, count } = await supabase
      .from('custom_api_configs')
      .delete({ count: 'exact' })
      .eq('id', configId);
      // .eq('user_id', user.id); // Add this line in Milestone 13 to ensure user owns the config

    if (error) {
      console.error('Supabase error deleting custom config:', error);
      if (error.code === '23503') { // foreign_key_violation
        return NextResponse.json(
          { 
            error: 'Failed to delete custom API configuration because it is still in use.',
            details: 'Ensure no API keys are associated with this configuration before deleting.'
          },
          { status: 409 } // Conflict
        );
      }
      return NextResponse.json({ error: 'Failed to delete custom API configuration', details: error.message }, { status: 500 });
    }

    if (count === 0) {
      return NextResponse.json({ error: 'Custom API configuration not found or already deleted.' }, { status: 404 });
    }

    return NextResponse.json({ message: 'Custom API configuration deleted successfully' }, { status: 200 });

  } catch (e: any) {
    console.error('Error in DELETE /api/custom-configs/[configId]:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
} 

export async function GET(request: NextRequest, { params }: RouteParams) {
  const supabase = await createSupabaseServerClientOnRequest();
  const { configId } = await params;

  // Basic auth - ensure this is robust in a real app (e.g., user owns the config)
  // For now, just checking if a user session exists. Milestone 11 will enhance this.
  // const { data: { user }, error: userError } = await supabase.auth.getUser();
  // if (userError || !user) {
  //   return NextResponse.json({ error: 'Unauthorized: You must be logged in to view this configuration.' }, { status: 401 });
  // }

  if (!configId || typeof configId !== 'string') { // Basic validation for configId
    return NextResponse.json({ error: 'Invalid configuration ID provided.' }, { status: 400 });
  }

  try {
    const { data: configData, error: fetchError } = await supabase
      .from('custom_api_configs')
      .select('id, name, created_at, updated_at, user_id, routing_strategy, routing_strategy_params')
      .eq('id', configId)
      // .eq('user_id', user.id) // Ensure the authenticated user owns this config --- TEMPORARILY COMMENTED OUT FOR TESTING
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') { // No rows found
        return NextResponse.json({ error: 'Configuration not found or you do not have permission to view it.' }, { status: 404 });
      }
      console.error('Supabase error fetching single custom config:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch custom API configuration.', details: fetchError.message }, { status: 500 });
    }

    if (!configData) {
        // This case should ideally be covered by PGRST116, but as a safeguard:
        return NextResponse.json({ error: 'Configuration not found.' }, { status: 404 });
    }

    return NextResponse.json(configData, { status: 200 });

  } catch (e: any) {
    console.error('Error in GET /api/custom-configs/[configId]:', e);
    return NextResponse.json({ error: 'An unexpected server error occurred.', details: e.message }, { status: 500 });
  }
}

// Optional: OPTIONS handler for CORS preflight if your frontend is on a different domain/port during dev
export async function OPTIONS() {
  return NextResponse.json({}, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*', // Adjust for production
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}

// Note: If you later need DELETE or PUT for the entire config object (not just routing part),
// you can add those handlers to this file as well. 