// Types for chat history functionality

export interface ChatConversation {
  id: string;
  user_id?: string;
  custom_api_config_id: string;
  title: string;
  created_at: string;
  updated_at: string;
  message_count?: number; // Computed field
  last_message_preview?: string; // Computed field
}

export interface ChatMessageContentPart {
  type: 'text' | 'image_url';
  text?: string;
  image_url?: {
    url: string;
  };
}

export interface ChatMessage {
  id: string;
  conversation_id: string;
  role: 'user' | 'assistant' | 'system' | 'error';
  content: ChatMessageContentPart[];
  api_key_id?: string;
  model_used?: string;
  temperature_used?: number;
  tokens_prompt?: number;
  tokens_completion?: number;
  cost?: number;
  created_at: string;
}

export interface NewChatConversation {
  custom_api_config_id: string;
  title: string;
}

export interface NewChatMessage {
  conversation_id: string;
  role: 'user' | 'assistant' | 'system' | 'error';
  content: ChatMessageContentPart[];
  api_key_id?: string;
  model_used?: string;
  temperature_used?: number;
  tokens_prompt?: number;
  tokens_completion?: number;
  cost?: number;
}

// For the playground interface
export interface PlaygroundChatHistory {
  conversations: ChatConversation[];
  currentConversation?: ChatConversation;
  messages: ChatMessage[];
}
