import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';

interface ClassifyRolesRequest {
  task: string;
  available_roles: string[];
  user_config: Record<string, any>;
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClientFromRequest(request);
    
    // Get authenticated user from session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body: ClassifyRolesRequest = await request.json();
    const { task, available_roles, user_config } = body;

    if (!task || !available_roles || !Array.isArray(available_roles)) {
      return NextResponse.json({ 
        error: 'Invalid request: task and available_roles are required' 
      }, { status: 400 });
    }

    // Get classification API key
    const classificationApiKey = process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;
    if (!classificationApiKey) {
      return NextResponse.json({ 
        error: 'Classification service unavailable' 
      }, { status: 503 });
    }

    // Use Gemini to classify which roles should handle this task
    const systemPrompt = `You are RouKey's intelligent role classifier for browser automation tasks.

Available Roles: ${available_roles.join(', ')}

Analyze the task and determine which role(s) are most appropriate. Consider:
- Task complexity and requirements
- Role specializations and capabilities
- Multi-role coordination if needed

Respond with JSON: {"roles": ["role1", "role2"], "reasoning": "explanation"}

If multiple roles are needed, list them in order of importance.
If no specific role matches, use the first available role.`;

    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/openai/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${classificationApiKey}`,
      },
      body: JSON.stringify({
        model: 'gemini-2.0-flash-lite',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `Task: ${task}` }
        ],
        temperature: 0.1,
        max_tokens: 200
      })
    });

    if (!response.ok) {
      console.error('Gemini API error:', response.status);
      return NextResponse.json({ 
        error: 'Role classification failed' 
      }, { status: 500 });
    }

    const result = await response.json();
    const content = result.choices?.[0]?.message?.content;
    
    if (!content) {
      return NextResponse.json({ 
        error: 'Invalid classification response' 
      }, { status: 500 });
    }

    try {
      const parsed = JSON.parse(content);
      const classifiedRoles = parsed.roles || [];
      
      // Ensure classified roles are in available roles
      const validRoles = classifiedRoles.filter((role: string) => 
        available_roles.includes(role)
      );
      
      // If no valid roles, use first available role as fallback
      if (validRoles.length === 0 && available_roles.length > 0) {
        validRoles.push(available_roles[0]);
      }

      return NextResponse.json({
        roles: validRoles,
        reasoning: parsed.reasoning || 'Gemini-based role classification',
        classification_method: 'gemini',
        available_roles: available_roles,
        task_summary: task.substring(0, 100)
      });

    } catch (parseError) {
      console.error('Failed to parse Gemini response:', parseError);
      
      // Fallback: return first available role
      return NextResponse.json({
        roles: available_roles.length > 0 ? [available_roles[0]] : [],
        reasoning: 'Fallback classification due to parsing error',
        classification_method: 'fallback',
        available_roles: available_roles,
        task_summary: task.substring(0, 100)
      });
    }

  } catch (error) {
    console.error('Role classification error:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
