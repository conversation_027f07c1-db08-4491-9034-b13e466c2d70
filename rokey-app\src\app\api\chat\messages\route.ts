import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { type NewChatMessage, type ChatMessage } from '@/types/chatHistory';

// GET /api/chat/messages?conversation_id=<ID>&limit=<NUM>&offset=<NUM>&latest=<BOOL>
// Retrieves messages for a specific conversation with pagination
export async function GET(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  const { searchParams } = new URL(request.url);
  const conversationId = searchParams.get('conversation_id');
  const limit = parseInt(searchParams.get('limit') || '25'); // Phase 1 Optimization: Reduced from 50 to 25 messages
  const offset = parseInt(searchParams.get('offset') || '0');
  const latest = searchParams.get('latest') === 'true'; // Get latest messages first

  if (!conversationId) {
    return NextResponse.json({ error: 'conversation_id query parameter is required' }, { status: 400 });
  }

  // Phase 1 Optimization: Reduced max limit validation
  if (limit > 50) {
    return NextResponse.json({ error: 'Limit cannot exceed 50 messages' }, { status: 400 });
  }

  try {
    let query = supabase
      .from('chat_messages')
      .select('*')
      .eq('conversation_id', conversationId);

    if (latest) {
      // Get latest messages first (for initial load)
      query = query
        .order('created_at', { ascending: false })
        .limit(limit);
    } else {
      // Get messages with pagination (for loading more)
      query = query
        .order('created_at', { ascending: true })
        .range(offset, offset + limit - 1);
    }

    const { data: messages, error } = await query;

    if (error) {
      console.error('Supabase error fetching messages:', error);
      return NextResponse.json({ error: 'Failed to fetch messages', details: error.message }, { status: 500 });
    }

    // If getting latest messages, reverse to chronological order
    const orderedMessages = latest && messages ? messages.reverse() : messages;

    // Add cache headers for better performance
    const response = NextResponse.json(orderedMessages || [], { status: 200 });
    response.headers.set('Cache-Control', 'private, max-age=60'); // Cache for 1 minute

    return response;

  } catch (e: any) {
    console.error('Error in GET /api/chat/messages:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}

// POST /api/chat/messages
// Creates a new chat message
export async function POST(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  try {
    const messageData = await request.json() as NewChatMessage;
    const { 
      conversation_id, 
      role, 
      content, 
      api_key_id, 
      model_used, 
      temperature_used, 
      tokens_prompt, 
      tokens_completion, 
      cost 
    } = messageData;

    if (!conversation_id || !role || !content) {
      return NextResponse.json({ error: 'Missing required fields: conversation_id, role, content' }, { status: 400 });
    }

    // Validate role
    if (!['user', 'assistant', 'system', 'error'].includes(role)) {
      return NextResponse.json({ error: 'Invalid role. Must be one of: user, assistant, system, error' }, { status: 400 });
    }

    // Validate content structure
    if (!Array.isArray(content) || content.length === 0) {
      return NextResponse.json({ error: 'Content must be a non-empty array' }, { status: 400 });
    }

    const { data, error } = await supabase
      .from('chat_messages')
      .insert({
        conversation_id,
        role,
        content,
        api_key_id,
        model_used,
        temperature_used,
        tokens_prompt,
        tokens_completion,
        cost,
      })
      .select()
      .single();

    if (error) {
      console.error('Supabase error creating message:', error);
      return NextResponse.json({ error: 'Failed to create message', details: error.message }, { status: 500 });
    }

    // Update conversation's updated_at timestamp
    await supabase
      .from('chat_conversations')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', conversation_id);

    return NextResponse.json(data, { status: 201 });

  } catch (e: any) {
    console.error('Error in POST /api/chat/messages:', e);
    if (e.name === 'SyntaxError') {
      return NextResponse.json({ error: 'Invalid request body: Malformed JSON.' }, { status: 400 });
    }
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}

// PUT /api/chat/messages?id=<ID>
// Updates a specific message's content
export async function PUT(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  const { searchParams } = new URL(request.url);
  const messageId = searchParams.get('id');

  if (!messageId) {
    return NextResponse.json({ error: 'id query parameter is required' }, { status: 400 });
  }

  try {
    const updateData = await request.json();
    const { content } = updateData;

    if (!content) {
      return NextResponse.json({ error: 'content field is required' }, { status: 400 });
    }

    // Validate content structure
    if (!Array.isArray(content) || content.length === 0) {
      return NextResponse.json({ error: 'Content must be a non-empty array' }, { status: 400 });
    }

    const { data, error } = await supabase
      .from('chat_messages')
      .update({ content })
      .eq('id', messageId)
      .select()
      .single();

    if (error) {
      console.error('Supabase error updating message:', error);
      return NextResponse.json({ error: 'Failed to update message', details: error.message }, { status: 500 });
    }

    return NextResponse.json(data, { status: 200 });

  } catch (e: any) {
    console.error('Error in PUT /api/chat/messages:', e);
    if (e.name === 'SyntaxError') {
      return NextResponse.json({ error: 'Invalid request body: Malformed JSON.' }, { status: 400 });
    }
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}

// DELETE /api/chat/messages?id=<ID>
// Deletes a specific message
export async function DELETE(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  const { searchParams } = new URL(request.url);
  const messageId = searchParams.get('id');

  if (!messageId) {
    return NextResponse.json({ error: 'id query parameter is required' }, { status: 400 });
  }

  try {
    const { error } = await supabase
      .from('chat_messages')
      .delete()
      .eq('id', messageId);

    if (error) {
      console.error('Supabase error deleting message:', error);
      return NextResponse.json({ error: 'Failed to delete message', details: error.message }, { status: 500 });
    }

    return NextResponse.json({ message: 'Message deleted successfully' }, { status: 200 });

  } catch (e: any) {
    console.error('Error in DELETE /api/chat/messages:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}
