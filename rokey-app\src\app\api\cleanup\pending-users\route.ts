import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(req: NextRequest) {
  try {
    // Get users with payment_status: 'pending' that are older than 1 hour
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    
    const { data: pendingUsers, error: queryError } = await supabase
      .from('auth.users')
      .select('id, email, created_at, raw_user_meta_data')
      .lt('created_at', oneHourAgo);

    if (queryError) {
      console.error('Error querying pending users:', queryError);
      return NextResponse.json({ error: 'Failed to query users' }, { status: 500 });
    }

    // Filter users with payment_status: 'pending'
    const usersToDelete = pendingUsers?.filter(user => 
      user.raw_user_meta_data?.payment_status === 'pending'
    ) || [];

    console.log(`Found ${usersToDelete.length} pending users to clean up`);

    // Delete each pending user
    const deletedUsers = [];
    for (const user of usersToDelete) {
      try {
        const { error: deleteError } = await supabase.auth.admin.deleteUser(user.id);
        
        if (deleteError) {
          console.error(`Failed to delete user ${user.email}:`, deleteError);
        } else {
          console.log(`Deleted pending user: ${user.email}`);
          deletedUsers.push(user.email);
        }
      } catch (error) {
        console.error(`Error deleting user ${user.email}:`, error);
      }
    }

    return NextResponse.json({
      success: true,
      message: `Cleaned up ${deletedUsers.length} pending users`,
      deletedUsers
    });

  } catch (error) {
    console.error('Cleanup error:', error);
    return NextResponse.json({ error: 'Cleanup failed' }, { status: 500 });
  }
}

// Manual cleanup endpoint for testing
export async function GET() {
  return NextResponse.json({
    message: 'Pending user cleanup endpoint. Use POST to run cleanup.',
    usage: 'POST /api/cleanup/pending-users'
  });
}
