// Test script to verify RAG search improvements
// Run with: node test-rag-search.js

const testQueries = [
  "who is ok<PERSON> david?",
  "what is david's background?", 
  "tell me about david chuk<PERSON>",
  "david's experience",
  "what does david do?",
  "david's skills and expertise"
];

async function testSearch(query) {
  try {
    console.log(`\n🔍 Testing query: "${query}"`);
    console.log('=' .repeat(50));
    
    const response = await fetch('http://localhost:3000/api/documents/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // You'll need to add proper auth headers here
      },
      body: JSON.stringify({
        query: query,
        configId: 'your-config-id', // Replace with actual config ID
        limit: 8,
        threshold: 0.5
      })
    });
    
    const data = await response.json();
    
    if (data.success && data.results.length > 0) {
      console.log(`✅ Found ${data.results.length} results:`);
      data.results.forEach((result, index) => {
        console.log(`\n📄 Result ${index + 1}:`);
        console.log(`   Similarity: ${result.similarity.toFixed(3)}`);
        console.log(`   Content: "${result.content.substring(0, 200)}..."`);
      });
    } else {
      console.log('❌ No results found');
    }
    
  } catch (error) {
    console.error('❌ Error testing search:', error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting RAG Search Tests');
  console.log('Testing improved search parameters...\n');
  
  for (const query of testQueries) {
    await testSearch(query);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests
  }
  
  console.log('\n✅ All tests completed!');
}

// Run the tests
runTests().catch(console.error);
