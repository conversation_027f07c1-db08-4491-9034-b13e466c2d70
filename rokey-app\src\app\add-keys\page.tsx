'use client';

import { useState, useEffect, FormEvent } from 'react';
import { type ApiKeyProvider, type NewA<PERSON><PERSON>ey, type Display<PERSON>pi<PERSON>ey } from '@/types/apiKeys';
import { llmProviders, type Model as ConfigModel } from '@/config/models';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import { useConfirmation } from '@/hooks/useConfirmation';

const PROVIDER_OPTIONS = llmProviders.map(p => ({ value: p.id, label: p.name }));

const getModelOptionsForProvider = (providerId: string): { value: string, label: string }[] => {
  const provider = llmProviders.find(p => p.id === providerId);
  return provider ? provider.models.map(m => ({ value: m.id, label: m.name })) : [];
};

export default function AddKeysPage() {
  // Confirmation modal hook
  const confirmation = useConfirmation();

  const [provider, setProvider] = useState<string>(PROVIDER_OPTIONS[0]?.value || 'openai');
  const [modelId, setModelId] = useState<string>('');
  const [apiKeyRaw, setApiKeyRaw] = useState<string>('');
  const [label, setLabel] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const [savedKeys, setSavedKeys] = useState<DisplayApiKey[]>([]);
  const [isLoadingKeys, setIsLoadingKeys] = useState<boolean>(true);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  const fetchKeys = async () => {
    setIsLoadingKeys(true);
    let fetchError = null;
    try {
      const response = await fetch('/api/keys');
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch keys');
      }
      const keys: DisplayApiKey[] = await response.json();
      setSavedKeys(keys);
      setError(null);
    } catch (err: any) {
      fetchError = `Error fetching keys: ${err.message}`;
      setError(fetchError); 
    }
    setIsLoadingKeys(false);
  };

  useEffect(() => {
    fetchKeys();
  }, []);

  useEffect(() => {
    const currentModels = getModelOptionsForProvider(provider);
    setModelId(currentModels[0]?.value || '');
  }, [provider]);

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);

    const newKeyData: NewApiKey = {
      provider,
      predefined_model_id: modelId,
      api_key_raw: apiKeyRaw,
      label,
      custom_api_config_id: '',
    };

    try {
      const response = await fetch('/api/keys', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newKeyData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.details || result.error || 'Failed to save API key');
      }

      setSuccessMessage(`API key "${label}" saved successfully!`);
      setProvider(PROVIDER_OPTIONS[0]?.value || 'openai');
      setModelId('');
      setApiKeyRaw('');
      setLabel('');
      await fetchKeys(); 
    } catch (err: any) {
      setError(err.message);
      setSuccessMessage(null);
    }
    setIsLoading(false);
  };

  const handleDeleteKey = (keyId: string, keyLabel: string) => {
    confirmation.showConfirmation(
      {
        title: 'Delete API Key',
        message: `Are you sure you want to delete the API key "${keyLabel}"? This action cannot be undone.`,
        confirmText: 'Delete API Key',
        cancelText: 'Cancel',
        type: 'danger'
      },
      async () => {
        setIsDeleting(keyId);
        setError(null);
        setSuccessMessage(null);

        try {
          const response = await fetch(`/api/keys/${keyId}`, {
            method: 'DELETE',
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.details || result.error || 'Failed to delete API key');
          }

          setSuccessMessage(`API key "${keyLabel}" deleted successfully!`);
          setSavedKeys(prevKeys => prevKeys.filter(key => key.id !== keyId));
        } catch (err: any) {
          setError(err.message);
          setSuccessMessage(null);
          throw err; // Re-throw to keep modal open on error
        } finally {
          setIsDeleting(null);
        }
      }
    );
  };

  const currentModelOptions = getModelOptionsForProvider(provider);

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold mb-6">Add API Key</h1>
        <form onSubmit={handleSubmit} className="space-y-6 bg-gray-800 p-6 rounded-lg shadow-xl max-w-lg">
          {error && <p className="text-red-400 bg-red-900/50 p-3 rounded-md">Error: {error}</p>}
          {successMessage && <p className="text-green-400 bg-green-900/50 p-3 rounded-md">{successMessage}</p>}

          <div>
            <label htmlFor="provider" className="block text-sm font-medium text-gray-300 mb-1">Provider</label>
            <select
              id="provider"
              value={provider}
              onChange={(e) => setProvider(e.target.value)}
              className="w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500"
            >
              {PROVIDER_OPTIONS.map(p => (
                <option key={p.value} value={p.value}>{p.label}</option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="modelId" className="block text-sm font-medium text-gray-300 mb-1">Model ID</label>
            <select
              id="modelId"
              value={modelId}
              onChange={(e) => setModelId(e.target.value)}
              disabled={!currentModelOptions.length}
              className="w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
            >
              {currentModelOptions.length > 0 ? (
                currentModelOptions.map(m => (
                  <option key={m.value} value={m.value}>{m.label}</option>
                ))
              ) : (
                <option value="" disabled>Select a provider first or no models configured</option>
              )}
            </select>
          </div>

          <div>
            <label htmlFor="apiKey" className="block text-sm font-medium text-gray-300 mb-1">API Key</label>
            <input
              type="password"
              id="apiKey"
              value={apiKeyRaw}
              onChange={(e) => setApiKeyRaw(e.target.value)}
              required
              className="w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter your API key"
            />
          </div>

          <div>
            <label htmlFor="label" className="block text-sm font-medium text-gray-300 mb-1">Label</label>
            <input
              type="text"
              id="label"
              value={label}
              onChange={(e) => setLabel(e.target.value)}
              required
              className="w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-blue-500 focus:border-blue-500"
              placeholder="e.g., My Personal OpenAI Key"
            />
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full px-4 py-2.5 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:ring-4 focus:ring-blue-800 disabled:opacity-50 font-medium"
          >
            {isLoading ? 'Saving...' : 'Save API Key'}
          </button>
        </form>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">Saved API Keys</h2>
        {isLoadingKeys ? (
          <p className="text-gray-400">Loading keys...</p>
        ) : error && savedKeys.length === 0 ? (
           <p className="text-red-400 bg-red-900/50 p-3 rounded-md">Could not load keys: {error.replace('Error fetching keys: ', '')}</p>
        ) : savedKeys.length === 0 ? (
          <p className="text-gray-400">No API keys saved yet.</p>
        ) : (
          <div className="overflow-x-auto bg-gray-800 p-4 rounded-lg shadow-xl">
            <table className="min-w-full text-sm text-left text-gray-300">
              <thead className="text-xs text-gray-400 uppercase bg-gray-700">
                <tr>
                  <th scope="col" className="px-6 py-3">Label</th>
                  <th scope="col" className="px-6 py-3">Provider</th>
                  <th scope="col" className="px-6 py-3">Predefined Model ID</th>
                  <th scope="col" className="px-6 py-3">Status</th>
                  <th scope="col" className="px-6 py-3">Created At</th>
                  <th scope="col" className="px-6 py-3">Last Used</th>
                  <th scope="col" className="px-6 py-3"><span className="sr-only">Actions</span></th>
                </tr>
              </thead>
              <tbody>
                {savedKeys.map((key) => (
                  <tr key={key.id} className="border-b border-gray-700 hover:bg-gray-700/50">
                    <td className="px-6 py-4 font-medium whitespace-nowrap text-white">{key.label}</td>
                    <td className="px-6 py-4">{key.provider}</td>
                    <td className="px-6 py-4">{key.predefined_model_id}</td>
                    <td className="px-6 py-4">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full 
                        ${key.status === 'active' ? 'bg-green-900 text-green-300' : 
                          key.status === 'inactive' ? 'bg-yellow-900 text-yellow-300' : 
                          'bg-red-900 text-red-300'}`}>
                        {key.status}
                      </span>
                    </td>
                    <td className="px-6 py-4">{new Date(key.created_at).toLocaleDateString()}</td>
                    <td className="px-6 py-4">{key.last_used_at ? new Date(key.last_used_at).toLocaleDateString() : 'Never'}</td>
                    <td className="px-6 py-4 text-right">
                      <button
                        onClick={() => handleDeleteKey(key.id, key.label)}
                        disabled={isDeleting === key.id}
                        className="font-medium text-red-500 hover:text-red-400 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isDeleting === key.id ? 'Deleting...' : 'Delete'}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmation.isOpen}
        onClose={confirmation.hideConfirmation}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        type={confirmation.type}
        isLoading={confirmation.isLoading}
      />
    </div>
  );
}