import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { z } from 'zod';

// Schema for query parameters
const AnalyticsQuerySchema = z.object({
  startDate: z.string().datetime({ offset: true }).optional(),
  endDate: z.string().datetime({ offset: true }).optional(),
  customApiConfigId: z.string().uuid().optional(),
  groupBy: z.enum(['day', 'week', 'month', 'provider', 'model']).optional().default('day'),
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    // Validate query parameters
    const validatedParams = AnalyticsQuerySchema.parse(queryParams);
    const { startDate, endDate, customApiConfigId, groupBy } = validatedParams;

    const supabase = await createSupabaseServerClientOnRequest();

    // Build base query
    let query = supabase
      .from('request_logs')
      .select(`
        request_timestamp,
        status_code,
        cost,
        input_tokens,
        output_tokens,
        llm_provider_name,
        llm_model_name,
        custom_api_config_id
      `)
      .not('cost', 'is', null); // Only include records with cost data

    // Apply filters
    if (startDate) {
      query = query.gte('request_timestamp', startDate);
    }
    if (endDate) {
      query = query.lte('request_timestamp', endDate);
    }
    if (customApiConfigId) {
      query = query.eq('custom_api_config_id', customApiConfigId);
    }

    const { data: logs, error } = await query.order('request_timestamp', { ascending: false });

    if (error) {
      console.error('[Analytics API Error]', error);
      return NextResponse.json(
        { error: 'Failed to fetch analytics data', details: error.message },
        { status: 500 }
      );
    }

    // Calculate summary statistics
    const totalRequests = logs.length;
    const successfulRequests = logs.filter(log => log.status_code && log.status_code < 400).length;
    const totalCost = logs.reduce((sum, log) => sum + (log.cost || 0), 0);
    const totalInputTokens = logs.reduce((sum, log) => sum + (log.input_tokens || 0), 0);
    const totalOutputTokens = logs.reduce((sum, log) => sum + (log.output_tokens || 0), 0);

    // Group data based on groupBy parameter
    let groupedData: any[] = [];
    
    if (groupBy === 'provider') {
      const providerStats = logs.reduce((acc, log) => {
        const provider = log.llm_provider_name || 'Unknown';
        if (!acc[provider]) {
          acc[provider] = { 
            name: provider, 
            requests: 0, 
            cost: 0, 
            input_tokens: 0, 
            output_tokens: 0,
            success_rate: 0
          };
        }
        acc[provider].requests += 1;
        acc[provider].cost += log.cost || 0;
        acc[provider].input_tokens += log.input_tokens || 0;
        acc[provider].output_tokens += log.output_tokens || 0;
        if (log.status_code && log.status_code < 400) {
          acc[provider].success_rate += 1;
        }
        return acc;
      }, {} as Record<string, any>);

      groupedData = Object.values(providerStats).map((provider: any) => ({
        ...provider,
        success_rate: provider.requests > 0 ? (provider.success_rate / provider.requests) * 100 : 0
      }));
    } else if (groupBy === 'model') {
      const modelStats = logs.reduce((acc, log) => {
        const model = log.llm_model_name || 'Unknown';
        if (!acc[model]) {
          acc[model] = { 
            name: model, 
            requests: 0, 
            cost: 0, 
            input_tokens: 0, 
            output_tokens: 0,
            provider: log.llm_provider_name || 'Unknown'
          };
        }
        acc[model].requests += 1;
        acc[model].cost += log.cost || 0;
        acc[model].input_tokens += log.input_tokens || 0;
        acc[model].output_tokens += log.output_tokens || 0;
        return acc;
      }, {} as Record<string, any>);

      groupedData = Object.values(modelStats);
    } else {
      // Group by time period (day, week, month)
      const timeStats = logs.reduce((acc, log) => {
        const date = new Date(log.request_timestamp);
        let key: string;
        
        if (groupBy === 'day') {
          key = date.toISOString().split('T')[0]; // YYYY-MM-DD
        } else if (groupBy === 'week') {
          const weekStart = new Date(date);
          weekStart.setDate(date.getDate() - date.getDay());
          key = weekStart.toISOString().split('T')[0];
        } else { // month
          key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        }

        if (!acc[key]) {
          acc[key] = { 
            period: key, 
            requests: 0, 
            cost: 0, 
            input_tokens: 0, 
            output_tokens: 0 
          };
        }
        acc[key].requests += 1;
        acc[key].cost += log.cost || 0;
        acc[key].input_tokens += log.input_tokens || 0;
        acc[key].output_tokens += log.output_tokens || 0;
        return acc;
      }, {} as Record<string, any>);

      groupedData = Object.values(timeStats).sort((a, b) => a.period.localeCompare(b.period));
    }

    const response = {
      summary: {
        total_requests: totalRequests,
        successful_requests: successfulRequests,
        success_rate: totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0,
        total_cost: totalCost,
        total_input_tokens: totalInputTokens,
        total_output_tokens: totalOutputTokens,
        total_tokens: totalInputTokens + totalOutputTokens,
        average_cost_per_request: totalRequests > 0 ? totalCost / totalRequests : 0,
      },
      grouped_data: groupedData,
      filters: {
        start_date: startDate,
        end_date: endDate,
        custom_api_config_id: customApiConfigId,
        group_by: groupBy,
      },
    };

    return NextResponse.json(response);

  } catch (error: any) {
    console.error('[Analytics API Error]', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
