import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';

export async function DELETE(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  try {
    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log(`Starting account deletion for user: ${user.id}`);

    // Start a transaction-like cleanup process
    const cleanupErrors: string[] = [];

    // 1. Delete user-generated API keys
    try {
      const { error: userKeysError } = await supabase
        .from('user_generated_api_keys')
        .delete()
        .eq('user_id', user.id);
      
      if (userKeysError) {
        console.error('Error deleting user API keys:', userKeysError);
        cleanupErrors.push('Failed to delete user-generated API keys');
      } else {
        console.log('Deleted user-generated API keys');
      }
    } catch (error) {
      console.error('Error in user API keys deletion:', error);
      cleanupErrors.push('Failed to delete user-generated API keys');
    }

    // 2. Delete API keys from configurations
    try {
      const { error: apiKeysError } = await supabase
        .from('api_keys')
        .delete()
        .eq('user_id', user.id);
      
      if (apiKeysError) {
        console.error('Error deleting API keys:', apiKeysError);
        cleanupErrors.push('Failed to delete API keys');
      } else {
        console.log('Deleted API keys');
      }
    } catch (error) {
      console.error('Error in API keys deletion:', error);
      cleanupErrors.push('Failed to delete API keys');
    }

    // 3. Delete custom roles
    try {
      const { error: rolesError } = await supabase
        .from('user_custom_roles')
        .delete()
        .eq('user_id', user.id);
      
      if (rolesError) {
        console.error('Error deleting custom roles:', rolesError);
        cleanupErrors.push('Failed to delete custom roles');
      } else {
        console.log('Deleted custom roles');
      }
    } catch (error) {
      console.error('Error in custom roles deletion:', error);
      cleanupErrors.push('Failed to delete custom roles');
    }

    // 4. Delete custom API configurations
    try {
      const { error: configsError } = await supabase
        .from('custom_api_configs')
        .delete()
        .eq('user_id', user.id);
      
      if (configsError) {
        console.error('Error deleting configurations:', configsError);
        cleanupErrors.push('Failed to delete configurations');
      } else {
        console.log('Deleted custom configurations');
      }
    } catch (error) {
      console.error('Error in configurations deletion:', error);
      cleanupErrors.push('Failed to delete configurations');
    }

    // 5. Delete semantic cache entries
    try {
      const { error: cacheError } = await supabase
        .from('semantic_cache')
        .delete()
        .eq('user_id', user.id);
      
      if (cacheError) {
        console.error('Error deleting semantic cache:', cacheError);
        cleanupErrors.push('Failed to delete semantic cache');
      } else {
        console.log('Deleted semantic cache entries');
      }
    } catch (error) {
      console.error('Error in semantic cache deletion:', error);
      cleanupErrors.push('Failed to delete semantic cache');
    }

    // 6. Delete workflow usage tracking
    try {
      const { error: workflowError } = await supabase
        .from('workflow_usage')
        .delete()
        .eq('user_id', user.id);
      
      if (workflowError) {
        console.error('Error deleting workflow usage:', workflowError);
        cleanupErrors.push('Failed to delete workflow usage');
      } else {
        console.log('Deleted workflow usage tracking');
      }
    } catch (error) {
      console.error('Error in workflow usage deletion:', error);
      cleanupErrors.push('Failed to delete workflow usage');
    }

    // 7. Delete cost optimization profiles
    try {
      const { error: costError } = await supabase
        .from('cost_optimization_profiles')
        .delete()
        .eq('user_id', user.id);
      
      if (costError) {
        console.error('Error deleting cost profiles:', costError);
        cleanupErrors.push('Failed to delete cost optimization profiles');
      } else {
        console.log('Deleted cost optimization profiles');
      }
    } catch (error) {
      console.error('Error in cost profiles deletion:', error);
      cleanupErrors.push('Failed to delete cost optimization profiles');
    }

    // 8. Delete user profile
    try {
      const { error: profileError } = await supabase
        .from('user_profiles')
        .delete()
        .eq('id', user.id);
      
      if (profileError) {
        console.error('Error deleting user profile:', profileError);
        cleanupErrors.push('Failed to delete user profile');
      } else {
        console.log('Deleted user profile');
      }
    } catch (error) {
      console.error('Error in user profile deletion:', error);
      cleanupErrors.push('Failed to delete user profile');
    }

    // 9. Finally, delete the auth user (this will cascade to any remaining references)
    try {
      const { error: deleteUserError } = await supabase.auth.admin.deleteUser(user.id);
      
      if (deleteUserError) {
        console.error('Error deleting auth user:', deleteUserError);
        return NextResponse.json({ 
          error: 'Failed to delete user account',
          details: deleteUserError.message,
          cleanupErrors 
        }, { status: 500 });
      } else {
        console.log('Successfully deleted auth user');
      }
    } catch (error) {
      console.error('Error in auth user deletion:', error);
      return NextResponse.json({ 
        error: 'Failed to delete user account',
        details: 'Auth deletion failed',
        cleanupErrors 
      }, { status: 500 });
    }

    // Log completion
    console.log(`Account deletion completed for user: ${user.id}`);
    
    if (cleanupErrors.length > 0) {
      console.warn('Account deletion completed with some cleanup errors:', cleanupErrors);
      return NextResponse.json({ 
        success: true,
        message: 'Account deleted successfully with some cleanup warnings',
        warnings: cleanupErrors 
      });
    }

    return NextResponse.json({ 
      success: true,
      message: 'Account deleted successfully' 
    });

  } catch (error: any) {
    console.error('Error in account deletion:', error);
    return NextResponse.json({
      error: 'Failed to delete account',
      details: error.message
    }, { status: 500 });
  }
}
