'use client';

import React, { memo, useMemo, useCallback, useRef, useEffect } from 'react';
import { usePerformanceMonitor } from '@/contexts/PerformanceContext';

interface OptimizedComponentProps {
  children: React.ReactNode;
  name: string;
  dependencies?: any[];
  skipMemo?: boolean;
  onRender?: (renderTime: number) => void;
}

// Higher-order component for performance optimization
export function withPerformanceOptimization<T extends object>(
  Component: React.ComponentType<T>,
  componentName: string
) {
  const OptimizedComponent = memo((props: T) => {
    const { renderTime } = usePerformanceMonitor(componentName);
    
    useEffect(() => {
      if (renderTime > 100) {
        console.warn(`⚠️ ${componentName} took ${renderTime}ms to render`);
      }
    }, [renderTime]);

    return <Component {...props} />;
  });

  OptimizedComponent.displayName = `Optimized(${componentName})`;
  return OptimizedComponent;
}

// Optimized wrapper component
const OptimizedWrapper: React.FC<OptimizedComponentProps> = ({
  children,
  name,
  dependencies = [],
  skipMemo = false,
  onRender
}) => {
  const { renderTime } = usePerformanceMonitor(name);
  const renderCount = useRef(0);

  useEffect(() => {
    renderCount.current += 1;
    if (onRender) {
      onRender(renderTime);
    }
  }, [renderTime, onRender]);

  const memoizedChildren = useMemo(() => {
    if (skipMemo) return children;
    return children;
  }, dependencies);

  return <>{memoizedChildren}</>;
};

export const OptimizedComponent = memo(OptimizedWrapper);

// Hook for optimizing expensive calculations
export function useOptimizedMemo<T>(
  factory: () => T,
  deps: React.DependencyList,
  debugName?: string
): T {
  const startTime = useRef<number>();
  
  return useMemo(() => {
    if (debugName) {
      startTime.current = performance.now();
    }
    
    const result = factory();
    
    if (debugName && startTime.current) {
      const duration = performance.now() - startTime.current;
      if (duration > 10) {
        console.warn(`⚠️ Expensive memo calculation in ${debugName}: ${duration.toFixed(2)}ms`);
      }
    }
    
    return result;
  }, deps);
}

// Hook for optimizing callbacks
export function useOptimizedCallback<T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList,
  debugName?: string
): T {
  return useCallback((...args: Parameters<T>) => {
    const startTime = debugName ? performance.now() : 0;
    
    const result = callback(...args);
    
    if (debugName && startTime) {
      const duration = performance.now() - startTime;
      if (duration > 5) {
        console.warn(`⚠️ Slow callback in ${debugName}: ${duration.toFixed(2)}ms`);
      }
    }
    
    return result;
  }, deps) as T;
}

// Component for lazy loading with intersection observer
interface LazyComponentProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  rootMargin?: string;
  threshold?: number;
  onVisible?: () => void;
}

export const LazyComponent: React.FC<LazyComponentProps> = ({
  children,
  fallback = null,
  rootMargin = '50px',
  threshold = 0.1,
  onVisible
}) => {
  const [isVisible, setIsVisible] = React.useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
          onVisible?.();
          observer.disconnect();
        }
      },
      { rootMargin, threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [isVisible, rootMargin, threshold, onVisible]);

  return (
    <div ref={ref}>
      {isVisible ? children : fallback}
    </div>
  );
};

// Hook for debouncing state updates
export function useDebouncedState<T>(
  initialValue: T,
  delay: number
): [T, T, (value: T) => void] {
  const [immediateValue, setImmediateValue] = React.useState(initialValue);
  const [debouncedValue, setDebouncedValue] = React.useState(initialValue);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(immediateValue);
    }, delay);

    return () => clearTimeout(timer);
  }, [immediateValue, delay]);

  return [immediateValue, debouncedValue, setImmediateValue];
}

// Hook for throttling function calls
export function useThrottledCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastCall = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout>();

  return useCallback((...args: Parameters<T>) => {
    const now = Date.now();
    
    if (now - lastCall.current >= delay) {
      lastCall.current = now;
      return callback(...args);
    } else {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        lastCall.current = Date.now();
        callback(...args);
      }, delay - (now - lastCall.current));
    }
  }, [callback, delay]) as T;
}

// Component for measuring render performance
interface PerformanceMeasureProps {
  name: string;
  children: React.ReactNode;
  threshold?: number;
}

export const PerformanceMeasure: React.FC<PerformanceMeasureProps> = ({
  name,
  children,
  threshold = 16 // 16ms for 60fps
}) => {
  const startTime = useRef<number>();
  
  useEffect(() => {
    startTime.current = performance.now();
  });

  useEffect(() => {
    if (startTime.current) {
      const duration = performance.now() - startTime.current;
      if (duration > threshold) {
        console.warn(`⚠️ ${name} render exceeded threshold: ${duration.toFixed(2)}ms`);
      }
    }
  });

  return <>{children}</>;
};

export default OptimizedComponent;
