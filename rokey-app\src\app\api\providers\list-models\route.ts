import { type NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Define the structure of the model information we expect to send to the frontend
// This should align with what the frontend expects and what's available in your public.models table
interface ModelInfo {
  id: string; // Corresponds to 'id' in public.models
  name: string; // Corresponds to 'name' or 'display_name' in public.models
  display_name: string; // Corresponds to 'display_name' in public.models
  description?: string;
  provider_id?: string; // Corresponds to 'provider_id' in public.models
  family?: string;
  context_window?: number;
  input_token_limit?: number;
  output_token_limit?: number;
  modality?: 'text' | 'image' | 'audio' | 'video' | 'multimodal' | 'embedding' | 'moderation' | 'data_analysis' | 'unknown';
  is_public?: boolean;
  // Add any other fields from public.models that the frontend might need
}

export async function POST(request: NextRequest) {
  try {
    // Initialize Supabase client
    // Ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY (or SUPABASE_SERVICE_ROLE_KEY for admin tasks)
    // are set in your environment variables.
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY; // Using anon key, RLS must allow reads

    if (!supabaseUrl || !supabaseKey) {
      console.error('Supabase URL or Key is not defined in environment variables.');
      return NextResponse.json({ error: 'Server configuration error: Supabase credentials missing.' }, { status: 500 });
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    const { data: modelsData, error: fetchError } = await supabase
      .from('models')
      .select(`
        id,
        name,
        display_name,
        description,
        provider_id,
        family,
        context_window,
        input_token_limit,
        output_token_limit,
        modality,
        is_public
      `)
      .order('provider_id', { ascending: true })
      .order('name', { ascending: true });

    if (fetchError) {
      console.error('Error fetching models from Supabase:', fetchError);
      return NextResponse.json({ error: `Failed to fetch models: ${fetchError.message}` }, { status: 500 });
    }

    if (!modelsData) {
      return NextResponse.json({ models: [] }, { status: 200 });
    }

    // Transform the data if necessary to match the ModelInfo interface
    const formattedModels: ModelInfo[] = modelsData.map(model => ({
      id: model.id,
      name: model.name,
      display_name: model.display_name || model.name,
      description: model.description,
      provider_id: model.provider_id,
      family: model.family,
      context_window: model.context_window,
      input_token_limit: model.input_token_limit,
      output_token_limit: model.output_token_limit,
      modality: model.modality as ModelInfo['modality'] || 'unknown',
      is_public: model.is_public,
    }));
    
    // Apply the same filtering logic we had before if desired
    // For now, returning all models that were inserted.
    // You might want to re-introduce modality filters here based on your requirements.
    const allowedModalities: ModelInfo['modality'][] = ['text', 'multimodal', 'image']; // Example filter
    const filteredModels = formattedModels.filter(model => 
        model.modality && allowedModalities.includes(model.modality)
    );

    return NextResponse.json({ models: filteredModels }, { status: 200 });

  } catch (err: any) {
    console.error('Unexpected error in /api/providers/list-models:', err);
    return NextResponse.json({ error: `An unexpected error occurred: ${err.message || 'Unknown error'}` }, { status: 500 });
  }
}