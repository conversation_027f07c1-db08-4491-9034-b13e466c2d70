const API_KEY = 'rk_live_c5ae5a36_9Nl8iikIL2SDSNt6R4TujRKEg8xzUkK6';
const BASE_URL = 'http://localhost:3000/api/external/v1/chat/completions'; // LOCAL DEV SERVER

console.log('🧪 Testing Auto-Stream for Multi-Role Tasks (LOCAL DEV)');
console.log('🏠 Using localhost:3000 for debugging');
console.log('='.repeat(60));

async function testAutoStream() {
  // Test 1: Complex multi-role task that should trigger auto-streaming
  console.log('\n🧪 Test 1: Complex multi-role task (should auto-stream)');
  console.log('📤 Request: "brainstorm an idea for a book and write a python code about it"');
  console.log('📤 Stream: false (should be forced to true)');
  
  try {
    const response = await fetch(BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: 'brainstorm an idea for a book and write a python code about it'
          }
        ],
        stream: false, // This should be forced to true
        max_tokens: 500
      })
    });

    console.log(`📊 Status: ${response.status}`);
    console.log(`📋 Content-Type: ${response.headers.get('content-type')}`);
    
    // Check for auto-stream headers
    const streamForced = response.headers.get('X-RouKey-Stream-Forced');
    const streamReason = response.headers.get('X-RouKey-Stream-Reason');
    const multiRoleDetected = response.headers.get('X-RouKey-Multi-Role-Detected');
    const asyncRecommendation = response.headers.get('X-RouKey-Async-Recommendation');
    
    console.log(`🔄 Stream Forced: ${streamForced || 'No'}`);
    console.log(`💡 Stream Reason: ${streamReason || 'None'}`);
    console.log(`🎭 Multi-Role Detected: ${multiRoleDetected || 'No'}`);
    console.log(`📋 Async Recommendation: ${asyncRecommendation || 'None'}`);
    
    if (response.headers.get('content-type')?.includes('text/event-stream')) {
      console.log('✅ Successfully converted to streaming!');
      
      // Read the streaming response
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let fullResponse = '';
      
      console.log('📡 Streaming response:');
      console.log('-'.repeat(30));
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        fullResponse += chunk;
        process.stdout.write(chunk);
      }
      
      console.log('\n' + '-'.repeat(30));
      console.log(`📏 Total response length: ${fullResponse.length} characters`);
      
    } else {
      console.log('❌ Not converted to streaming');
      const responseText = await response.text();
      console.log('📥 Response:', responseText.substring(0, 500) + '...');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Test 2: More obviously multi-role task
async function testObviousMultiRole() {
  console.log('\n🧪 Test 2: Obviously multi-role task');
  console.log('📤 Request: "Research quantum computing, then write Python code, then create a business plan"');
  console.log('📤 Stream: false (should be forced to true)');
  
  try {
    const response = await fetch(BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: 'Research quantum computing, then write Python code to simulate it, then create a business plan for a quantum startup'
          }
        ],
        stream: false, // This should be forced to true
        max_tokens: 500
      })
    });

    console.log(`📊 Status: ${response.status}`);
    console.log(`📋 Content-Type: ${response.headers.get('content-type')}`);
    
    const streamForced = response.headers.get('X-RouKey-Stream-Forced');
    const streamReason = response.headers.get('X-RouKey-Stream-Reason');
    const multiRoleDetected = response.headers.get('X-RouKey-Multi-Role-Detected');
    const asyncRecommendation = response.headers.get('X-RouKey-Async-Recommendation');
    
    console.log(`🔄 Stream Forced: ${streamForced || 'No'}`);
    console.log(`💡 Stream Reason: ${streamReason || 'None'}`);
    console.log(`🎭 Multi-Role Detected: ${multiRoleDetected || 'No'}`);
    console.log(`📋 Async Recommendation: ${asyncRecommendation || 'None'}`);
    
    if (response.headers.get('content-type')?.includes('text/event-stream')) {
      console.log('✅ Successfully converted to streaming!');
      
      // Read first few chunks to avoid overwhelming output
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let chunkCount = 0;
      
      console.log('📡 First few streaming chunks:');
      console.log('-'.repeat(30));
      
      while (chunkCount < 5) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        process.stdout.write(chunk);
        chunkCount++;
      }
      
      console.log('\n' + '-'.repeat(30));
      console.log('✅ Streaming confirmed (stopped after 5 chunks)');
      
    } else {
      console.log('❌ Not converted to streaming');
      const responseText = await response.text();
      console.log('📥 Response:', responseText.substring(0, 300) + '...');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Test 3: Simple task that should NOT trigger auto-streaming
async function testNoAutoStream() {
  console.log('\n🧪 Test 3: Simple task (should NOT auto-stream)');
  console.log('📤 Request: "hi"');
  console.log('📤 Stream: false (should remain false)');
  
  try {
    const response = await fetch(BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: 'hi'
          }
        ],
        stream: false,
        max_tokens: 500
      })
    });

    console.log(`📊 Status: ${response.status}`);
    console.log(`📋 Content-Type: ${response.headers.get('content-type')}`);
    
    const streamForced = response.headers.get('X-RouKey-Stream-Forced');
    const multiRoleDetected = response.headers.get('X-RouKey-Multi-Role-Detected');
    
    console.log(`🔄 Stream Forced: ${streamForced || 'No'}`);
    console.log(`🎭 Multi-Role Detected: ${multiRoleDetected || 'No'}`);
    
    if (response.headers.get('content-type')?.includes('application/json')) {
      console.log('✅ Correctly remained as non-streaming');
      const responseData = await response.json();
      console.log(`📏 Response length: ${responseData.choices?.[0]?.message?.content?.length || 0} characters`);
      console.log(`🎭 Roles used: ${responseData.rokey_metadata?.roles_used?.join(', ') || 'none'}`);
    } else {
      console.log('⚠️ Unexpectedly converted to streaming');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting local auto-stream tests...');
  console.log('👀 Check your terminal logs for classification details!');
  
  await testAutoStream();
  await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 seconds
  
  await testObviousMultiRole();
  await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 seconds
  
  await testNoAutoStream();
  
  console.log('\n' + '='.repeat(60));
  console.log('🏁 Local Auto-Stream Tests Complete');
  console.log('📋 Check your dev server terminal for detailed logs!');
}

runTests().catch(console.error);
