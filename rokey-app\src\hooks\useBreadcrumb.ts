'use client';

import { usePathname, useSearchParams } from 'next/navigation';
import { useMemo } from 'react';

interface BreadcrumbItem {
  title: string;
  subtitle: string;
  href?: string;
  isActive?: boolean;
}

interface BreadcrumbConfig {
  title: string;
  subtitle: string;
  parent?: string;
  dynamic?: boolean;
}

// Route configuration for breadcrumbs
const routeConfig: Record<string, BreadcrumbConfig> = {
  '/dashboard': { 
    title: 'Dashboard', 
    subtitle: 'Overview & analytics' 
  },
  '/playground': { 
    title: 'Playground', 
    subtitle: 'Test your models',
    parent: '/dashboard'
  },
  '/my-models': { 
    title: 'My Models', 
    subtitle: 'API key management',
    parent: '/dashboard'
  },
  '/routing-setup': { 
    title: 'Routing Setup', 
    subtitle: 'Configure routing',
    parent: '/dashboard'
  },
  '/logs': { 
    title: 'Logs', 
    subtitle: 'Request history',
    parent: '/dashboard'
  },
  '/training': { 
    title: 'Prompt Engineering', 
    subtitle: 'Custom prompts',
    parent: '/dashboard'
  },
  '/analytics': { 
    title: 'Analytics', 
    subtitle: 'Advanced insights',
    parent: '/dashboard'
  },
  '/add-keys': { 
    title: 'Add Keys', 
    subtitle: 'API key setup',
    parent: '/my-models'
  },
};

// Dynamic route patterns
const dynamicRoutes = [
  {
    pattern: /^\/my-models\/([^\/]+)$/,
    getConfig: (matches: RegExpMatchArray) => ({
      title: 'Manage Keys',
      subtitle: 'API key management',
      parent: '/my-models'
    })
  },
  {
    pattern: /^\/routing-setup\/([^\/]+)$/,
    getConfig: (matches: RegExpMatchArray) => ({
      title: 'Routing Configuration',
      subtitle: 'Advanced routing setup',
      parent: '/routing-setup'
    })
  },
  {
    pattern: /^\/routing-setup\/([^\/]+)$/,
    getConfig: (matches: RegExpMatchArray) => ({
      title: 'Routing Setup',
      subtitle: 'Advanced configuration',
      parent: '/routing-setup'
    })
  },
  {
    pattern: /^\/playground\?config=([^&]+)/,
    getConfig: (matches: RegExpMatchArray, searchParams: URLSearchParams) => ({
      title: 'Playground',
      subtitle: `Testing configuration`,
      parent: '/playground'
    })
  }
];

export function useBreadcrumb() {
  const pathname = usePathname();

  // Always call useSearchParams (hooks must be called unconditionally)
  // If it fails, the component should be wrapped in Suspense
  const searchParams = useSearchParams();

  const breadcrumb = useMemo(() => {
    // Check for exact route matches first
    const exactConfig = routeConfig[pathname];
    if (exactConfig) {
      return {
        title: exactConfig.title,
        subtitle: exactConfig.subtitle,
        parent: exactConfig.parent
      };
    }

    // Check dynamic routes
    for (const route of dynamicRoutes) {
      const matches = pathname.match(route.pattern);
      if (matches) {
        return route.getConfig(matches, searchParams);
      }
    }

    // Special handling for query parameters
    const fullUrl = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : '');
    for (const route of dynamicRoutes) {
      const matches = fullUrl.match(route.pattern);
      if (matches) {
        return route.getConfig(matches, searchParams);
      }
    }

    // Default fallback
    return {
      title: 'Dashboard',
      subtitle: 'Overview',
      parent: undefined
    };
  }, [pathname, searchParams]);

  // Generate full breadcrumb trail
  const breadcrumbTrail = useMemo(() => {
    const trail: BreadcrumbItem[] = [];
    
    // Add parent if exists
    if (breadcrumb.parent && routeConfig[breadcrumb.parent]) {
      const parentConfig = routeConfig[breadcrumb.parent];
      trail.push({
        title: parentConfig.title,
        subtitle: parentConfig.subtitle,
        href: breadcrumb.parent,
        isActive: false
      });
    }

    // Add current page
    trail.push({
      title: breadcrumb.title,
      subtitle: breadcrumb.subtitle,
      href: pathname,
      isActive: true
    });

    return trail;
  }, [breadcrumb, pathname]);

  // Get simple breadcrumb for navbar
  const simpleBreadcrumb = useMemo(() => ({
    title: breadcrumb.title,
    subtitle: breadcrumb.subtitle
  }), [breadcrumb]);

  // Get page title for document title
  const pageTitle = useMemo(() => {
    return `${breadcrumb.title} - RouKey`;
  }, [breadcrumb]);

  return {
    breadcrumb: simpleBreadcrumb,
    breadcrumbTrail,
    pageTitle,
    currentPage: {
      title: breadcrumb.title,
      subtitle: breadcrumb.subtitle,
      path: pathname
    }
  };
}

// Hook for setting document title
export function useDocumentTitle() {
  const { pageTitle } = useBreadcrumb();
  
  // Set document title
  if (typeof document !== 'undefined') {
    document.title = pageTitle;
  }

  return pageTitle;
}

// Hook for getting contextual navigation suggestions
export function useContextualNavigation() {
  const pathname = usePathname();

  const suggestions = useMemo(() => {
    const contextualSuggestions = [];

    switch (pathname) {
      case '/dashboard':
        contextualSuggestions.push(
          { title: 'Test Models', href: '/playground', priority: 'high' },
          { title: 'Manage Keys', href: '/my-models', priority: 'medium' },
          { title: 'View Logs', href: '/logs', priority: 'medium' }
        );
        break;
      
      case '/my-models':
        contextualSuggestions.push(
          { title: 'Test Configuration', href: '/playground', priority: 'high' },
          { title: 'Setup Routing', href: '/routing-setup', priority: 'high' },
          { title: 'Add New Keys', href: '/add-keys', priority: 'medium' }
        );
        break;
      
      case '/playground':
        contextualSuggestions.push(
          { title: 'View Request Logs', href: '/logs', priority: 'high' },
          { title: 'Switch Configuration', href: '/my-models', priority: 'medium' },
          { title: 'Customize Prompts', href: '/training', priority: 'low' }
        );
        break;
      
      case '/logs':
        contextualSuggestions.push(
          { title: 'Test in Playground', href: '/playground', priority: 'high' },
          { title: 'View Analytics', href: '/analytics', priority: 'medium' },
          { title: 'Adjust Configuration', href: '/my-models', priority: 'low' }
        );
        break;

      case '/routing-setup':
        contextualSuggestions.push(
          { title: 'Test Routing', href: '/playground', priority: 'high' },
          { title: 'Manage Models', href: '/my-models', priority: 'medium' },
          { title: 'View Results', href: '/logs', priority: 'low' }
        );
        break;
    }

    return contextualSuggestions;
  }, [pathname]);

  return suggestions;
}
