#!/usr/bin/env node

/**
 * Comprehensive cache clearing script for development issues
 * Fixes the "Cannot read properties of undefined (reading 'call')" error
 * that occurs in normal browser mode but not incognito
 */

const fs = require('fs');
const path = require('path');

function deleteFolderRecursive(folderPath) {
  if (fs.existsSync(folderPath)) {
    fs.readdirSync(folderPath).forEach((file) => {
      const curPath = path.join(folderPath, file);
      if (fs.lstatSync(curPath).isDirectory()) {
        deleteFolderRecursive(curPath);
      } else {
        fs.unlinkSync(curPath);
      }
    });
    fs.rmdirSync(folderPath);
  }
}

console.log('🔧 Fixing cache-related development issues...\n');

// 1. Clear Next.js caches
console.log('1️⃣ Clearing Next.js caches...');
const nextCachePaths = [
  '.next',
  'node_modules/.cache',
  '.next/cache',
  '.next/server',
  '.next/static',
  '.next/trace'
];

nextCachePaths.forEach(cachePath => {
  const fullPath = path.join(process.cwd(), cachePath);
  if (fs.existsSync(fullPath)) {
    try {
      deleteFolderRecursive(fullPath);
      console.log(`   ✅ Cleared: ${cachePath}`);
    } catch (error) {
      console.log(`   ⚠️  Could not clear ${cachePath}: ${error.message}`);
    }
  }
});

// 2. Clear service worker files
console.log('\n2️⃣ Clearing service worker files...');
const swFiles = ['public/sw.js', 'public/workbox-*.js'];
swFiles.forEach(pattern => {
  const fullPath = path.join(process.cwd(), pattern);
  if (fs.existsSync(fullPath)) {
    try {
      fs.unlinkSync(fullPath);
      console.log(`   ✅ Removed: ${pattern}`);
    } catch (error) {
      console.log(`   ⚠️  Could not remove ${pattern}: ${error.message}`);
    }
  }
});

console.log('\n🎉 Cache clearing complete!\n');

console.log('📋 Next steps to completely fix the issue:\n');
console.log('1. 🌐 Clear browser cache:');
console.log('   • Chrome/Edge: Ctrl+Shift+Delete → Clear browsing data');
console.log('   • Firefox: Ctrl+Shift+Delete → Clear recent history');
console.log('   • Safari: Cmd+Option+E → Empty caches\n');

console.log('2. 🔄 Restart development server:');
console.log('   npm run dev\n');

console.log('3. 🧪 Test navigation:');
console.log('   • Try navigating from API config to Advanced setup');
console.log('   • Should work without the webpack error\n');

console.log('4. 🚀 Production note:');
console.log('   • This issue is development-only');
console.log('   • Production builds handle caching differently');
console.log('   • No changes needed for production deployment\n');

console.log('💡 If the issue persists:');
console.log('   • Try a different browser');
console.log('   • Check for browser extensions interfering');
console.log('   • Restart your computer (clears all system caches)');
