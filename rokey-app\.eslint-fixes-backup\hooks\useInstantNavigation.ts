'use client';

import React, { useEffect, useCallback, startTransition } from 'react';
import { useRouter } from 'next/navigation';

const ROUTES_TO_PREFETCH = [
  '/features',
  '/pricing', 
  '/about',
  '/auth/signin',
  '/auth/signup',
  '/docs'
];

export function useInstantNavigation() {
  const router = useRouter();

  // Prefetch all routes immediately when the hook is used
  useEffect(() => {
    const prefetchRoutes = async () => {
      // Use requestIdleCallback for better performance
      if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
        window.requestIdleCallback(() => {
          ROUTES_TO_PREFETCH.forEach(route => {
            router.prefetch(route);
          });
        });
      } else {
        // Fallback for browsers without requestIdleCallback
        setTimeout(() => {
          ROUTES_TO_PREFETCH.forEach(route => {
            router.prefetch(route);
          });
        }, 100);
      }
    };

    prefetchRoutes();
  }, [router]);

  // Create instant navigation function
  const navigateInstantly = useCallback((href: string) => {
    // Use startTransition for better performance
    startTransition(() => {
      router.push(href);
    });
  }, [router]);

  return { navigateInstantly };
}
