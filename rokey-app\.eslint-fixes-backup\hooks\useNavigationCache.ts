'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { usePathname } from 'next/navigation';

interface CachedPageData {
  route: string;
  timestamp: number;
  loadTime: number;
  isReady: boolean;
  prefetched: boolean;
}

interface NavigationMetrics {
  totalNavigations: number;
  averageLoadTime: number;
  cacheHitRate: number;
  fastNavigations: number; // < 100ms
}

const CACHE_DURATION = 10 * 60 * 1000; // 10 minutes
const FAST_NAVIGATION_THRESHOLD = 100; // ms

export function useNavigationCache() {
  const [cachedPages, setCachedPages] = useState<Map<string, CachedPageData>>(new Map());
  const [metrics, setMetrics] = useState<NavigationMetrics>({
    totalNavigations: 0,
    averageLoadTime: 0,
    cacheHitRate: 0,
    fastNavigations: 0
  });
  
  const pathname = usePathname();
  const navigationStartTime = useRef<number>(0);
  const loadTimes = useRef<number[]>([]);

  // Track page load completion
  useEffect(() => {
    if (navigationStartTime.current > 0) {
      const loadTime = Date.now() - navigationStartTime.current;
      loadTimes.current.push(loadTime);
      
      // Update cached page data
      setCachedPages(prev => {
        const updated = new Map(prev);
        updated.set(pathname, {
          route: pathname,
          timestamp: Date.now(),
          loadTime,
          isReady: true,
          prefetched: prev.get(pathname)?.prefetched || false
        });
        return updated;
      });

      // Update metrics
      setMetrics(prev => {
        const totalNavigations = prev.totalNavigations + 1;
        const averageLoadTime = loadTimes.current.reduce((a, b) => a + b, 0) / loadTimes.current.length;
        const fastNavigations = loadTimes.current.filter(time => time < FAST_NAVIGATION_THRESHOLD).length;
        const cacheHitRate = (fastNavigations / totalNavigations) * 100;

        return {
          totalNavigations,
          averageLoadTime,
          cacheHitRate,
          fastNavigations
        };
      });

      navigationStartTime.current = 0;
    }
  }, [pathname]);

  // Start navigation timing
  const startNavigation = useCallback((route: string) => {
    navigationStartTime.current = Date.now();
    
    // Mark as navigating if not cached
    const cached = cachedPages.get(route);
    if (!cached || !cached.isReady || (Date.now() - cached.timestamp) > CACHE_DURATION) {
      return false; // Not cached or expired
    }
    
    return true; // Cached and ready
  }, [cachedPages]);

  // Mark page as prefetched
  const markPrefetched = useCallback((route: string) => {
    setCachedPages(prev => {
      const updated = new Map(prev);
      const existing = updated.get(route);
      updated.set(route, {
        route,
        timestamp: Date.now(),
        loadTime: existing?.loadTime || 0,
        isReady: existing?.isReady || false,
        prefetched: true
      });
      return updated;
    });
  }, []);

  // Check if page is cached and ready
  const isPageReady = useCallback((route: string) => {
    const cached = cachedPages.get(route);
    if (!cached) return false;
    
    // Check if cache is still valid
    const isValid = (Date.now() - cached.timestamp) < CACHE_DURATION;
    return isValid && cached.isReady;
  }, [cachedPages]);

  // Get cache status for a route
  const getCacheStatus = useCallback((route: string) => {
    const cached = cachedPages.get(route);
    if (!cached) return 'not-cached';
    
    const isValid = (Date.now() - cached.timestamp) < CACHE_DURATION;
    if (!isValid) return 'expired';
    
    if (cached.prefetched && !cached.isReady) return 'prefetching';
    if (cached.isReady) return 'ready';
    
    return 'loading';
  }, [cachedPages]);

  // Clean up expired cache entries
  const cleanupCache = useCallback(() => {
    setCachedPages(prev => {
      const updated = new Map();
      const now = Date.now();
      
      for (const [route, data] of prev.entries()) {
        if ((now - data.timestamp) < CACHE_DURATION) {
          updated.set(route, data);
        }
      }
      
      return updated;
    });
  }, []);

  // Auto cleanup every 5 minutes
  useEffect(() => {
    const interval = setInterval(cleanupCache, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [cleanupCache]);

  // Get performance insights
  const getPerformanceInsights = useCallback(() => {
    const insights = [];
    
    if (metrics.cacheHitRate < 50) {
      insights.push('Consider implementing more aggressive prefetching');
    }
    
    if (metrics.averageLoadTime > 1000) {
      insights.push('Page load times are slower than optimal');
    }
    
    if (metrics.fastNavigations / metrics.totalNavigations > 0.8) {
      insights.push('Navigation performance is excellent');
    }
    
    return insights;
  }, [metrics]);

  return {
    // Cache management
    isPageReady,
    getCacheStatus,
    markPrefetched,
    startNavigation,
    cleanupCache,
    
    // Metrics and insights
    metrics,
    getPerformanceInsights,
    
    // Cache data
    cachedPages: Array.from(cachedPages.values()),
    cacheSize: cachedPages.size
  };
}

// Hook for monitoring navigation performance
export function useNavigationPerformance() {
  const [navigationTimes, setNavigationTimes] = useState<number[]>([]);
  const [currentNavigation, setCurrentNavigation] = useState<{
    startTime: number;
    route: string;
  } | null>(null);

  const startTiming = useCallback((route: string) => {
    setCurrentNavigation({
      startTime: performance.now(),
      route
    });
  }, []);

  const endTiming = useCallback(() => {
    if (currentNavigation) {
      const endTime = performance.now();
      const duration = endTime - currentNavigation.startTime;
      
      setNavigationTimes(prev => [...prev.slice(-19), duration]); // Keep last 20
      setCurrentNavigation(null);
      
      // Log performance data in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`🏁 [NAV PERF] Navigation to ${currentNavigation.route} took ${duration.toFixed(2)}ms`);
      }
      
      return duration;
    }
    return 0;
  }, [currentNavigation]);

  const getAverageTime = useCallback(() => {
    if (navigationTimes.length === 0) return 0;
    return navigationTimes.reduce((a, b) => a + b, 0) / navigationTimes.length;
  }, [navigationTimes]);

  const getPerformanceGrade = useCallback(() => {
    const avgTime = getAverageTime();
    if (avgTime < 100) return 'A';
    if (avgTime < 300) return 'B';
    if (avgTime < 500) return 'C';
    if (avgTime < 1000) return 'D';
    return 'F';
  }, [getAverageTime]);

  return {
    startTiming,
    endTiming,
    getAverageTime,
    getPerformanceGrade,
    navigationTimes,
    isNavigating: currentNavigation !== null
  };
}
