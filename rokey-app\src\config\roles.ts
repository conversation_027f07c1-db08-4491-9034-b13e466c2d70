export interface Role {
  id: string; // e.g., 'logic', 'copywriting'
  name: string; // e.g., 'Logic & Reasoning', 'Copywriting & Content Creation'
  description?: string; // Optional: A brief explanation of the role
}

export const PREDEFINED_ROLES: Role[] = [
  {
    id: 'general_chat',
    name: 'General Chat',
    description: 'Casual conversation, simple questions, greetings, and basic interactions that do not require specialized expertise.'
  },
  {
    id: 'logic_reasoning',
    name: 'Logic & Reasoning',
    description: 'Mathematical calculations, logical puzzles, analytical problem-solving, deductive reasoning, and complex analytical thinking.'
  },
  {
    id: 'writing',
    name: 'Writing & Content Creation',
    description: 'Creating written content like stories, articles, books, poems, scripts, marketing copy, blog posts, and creative narratives.'
  },
  {
    id: 'coding_frontend',
    name: 'Coding - Frontend',
    description: 'Building user interfaces with HTML, CSS, JavaScript, React, Vue, Angular, web design, and client-side development.'
  },
  {
    id: 'coding_backend',
    name: 'Coding - Backend',
    description: 'Programming server-side applications, APIs, databases, Python scripts, Node.js, Java, backend logic, and system architecture.'
  },
  {
    id: 'research_synthesis',
    name: 'Research & Synthesis',
    description: 'Gathering information from sources, conducting research, analyzing data, and synthesizing findings into comprehensive reports.'
  },
  {
    id: 'summarization_briefing',
    name: 'Summarization & Briefing',
    description: 'Condensing lengthy documents, extracting key points, creating executive summaries, and briefing complex information.'
  },
  {
    id: 'translation_localization',
    name: 'Translation & Localization',
    description: 'Converting text between different languages, linguistic translation, cultural adaptation, and multilingual communication.'
  },
  {
    id: 'data_extraction_structuring',
    name: 'Data Extraction & Structuring',
    description: 'Extracting specific information from unstructured text, organizing data into tables, lists, JSON, CSV, and structured formats.'
  },
  {
    id: 'brainstorming_ideation',
    name: 'Brainstorming & Ideation',
    description: 'Generating creative ideas, innovative concepts, brainstorming solutions, exploring possibilities, and creative thinking sessions.'
  },
  {
    id: 'education_tutoring',
    name: 'Education & Tutoring',
    description: 'Teaching concepts, explaining topics step-by-step, creating educational materials, tutoring, and providing learning guidance.'
  },
  {
    id: 'image_generation',
    name: 'Image Generation',
    description: 'Creating visual images, artwork, graphics, and illustrations from textual descriptions using AI image generation models.'
  },
  {
    id: 'audio_transcription',
    name: 'Audio Transcription',
    description: 'Converting speech from audio files into written text, transcribing recordings, and speech-to-text processing.'
  },

  {
    id: 'data_extractor',
    name: 'Data Extractor',
    description: 'Extracting specific data from web pages, scraping content, and gathering information from websites.'
  },
  {
    id: 'form_filler',
    name: 'Form Filler',
    description: 'Filling out web forms, submitting data, and handling form-based interactions on websites.'
  },
  {
    id: 'verification_agent',
    name: 'Verification Agent',
    description: 'Verifying information on websites, fact-checking, and validating data accuracy.'
  },
  {
    id: 'research_assistant',
    name: 'Research Assistant',
    description: 'Conducting web-based research, gathering information from multiple sources, and compiling research findings.'
  },
  {
    id: 'shopping_assistant',
    name: 'Shopping Assistant',
    description: 'Helping with online shopping, price comparisons, product research, and e-commerce tasks.'
  },
  {
    id: 'price_comparison',
    name: 'Price Comparison',
    description: 'Comparing prices across different websites, finding deals, and analyzing product pricing.'
  },
  {
    id: 'fact_checker',
    name: 'Fact Checker',
    description: 'Verifying facts and information across multiple web sources, cross-referencing data for accuracy.'
  },
  {
    id: 'task_executor',
    name: 'Task Executor',
    description: 'General task execution and automation, handling various web-based tasks and workflows.'
  }
  // TODO: Consider adding more specialized roles for legal, financial, or specific industry tasks if needed.
];

export const getRoleById = (id: string): Role | undefined => {
  return PREDEFINED_ROLES.find(role => role.id === id);
};

export const getRoleName = (id: string): string | undefined => {
  return PREDEFINED_ROLES.find(role => role.id === id)?.name;
}; 