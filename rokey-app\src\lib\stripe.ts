import Stripe from 'stripe';
import { STRIPE_KEYS, STRIPE_PRICE_IDS, STRIPE_PRODUCT_IDS } from './stripe-config';

// Server-side only Stripe instance
let stripeInstance: Stripe | null = null;

export const getStripe = (): Stripe => {
  if (!stripeInstance) {
    if (!STRIPE_KEYS.secretKey) {
      throw new Error('Stripe secret key is not configured');
    }
    stripeInstance = new Stripe(STRIPE_KEYS.secretKey, {
      apiVersion: '2025-02-24.acacia',
    });
  }
  return stripeInstance;
};

// For backward compatibility
export const stripe = typeof window === 'undefined' ? getStripe() : null;

export const STRIPE_CONFIG = {
  PRICE_IDS: STRIPE_PRICE_IDS,
  PRODUCT_IDS: STRIPE_PRODUCT_IDS,
};

// Re-export client-safe types and functions
export {
  type SubscriptionTier,
  type TierConfig,
  type SubscriptionStatus,
  type UsageStatus,
  TIER_CONFIGS,
  getTierConfig,
  getPriceIdForTier,
  getTierFromPriceId,
  formatPrice,
  canPerformAction,
  hasFeatureAccess
} from './stripe-client';
