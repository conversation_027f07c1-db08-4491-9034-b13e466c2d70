'use client';

import { useState, useEffect, Suspense } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { EnvelopeIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import EnhancedGridBackground from '@/components/landing/EnhancedGridBackground';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { useSearchParams, useRouter } from 'next/navigation';

function VerifyEmailContent() {
  const [isResending, setIsResending] = useState(false);
  const [resendMessage, setResendMessage] = useState('');
  const [email, setEmail] = useState('');
  const searchParams = useSearchParams();
  const router = useRouter();
  const supabase = createSupabaseBrowserClient();

  useEffect(() => {
    const emailParam = searchParams.get('email');
    const paymentSuccess = searchParams.get('payment_success');

    if (emailParam) {
      setEmail(decodeURIComponent(emailParam));
    }

    // Check if user is already verified and has completed payment
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        // Check if user has active subscription
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('subscription_status')
          .eq('user_id', user.id)
          .single();

        if (profile && profile.subscription_status === 'active') {
          // User has paid and verified, redirect to dashboard
          router.push('/dashboard');
        } else if (paymentSuccess === 'true') {
          // Payment was successful but subscription not yet active (webhook delay)
          // Show success message and wait for webhook
          setResendMessage('Payment successful! Please verify your email to complete setup.');
        }
      }
    };
    checkUser();
  }, [searchParams, router, supabase]);

  const handleResendEmail = async () => {
    if (!email) {
      setResendMessage('Please enter your email address');
      return;
    }

    setIsResending(true);
    setResendMessage('');

    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
      });

      if (error) {
        throw error;
      }

      setResendMessage('Verification email sent! Please check your inbox.');
    } catch (err: any) {
      console.error('Resend error:', err);
      setResendMessage(err.message || 'Failed to resend email. Please try again.');
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="min-h-screen bg-white relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      {/* Grid Background */}
      <EnhancedGridBackground
        gridSize={50}
        opacity={0.064}
        color="#000000"
        variant="subtle"
        animated={true}
        className="fixed inset-0"
      />

      {/* Orange Accent Grid */}
      <div className="absolute inset-0">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `radial-gradient(circle, rgba(255, 107, 53, 0.12) 1px, transparent 1px)`,
            backgroundSize: '100px 100px',
            mask: `radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)`,
            WebkitMask: `radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)`
          }}
        ></div>
      </div>

      <div className="relative z-10 w-full max-w-md mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center"
        >
          {/* Header */}
          <div className="mb-8">
            <Link href="/" className="inline-flex items-center space-x-3 mb-8">
              <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg border border-gray-100 p-1">
                <Image
                  src="/roukey_logo.png"
                  alt="RouKey"
                  width={40}
                  height={40}
                  className="w-full h-full object-contain"
                  priority
                />
              </div>
              <span className="text-3xl font-bold text-black">RouKey</span>
            </Link>
          </div>

          {/* Main Content */}
          <div className="bg-white rounded-3xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm relative overflow-hidden">
            {/* Background Pattern */}
            <div
              className="absolute inset-0 opacity-5"
              style={{
                backgroundImage: `
                  linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
                  linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
                `,
                backgroundSize: '20px 20px'
              }}
            ></div>

            <div className="relative z-10">
              {/* Icon */}
              <div className="w-20 h-20 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-full flex items-center justify-center mx-auto mb-6">
                <EnvelopeIcon className="w-10 h-10 text-white" />
              </div>

              <h2 className="text-3xl font-bold text-black mb-4">Check Your Email</h2>
              <p className="text-gray-600 text-lg mb-6">
                We've sent a verification link to:
              </p>
              
              {email && (
                <div className="bg-gray-50 rounded-xl p-4 mb-6">
                  <p className="text-[#ff6b35] font-semibold text-lg">{email}</p>
                </div>
              )}

              <p className="text-gray-600 mb-8">
                Click the link in the email to verify your account and complete your registration.
              </p>

              {/* Resend Section */}
              <div className="space-y-4">
                {!email && (
                  <div>
                    <input
                      type="email"
                      placeholder="Enter your email address"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white mb-4"
                    />
                  </div>
                )}

                <button
                  onClick={handleResendEmail}
                  disabled={isResending || !email}
                  className="w-full bg-white border-2 border-gray-200 text-gray-700 py-3 px-6 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-300 focus:ring-4 focus:ring-gray-200 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {isResending ? (
                    <>
                      <ArrowPathIcon className="w-5 h-5 mr-2 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <ArrowPathIcon className="w-5 h-5 mr-2" />
                      Resend verification email
                    </>
                  )}
                </button>

                {resendMessage && (
                  <div className={`p-4 rounded-xl ${resendMessage.includes('sent') ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                    <p className={`text-sm ${resendMessage.includes('sent') ? 'text-green-600' : 'text-red-600'}`}>
                      {resendMessage}
                    </p>
                  </div>
                )}
              </div>

              {/* Back to Sign In */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <p className="text-gray-600">
                  Already verified?{' '}
                  <Link href="/auth/signin" className="text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors">
                    Sign in to your account
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}

export default function VerifyEmailPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <VerifyEmailContent />
    </Suspense>
  );
}
