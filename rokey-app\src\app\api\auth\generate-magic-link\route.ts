import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/service-role';

export async function POST(req: NextRequest) {
  console.log('🔗 GENERATE-MAGIC-LINK: API called');
  
  try {
    const { email, userId, redirectTo } = await req.json();
    console.log('🔗 GENERATE-MAGIC-LINK: Request data:', { email, userId, redirectTo });

    if (!email || !userId) {
      console.log('🔗 GENERATE-MAGIC-LINK: Missing required fields');
      return NextResponse.json(
        { error: 'Email and userId are required' },
        { status: 400 }
      );
    }

    const supabase = createServiceRoleClient();
    console.log('🔗 GENERATE-MAGIC-LINK: Supabase client created');

    // Verify the user exists and get their details
    const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);

    if (userError || !userData.user) {
      console.error('🔗 GENERATE-MAGIC-LINK: User not found:', userError);
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    if (userData.user.email !== email) {
      console.error('🔗 GENERATE-MAGIC-LINK: Email mismatch');
      return NextResponse.json(
        { error: 'Email does not match user' },
        { status: 400 }
      );
    }

    console.log('🔗 GENERATE-MAGIC-LINK: User verified, generating magic link...');

    // Generate magic link for auto sign-in
    const { data: linkData, error: linkError } = await supabase.auth.admin.generateLink({
      type: 'magiclink',
      email: email,
      options: {
        redirectTo: redirectTo || '/dashboard'
      }
    });

    if (linkError) {
      console.error('🔗 GENERATE-MAGIC-LINK: Error generating magic link:', linkError);
      return NextResponse.json(
        { error: 'Failed to generate magic link' },
        { status: 500 }
      );
    }

    console.log('🔗 GENERATE-MAGIC-LINK: Magic link generated successfully');

    return NextResponse.json({
      success: true,
      magicLink: linkData.properties.action_link
    });

  } catch (error) {
    console.error('🔗 GENERATE-MAGIC-LINK: Exception:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
