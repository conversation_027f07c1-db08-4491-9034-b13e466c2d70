/**
 * Test endpoint for Semantic Cache functionality
 * This endpoint allows testing the semantic cache without going through the full chat completions flow
 */

import { NextRequest, NextResponse } from 'next/server';
import { semanticCache as semanticCacheService } from '@/lib/semantic-cache/SemanticCacheService';
import { getUserTier } from '@/lib/semantic-cache/tierUtils';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, userId, configId, promptText, responseData } = body;

    if (!userId || !configId) {
      return NextResponse.json({ error: 'userId and configId are required' }, { status: 400 });
    }

    const userTier = await getUserTier(userId);

    if (action === 'search') {
      if (!promptText) {
        return NextResponse.json({ error: 'promptText is required for search' }, { status: 400 });
      }

      console.log(`[Semantic Cache Test] Searching cache for user ${userId} (tier: ${userTier})`);
      
      const cacheHit = await semanticCacheService.searchCache(
        {
          promptText,
          modelUsed: 'test-model',
          providerUsed: 'test-provider',
          temperature: 0.7,
          maxTokens: 1000,
          metadata: { test: true }
        },
        userId,
        configId,
        userTier
      );

      if (cacheHit) {
        return NextResponse.json({
          success: true,
          cacheHit: true,
          data: {
            similarity: cacheHit.similarity,
            promptText: cacheHit.promptText,
            responseData: cacheHit.responseData,
            hitCount: cacheHit.hitCount,
            createdAt: cacheHit.createdAt
          }
        });
      } else {
        return NextResponse.json({
          success: true,
          cacheHit: false,
          message: 'No cache hit found'
        });
      }
    }

    if (action === 'store') {
      if (!promptText || !responseData) {
        return NextResponse.json({ error: 'promptText and responseData are required for store' }, { status: 400 });
      }

      console.log(`[Semantic Cache Test] Storing cache for user ${userId} (tier: ${userTier})`);

      const stored = await semanticCacheService.storeCache(
        {
          promptText,
          modelUsed: 'test-model',
          providerUsed: 'test-provider',
          temperature: 0.7,
          maxTokens: 1000,
          metadata: { test: true }
        },
        {
          responseData,
          tokensPrompt: 100,
          tokensCompletion: 200,
          cost: 0.001
        },
        userId,
        configId,
        userTier
      );

      return NextResponse.json({
        success: true,
        stored,
        message: stored ? 'Response stored in cache' : 'Failed to store in cache (tier restrictions or error)'
      });
    }

    if (action === 'stats') {
      console.log(`[Semantic Cache Test] Getting stats for user ${userId}`);

      const stats = await semanticCacheService.getCacheStats(userId, configId, 7);

      return NextResponse.json({
        success: true,
        stats,
        userTier
      });
    }

    if (action === 'cleanup') {
      console.log(`[Semantic Cache Test] Running cleanup`);

      const deletedCount = await semanticCacheService.cleanupExpiredCache();

      return NextResponse.json({
        success: true,
        deletedCount,
        message: `Cleaned up ${deletedCount} expired cache entries`
      });
    }

    return NextResponse.json({ error: 'Invalid action. Use: search, store, stats, or cleanup' }, { status: 400 });

  } catch (error: any) {
    console.error('[Semantic Cache Test] Error:', error);
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error.message 
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Semantic Cache Test Endpoint',
    usage: {
      'POST /api/test/semantic-cache': {
        description: 'Test semantic cache functionality',
        actions: {
          search: 'Search for cached responses',
          store: 'Store a response in cache',
          stats: 'Get cache statistics',
          cleanup: 'Clean up expired cache entries'
        },
        example: {
          action: 'search',
          userId: 'user-uuid',
          configId: 'config-uuid',
          promptText: 'Hello, how are you?'
        }
      }
    }
  });
}
