-- Create async_jobs table for handling long-running requests
CREATE TABLE IF NOT EXISTS public.async_jobs (
    id TEXT PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    api_key_id UUID NOT NULL REFERENCES public.user_generated_api_keys(id) ON DELETE CASCADE,
    status TEXT NOT NULL CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'timeout')),
    request_data JSONB NOT NULL,
    response_data JSONB,
    error_message TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    estimated_completion TIMESTAMPTZ,
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    roles_detected TEXT[],
    webhook_url TEXT
);

-- <PERSON>reate indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_async_jobs_user_id ON public.async_jobs(user_id);
CREATE INDEX IF NOT EXISTS idx_async_jobs_status ON public.async_jobs(status);
CREATE INDEX IF NOT EXISTS idx_async_jobs_created_at ON public.async_jobs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_async_jobs_api_key_id ON public.async_jobs(api_key_id);

-- Create composite index for user job queries
CREATE INDEX IF NOT EXISTS idx_async_jobs_user_status_created ON public.async_jobs(user_id, status, created_at DESC);

-- Enable RLS
ALTER TABLE public.async_jobs ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own async jobs" ON public.async_jobs
    FOR SELECT USING (
        auth.uid() = user_id OR
        -- Allow service role access
        auth.jwt() ->> 'role' = 'service_role'
    );

CREATE POLICY "Users can insert their own async jobs" ON public.async_jobs
    FOR INSERT WITH CHECK (
        auth.uid() = user_id OR
        -- Allow service role access
        auth.jwt() ->> 'role' = 'service_role'
    );

CREATE POLICY "Users can update their own async jobs" ON public.async_jobs
    FOR UPDATE USING (
        auth.uid() = user_id OR
        -- Allow service role access
        auth.jwt() ->> 'role' = 'service_role'
    );

CREATE POLICY "Users can delete their own async jobs" ON public.async_jobs
    FOR DELETE USING (
        auth.uid() = user_id OR
        -- Allow service role access
        auth.jwt() ->> 'role' = 'service_role'
    );

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_async_jobs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER trigger_async_jobs_updated_at
    BEFORE UPDATE ON public.async_jobs
    FOR EACH ROW
    EXECUTE FUNCTION public.update_async_jobs_updated_at();

-- Function to cleanup old completed/failed jobs (call periodically)
CREATE OR REPLACE FUNCTION public.cleanup_old_async_jobs(older_than_days INTEGER DEFAULT 7)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.async_jobs 
    WHERE created_at < (now() - (older_than_days || ' days')::INTERVAL)
    AND status IN ('completed', 'failed', 'timeout');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON public.async_jobs TO authenticated;
GRANT EXECUTE ON FUNCTION public.cleanup_old_async_jobs TO authenticated;
