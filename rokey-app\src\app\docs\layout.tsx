import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'RouKey Documentation - Complete API Guide & Integration Examples',
  description: 'Comprehensive documentation for RouKey AI gateway. Learn how to integrate, configure routing strategies, use API endpoints, and optimize your LLM usage with detailed examples and best practices.',
  keywords: 'RouKey documentation, AI gateway API, LLM routing, API integration, OpenAI compatible, intelligent routing, cost optimization, streaming responses',
  openGraph: {
    title: 'RouKey Documentation - Complete API Guide',
    description: 'Learn how to integrate RouKey AI gateway with detailed examples, routing strategies, and best practices for optimal LLM usage.',
    type: 'website',
    url: 'https://roukey.online/docs',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'RouKey Documentation - Complete API Guide',
    description: 'Learn how to integrate RouKey AI gateway with detailed examples, routing strategies, and best practices.',
  },
  robots: {
    index: true,
    follow: true,
  },
  alternates: {
    canonical: 'https://roukey.online/docs',
  },
};

export default function DocsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
