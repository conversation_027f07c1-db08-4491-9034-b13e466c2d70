// Phase 2B: Advanced Client-Side Caching System
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
  tags: string[];
  priority: 'low' | 'medium' | 'high';
}

interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  tags?: string[]; // Tags for cache invalidation
  priority?: 'low' | 'medium' | 'high';
  maxSize?: number; // Maximum cache size
  serialize?: boolean; // Whether to serialize data
}

class AdvancedCache {
  private cache = new Map<string, CacheEntry<any>>();
  private maxSize: number;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(maxSize = 100) {
    this.maxSize = maxSize;
    this.startCleanup();
  }

  // Set cache entry with advanced options
  set<T>(key: string, data: T, options: CacheOptions = {}): void {
    const {
      ttl = 300000, // 5 minutes default
      tags = [],
      priority = 'medium',
      serialize = false
    } = options;

    // Serialize data if requested (useful for complex objects)
    const cacheData = serialize ? JSON.parse(JSON.stringify(data)) : data;

    const entry: CacheEntry<T> = {
      data: cacheData,
      timestamp: Date.now(),
      ttl,
      accessCount: 0,
      lastAccessed: Date.now(),
      tags,
      priority
    };

    // Check if cache is full and evict if necessary
    if (this.cache.size >= this.maxSize) {
      this.evictLeastUsed();
    }

    this.cache.set(key, entry);
  }

  // Get cache entry with access tracking
  get<T>(key: string): T | null {
    const entry = this.cache.get(key) as CacheEntry<T> | undefined;
    
    if (!entry) {
      return null;
    }

    // Check if expired
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();

    return entry.data;
  }

  // Get with stale-while-revalidate pattern
  getStale<T>(key: string): { data: T | null; isStale: boolean } {
    const entry = this.cache.get(key) as CacheEntry<T> | undefined;
    
    if (!entry) {
      return { data: null, isStale: false };
    }

    const isStale = this.isExpired(entry);
    
    // Update access statistics even for stale data
    entry.accessCount++;
    entry.lastAccessed = Date.now();

    return { data: entry.data, isStale };
  }

  // Check if entry exists and is valid
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  // Delete specific entry
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  // Invalidate by tags
  invalidateByTags(tags: string[]): number {
    let invalidated = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags.some(tag => tags.includes(tag))) {
        this.cache.delete(key);
        invalidated++;
      }
    }
    
    return invalidated;
  }

  // Clear all cache
  clear(): void {
    this.cache.clear();
  }

  // Get cache statistics
  getStats() {
    const entries = Array.from(this.cache.values());
    const now = Date.now();
    
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.calculateHitRate(),
      averageAge: entries.reduce((sum, entry) => sum + (now - entry.timestamp), 0) / entries.length || 0,
      totalAccesses: entries.reduce((sum, entry) => sum + entry.accessCount, 0),
      expiredEntries: entries.filter(entry => this.isExpired(entry)).length,
      priorityDistribution: {
        high: entries.filter(e => e.priority === 'high').length,
        medium: entries.filter(e => e.priority === 'medium').length,
        low: entries.filter(e => e.priority === 'low').length
      }
    };
  }

  // Preload data with background refresh
  async preload<T>(
    key: string, 
    fetchFn: () => Promise<T>, 
    options: CacheOptions = {}
  ): Promise<T> {
    // Check if we have cached data
    const cached = this.get<T>(key);
    if (cached) {
      // Return cached data immediately, but refresh in background
      this.backgroundRefresh(key, fetchFn, options);
      return cached;
    }

    // No cached data, fetch and cache
    const data = await fetchFn();
    this.set(key, data, options);
    return data;
  }

  // Background refresh for stale-while-revalidate
  private async backgroundRefresh<T>(
    key: string,
    fetchFn: () => Promise<T>,
    options: CacheOptions
  ): Promise<void> {
    try {
      const freshData = await fetchFn();
      this.set(key, freshData, options);
    } catch (error) {
      console.warn(`Background refresh failed for key ${key}:`, error);
    }
  }

  // Check if entry is expired
  private isExpired(entry: CacheEntry<any>): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  // Evict least recently used entries
  private evictLeastUsed(): void {
    if (this.cache.size === 0) return;

    // Sort by priority first, then by last accessed time
    const entries = Array.from(this.cache.entries()).sort(([, a], [, b]) => {
      const priorityOrder = { low: 0, medium: 1, high: 2 };
      const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
      
      if (priorityDiff !== 0) return priorityDiff;
      
      return a.lastAccessed - b.lastAccessed;
    });

    // Remove the least important/oldest entry
    const [keyToRemove] = entries[0];
    this.cache.delete(keyToRemove);
  }

  // Calculate hit rate (simplified)
  private calculateHitRate(): number {
    const entries = Array.from(this.cache.values());
    const totalAccesses = entries.reduce((sum, entry) => sum + entry.accessCount, 0);
    return totalAccesses > 0 ? (this.cache.size / totalAccesses) * 100 : 0;
  }

  // Start automatic cleanup
  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60000); // Clean up every minute
  }

  // Clean up expired entries
  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    
    if (keysToDelete.length > 0) {
      console.log(`[Cache] Cleaned up ${keysToDelete.length} expired entries`);
    }
  }

  // Destroy cache and cleanup
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.cache.clear();
  }
}

// Global cache instance
export const globalCache = new AdvancedCache(200);

// React hook for cache management
export function useAdvancedCache() {
  return {
    set: globalCache.set.bind(globalCache),
    get: globalCache.get.bind(globalCache),
    getStale: globalCache.getStale.bind(globalCache),
    has: globalCache.has.bind(globalCache),
    delete: globalCache.delete.bind(globalCache),
    invalidateByTags: globalCache.invalidateByTags.bind(globalCache),
    clear: globalCache.clear.bind(globalCache),
    preload: globalCache.preload.bind(globalCache),
    getStats: globalCache.getStats.bind(globalCache)
  };
}

// Utility function for API caching
export async function cachedFetch<T>(
  url: string,
  options: RequestInit & { cacheKey?: string; cacheTTL?: number; tags?: string[] } = {}
): Promise<T> {
  const { cacheKey = url, cacheTTL = 300000, tags = [], ...fetchOptions } = options;
  
  // Try to get from cache first
  const cached = globalCache.get<T>(cacheKey);
  if (cached) {
    return cached;
  }

  // Fetch from network
  const response = await fetch(url, fetchOptions);
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  const data = await response.json();
  
  // Cache the result
  globalCache.set(cacheKey, data, {
    ttl: cacheTTL,
    tags,
    priority: 'medium'
  });

  return data;
}

// Export the cache instance
export { AdvancedCache };
