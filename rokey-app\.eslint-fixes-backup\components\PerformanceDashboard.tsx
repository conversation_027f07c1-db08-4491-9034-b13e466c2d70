'use client';

import React, { useState, useEffect } from 'react';
import { usePerformance } from '@/contexts/PerformanceContext';
import { ChartBarIcon, ClockIcon, ServerIcon, CpuChipIcon } from '@heroicons/react/24/outline';

interface PerformanceMetrics {
  navigationTime: number;
  renderTime: number;
  cacheHitRate: number;
  memoryUsage: number;
  connectionType: string;
}

export const PerformanceDashboard: React.FC = () => {
  const { navigationTime, cacheHitRate, isOptimized } = usePerformance();
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    navigationTime: 0,
    renderTime: 0,
    cacheHitRate: 0,
    memoryUsage: 0,
    connectionType: 'unknown'
  });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const updateMetrics = () => {
      const newMetrics: PerformanceMetrics = {
        navigationTime,
        renderTime: performance.now(),
        cacheHitRate,
        memoryUsage: (performance as any).memory?.usedJSHeapSize || 0,
        connectionType: (navigator as any).connection?.effectiveType || 'unknown'
      };
      setMetrics(newMetrics);
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 5000);
    return () => clearInterval(interval);
  }, [navigationTime, cacheHitRate]);

  // Show/hide with keyboard shortcut
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        setIsVisible(!isVisible);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isVisible]);

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsVisible(true)}
          className="bg-gray-900 text-white p-2 rounded-lg shadow-lg hover:bg-gray-800 transition-colors"
          title="Show Performance Dashboard (Ctrl+Shift+P)"
        >
          <ChartBarIcon className="w-5 h-5" />
        </button>
      </div>
    );
  }

  const getPerformanceColor = (value: number, thresholds: { good: number; fair: number }) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.fair) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white rounded-lg shadow-xl border border-gray-200 p-4 w-80">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <ChartBarIcon className="w-5 h-5 mr-2" />
          Performance
        </h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-gray-600 transition-colors"
        >
          ×
        </button>
      </div>

      <div className="space-y-4">
        {/* Optimization Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Optimizations</span>
          <span className={`text-sm font-medium ${isOptimized ? 'text-green-600' : 'text-red-600'}`}>
            {isOptimized ? 'Active' : 'Inactive'}
          </span>
        </div>

        {/* Navigation Time */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 flex items-center">
            <ClockIcon className="w-4 h-4 mr-1" />
            Navigation
          </span>
          <span className={`text-sm font-medium ${getPerformanceColor(metrics.navigationTime, { good: 500, fair: 1000 })}`}>
            {metrics.navigationTime.toFixed(0)}ms
          </span>
        </div>

        {/* Cache Hit Rate */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 flex items-center">
            <ServerIcon className="w-4 h-4 mr-1" />
            Cache Hit Rate
          </span>
          <span className={`text-sm font-medium ${getPerformanceColor(100 - metrics.cacheHitRate, { good: 30, fair: 50 })}`}>
            {metrics.cacheHitRate.toFixed(1)}%
          </span>
        </div>

        {/* Memory Usage */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 flex items-center">
            <CpuChipIcon className="w-4 h-4 mr-1" />
            Memory
          </span>
          <span className="text-sm font-medium text-gray-900">
            {formatBytes(metrics.memoryUsage)}
          </span>
        </div>

        {/* Connection Type */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Connection</span>
          <span className="text-sm font-medium text-gray-900 uppercase">
            {metrics.connectionType}
          </span>
        </div>

        {/* Performance Score */}
        <div className="pt-2 border-t border-gray-200">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-900">Performance Score</span>
            <span className={`text-lg font-bold ${getPerformanceScore() >= 90 ? 'text-green-600' : getPerformanceScore() >= 70 ? 'text-yellow-600' : 'text-red-600'}`}>
              {getPerformanceScore()}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                getPerformanceScore() >= 90 ? 'bg-green-500' : 
                getPerformanceScore() >= 70 ? 'bg-yellow-500' : 'bg-red-500'
              }`}
              style={{ width: `${getPerformanceScore()}%` }}
            />
          </div>
        </div>
      </div>

      <div className="mt-4 text-xs text-gray-500 text-center">
        Press Ctrl+Shift+P to toggle
      </div>
    </div>
  );

  function getPerformanceScore(): number {
    let score = 100;
    
    // Navigation time penalty
    if (metrics.navigationTime > 500) score -= 20;
    if (metrics.navigationTime > 1000) score -= 20;
    if (metrics.navigationTime > 2000) score -= 20;
    
    // Cache hit rate bonus/penalty
    score += (metrics.cacheHitRate - 50) * 0.4;
    
    // Optimization bonus
    if (isOptimized) score += 10;
    
    return Math.max(0, Math.min(100, Math.round(score)));
  }
};

// Mini performance indicator for production
export const PerformanceIndicator: React.FC = () => {
  const { navigationTime, isOptimized } = usePerformance();
  const [showDetails, setShowDetails] = useState(false);

  if (process.env.NODE_ENV !== 'development') return null;

  const getIndicatorColor = () => {
    if (!isOptimized) return 'bg-red-500';
    if (navigationTime < 500) return 'bg-green-500';
    if (navigationTime < 1000) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="fixed top-4 right-4 z-50">
      <div
        className={`w-3 h-3 rounded-full ${getIndicatorColor()} cursor-pointer transition-all duration-200 hover:scale-125`}
        onClick={() => setShowDetails(!showDetails)}
        title={`Performance: ${navigationTime.toFixed(0)}ms | Optimized: ${isOptimized ? 'Yes' : 'No'}`}
      />
      {showDetails && (
        <div className="absolute top-6 right-0 bg-black text-white text-xs p-2 rounded shadow-lg whitespace-nowrap">
          <div>Navigation: {navigationTime.toFixed(0)}ms</div>
          <div>Optimized: {isOptimized ? 'Yes' : 'No'}</div>
        </div>
      )}
    </div>
  );
};

export default PerformanceDashboard;
