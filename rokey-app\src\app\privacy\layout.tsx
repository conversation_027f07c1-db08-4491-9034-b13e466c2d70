import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Privacy Policy - RouKey',
  description: 'Learn how Rou<PERSON>ey protects your data and privacy. Comprehensive privacy policy covering data collection, usage, and your rights.',
  keywords: ['privacy policy', 'data protection', 'GDPR', 'CCPA', 'AI routing', 'RouKey'],
  openGraph: {
    title: 'Privacy Policy - RouKey',
    description: 'Learn how RouKey protects your data and privacy. Comprehensive privacy policy covering data collection, usage, and your rights.',
    type: 'website',
    url: 'https://roukey.online/privacy',
    images: [
      {
        url: 'https://roukey.online/og-privacy.jpg',
        width: 1200,
        height: 630,
        alt: 'RouKey Privacy Policy',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Privacy Policy - RouKey',
    description: 'Learn how <PERSON><PERSON><PERSON><PERSON> protects your data and privacy with enterprise-grade security.',
    images: ['https://roukey.online/og-privacy.jpg'],
  },
  alternates: {
    canonical: 'https://roukey.online/privacy',
  },
};

export default function PrivacyLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
