const fs = require('fs');
const path = require('path');

// Function to recursively find all route.ts files
function findRouteFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      findRouteFiles(fullPath, files);
    } else if (item === 'route.ts') {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Function to fix route parameters in a file
function fixRouteParams(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Pattern 1: Fix interface RouteParams with non-Promise params
  const interfacePattern = /interface RouteParams \{[\s\S]*?params: \{[\s\S]*?\};[\s\S]*?\}/g;
  content = content.replace(interfacePattern, (match) => {
    if (!match.includes('Promise<{')) {
      modified = true;
      return match.replace(/params: \{/, 'params: Promise<{').replace(/\};/, '}>;');
    }
    return match;
  });
  
  // Pattern 2: Fix destructuring of params
  const destructurePattern = /const \{ ([^}]+) \} = params;/g;
  content = content.replace(destructurePattern, (match, variables) => {
    modified = true;
    return `const { ${variables} } = await params;`;
  });
  
  // Pattern 3: Fix direct params access
  const directAccessPattern = /params\.(\w+)/g;
  content = content.replace(directAccessPattern, (match, prop) => {
    // Only replace if it's not already awaited
    if (!content.includes(`await params.${prop}`) && !content.includes(`(await params).${prop}`)) {
      modified = true;
      return `(await params).${prop}`;
    }
    return match;
  });
  
  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Fixed: ${filePath}`);
    return true;
  }
  
  return false;
}

// Main execution
console.log('🔧 Fixing Next.js 15 route parameter types...\n');

const apiDir = path.join(__dirname, 'src', 'app', 'api');
if (!fs.existsSync(apiDir)) {
  console.error('❌ API directory not found:', apiDir);
  process.exit(1);
}

const routeFiles = findRouteFiles(apiDir);
console.log(`Found ${routeFiles.length} route files\n`);

let fixedCount = 0;
for (const file of routeFiles) {
  if (fixRouteParams(file)) {
    fixedCount++;
  }
}

console.log(`\n🎉 Fixed ${fixedCount} out of ${routeFiles.length} route files`);
console.log('✅ All route files are now compatible with Next.js 15!');
