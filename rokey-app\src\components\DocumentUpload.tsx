'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Upload, File, X, CheckCircle, AlertCircle, FileText, FileImage, Loader2 } from 'lucide-react';
import { useSubscription } from '@/hooks/useSubscription';
import { getTierConfig, SubscriptionTier } from '@/lib/stripe-client';
import { LimitIndicator } from '@/components/TierEnforcement';
import { useConfirmation } from '@/hooks/useConfirmation';
import ConfirmationModal from '@/components/ui/ConfirmationModal';

interface Document {
  id: string;
  filename: string;
  file_type: string;
  file_size: number;
  status: 'processing' | 'completed' | 'failed';
  chunks_count: number;
  created_at: string;
}

interface DocumentUploadProps {
  configId: string;
  onDocumentUploaded?: () => void;
  onDocumentDeleted?: () => void;
  theme?: 'light' | 'dark';
}

export default function DocumentUpload({ configId, onDocumentUploaded, onDocumentDeleted, theme = 'light' }: DocumentUploadProps) {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const confirmation = useConfirmation();

  // Load documents for the current config with retry logic
  const loadDocuments = useCallback(async (retryCount = 0, expectedDocumentId?: string) => {
    if (!configId) return;

    if (retryCount === 0) {
      setIsRefreshing(true);
    }

    try {
      const response = await fetch(`/api/documents/list?configId=${configId}`);
      if (response.ok) {
        const data = await response.json();
        const newDocuments = data.documents || [];

        // If we're looking for a specific document and it's not found, retry
        if (expectedDocumentId && retryCount < 3) {
          const foundDocument = newDocuments.find((doc: Document) => doc.id === expectedDocumentId);
          if (!foundDocument) {
            console.log(`[DocumentUpload] Document ${expectedDocumentId} not found, retrying in ${(retryCount + 1) * 500}ms...`);
            setTimeout(() => {
              loadDocuments(retryCount + 1, expectedDocumentId);
            }, (retryCount + 1) * 500); // 500ms, 1s, 1.5s delays
            return;
          }
        }

        // Smart merge: preserve optimistic updates until server confirms them
        setDocuments(prev => {
          // If we're looking for a specific document (after upload), handle optimistic updates
          if (expectedDocumentId) {
            const serverDoc = newDocuments.find((doc: Document) => doc.id === expectedDocumentId);
            if (serverDoc) {
              // Server has the document, use server data (it's authoritative)
              console.log(`[DocumentUpload] Server confirmed document ${expectedDocumentId}, using server data`);
              return newDocuments.sort((a: Document, b: Document) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
            } else {
              // Server doesn't have the document yet, preserve optimistic version
              console.log(`[DocumentUpload] Server doesn't have document ${expectedDocumentId} yet, preserving optimistic version`);
              const serverDocIds = new Set(newDocuments.map((doc: Document) => doc.id));
              const optimisticDocs = prev.filter(doc => !serverDocIds.has(doc.id));
              const allDocs = [...newDocuments, ...optimisticDocs];
              return allDocs.sort((a: Document, b: Document) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
            }
          } else {
            // Regular refresh (page load, config change), just use server data
            return newDocuments.sort((a: Document, b: Document) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
          }
        });
      }
    } catch (err) {
      console.error('Failed to load documents:', err);

      // Retry on error if we haven't exceeded retry count
      if (retryCount < 2) {
        setTimeout(() => {
          loadDocuments(retryCount + 1, expectedDocumentId);
        }, 1000);
      }
    } finally {
      if (retryCount === 0 || expectedDocumentId) {
        setIsRefreshing(false);
      }
    }
  }, [configId]);

  // Load documents when configId changes
  React.useEffect(() => {
    loadDocuments();
  }, [loadDocuments]);

  // Handle file upload
  const handleFileUpload = async (files: FileList) => {
    if (!configId) {
      setError('Please select an API configuration first');
      return;
    }

    const file = files[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'text/plain',
      'text/markdown'
    ];

    if (!allowedTypes.includes(file.type)) {
      setError('Please upload PDF, TXT, or MD files only');
      return;
    }

    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
      setError('File size must be less than 10MB');
      return;
    }

    setIsUploading(true);
    setError(null);
    setSuccess(null);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('configId', configId);

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const response = await fetch('/api/documents/upload', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();
      console.log(`[DocumentUpload] Upload successful:`, result);
      setSuccess(`✨ ${file.name} uploaded successfully! Processing ${result.document.chunks_total} chunks.`);

      // Optimistically add the document to the list immediately
      const optimisticDocument: Document = {
        id: result.document.id,
        filename: result.document.filename || file.name,
        file_type: file.type,
        file_size: file.size,
        status: (result.document.status as 'processing' | 'completed' | 'failed') || 'processing',
        chunks_count: result.document.chunks_processed || 0,
        created_at: new Date().toISOString()
      };

      console.log(`[DocumentUpload] Adding optimistic document:`, optimisticDocument);
      setDocuments(prev => {
        // Check if document already exists (shouldn't happen, but safety check)
        const exists = prev.find(doc => doc.id === optimisticDocument.id);
        if (exists) {
          console.log(`[DocumentUpload] Document ${optimisticDocument.id} already exists, updating instead`);
          return prev.map(doc => doc.id === optimisticDocument.id ? optimisticDocument : doc);
        }
        return [optimisticDocument, ...prev];
      });

      // Small delay to allow database transaction to commit, then reload with retry logic
      setTimeout(async () => {
        console.log(`[DocumentUpload] Starting document list refresh for document: ${result.document.id}`);
        await loadDocuments(0, result.document.id);
      }, 200);

      // Call callback if provided
      onDocumentUploaded?.();

    } catch (err: any) {
      const errorMessage = `Upload failed: ${err.message}`;
      setError(errorMessage);

      // Auto-clear error message after 8 seconds
      setTimeout(() => setError(null), 8000);
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
      
      // Clear file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      // Auto-clear success message after 5 seconds
      if (success) {
        setTimeout(() => setSuccess(null), 5000);
      }
    }
  };

  // Handle drag and drop
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files);
    }
  }, [configId]);

  // Handle file input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileUpload(e.target.files);
    }
  };

  // Delete document
  const handleDeleteDocument = (documentId: string, documentName: string) => {
    confirmation.showConfirmation(
      {
        title: 'Delete Document',
        message: `Are you sure you want to delete "${documentName}"? This will permanently remove the document and all its processed chunks from your knowledge base. This action cannot be undone.`,
        confirmText: 'Delete Document',
        cancelText: 'Cancel',
        type: 'danger'
      },
      async () => {
        // Optimistically remove the document from the list
        const originalDocuments = documents;
        setDocuments(prev => prev.filter(doc => doc.id !== documentId));

        try {
          console.log(`[DocumentUpload] Attempting to delete document: ${documentId}`);
          const response = await fetch(`/api/documents/${documentId}`, {
            method: 'DELETE',
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
            console.error(`[DocumentUpload] Delete failed:`, response.status, errorData);
            // Restore the original list on error
            setDocuments(originalDocuments);
            throw new Error(errorData.error || `HTTP ${response.status}: Failed to delete document`);
          }

          const result = await response.json();
          console.log(`[DocumentUpload] Delete successful:`, result);
          setSuccess('Document deleted successfully');

          // Force refresh the document list to ensure consistency
          setTimeout(async () => {
            console.log(`[DocumentUpload] Refreshing document list after deletion`);
            await loadDocuments();
          }, 200);

          // Call callback if provided
          onDocumentDeleted?.();

          // Auto-clear success message after 3 seconds
          setTimeout(() => setSuccess(null), 3000);
        } catch (err: any) {
          console.error(`[DocumentUpload] Delete error:`, err);
          // Restore the original list on error
          setDocuments(originalDocuments);
          const errorMessage = `Delete failed: ${err.message}`;
          setError(errorMessage);

          // Auto-clear error message after 8 seconds
          setTimeout(() => setError(null), 8000);
        }
      }
    );
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get file icon
  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) return <FileText className="w-5 h-5 text-red-500" />;
    if (fileType.includes('word')) return <FileText className="w-5 h-5 text-blue-500" />;
    return <File className="w-5 h-5 text-gray-500" />;
  };

  return (
    <div className="space-y-6">
      {/* Error/Success Messages */}
      {error && (
        <div className={`rounded-xl p-4 ${
          theme === 'dark'
            ? 'bg-red-900/20 border border-red-500/30'
            : 'bg-red-50 border border-red-200'
        }`}>
          <div className="flex items-center space-x-2">
            <AlertCircle className={`w-5 h-5 ${theme === 'dark' ? 'text-red-400' : 'text-red-600'}`} />
            <p className={`text-sm font-medium ${theme === 'dark' ? 'text-red-200' : 'text-red-800'}`}>{error}</p>
          </div>
        </div>
      )}

      {success && (
        <div className={`rounded-xl p-4 ${
          theme === 'dark'
            ? 'bg-green-900/20 border border-green-500/30'
            : 'bg-green-50 border border-green-200'
        }`}>
          <div className="flex items-center space-x-2">
            <CheckCircle className={`w-5 h-5 ${theme === 'dark' ? 'text-green-400' : 'text-green-600'}`} />
            <p className={`text-sm font-medium ${theme === 'dark' ? 'text-green-200' : 'text-green-800'}`}>{success}</p>
          </div>
        </div>
      )}

      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 transform ${
          dragActive
            ? theme === 'dark'
              ? 'border-orange-400 bg-orange-900/20 scale-105 shadow-lg'
              : 'border-orange-400 bg-orange-50 scale-105 shadow-lg'
            : theme === 'dark'
              ? 'border-gray-600 hover:border-orange-400 hover:bg-orange-900/10 hover:scale-102 hover:shadow-md'
              : 'border-gray-300 hover:border-orange-400 hover:bg-orange-50 hover:scale-102 hover:shadow-md'
        } ${!configId ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => configId && fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          accept=".pdf,.txt,.md"
          onChange={handleInputChange}
          disabled={!configId || isUploading}
        />

        {isUploading ? (
          <div className="space-y-4">
            <Loader2 className="w-12 h-12 text-orange-500 mx-auto animate-spin" />
            <div className="space-y-2">
              <p className={`text-lg font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>Processing Document...</p>
              <div className={`w-full rounded-full h-2 ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'}`}>
                <div
                  className="bg-orange-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                />
              </div>
              <p className={`text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>{uploadProgress}% complete</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <Upload className={`w-12 h-12 mx-auto ${theme === 'dark' ? 'text-gray-500' : 'text-gray-400'}`} />
            <div>
              <p className={`text-lg font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                {configId ? 'Upload Knowledge Documents' : 'Select a configuration first'}
              </p>
              <p className={`text-sm mt-1 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
                Drag and drop files here, or click to browse
              </p>
              <p className={`text-xs mt-2 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                Supports PDF, TXT, MD files up to 10MB
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Documents List */}
      {documents.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className={`text-lg font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>Uploaded Documents</h3>
            {isRefreshing && (
              <div className={`flex items-center space-x-2 text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Refreshing...</span>
              </div>
            )}
          </div>
          <div className="grid gap-4">
            {documents.map((doc) => (
              <div
                key={doc.id}
                className={`flex items-center justify-between p-4 rounded-xl transition-shadow ${
                  theme === 'dark'
                    ? 'bg-gray-800/50 border border-gray-700/50 hover:shadow-lg hover:shadow-gray-900/20'
                    : 'bg-white border border-gray-200 hover:shadow-md'
                }`}
              >
                <div className="flex items-center space-x-3">
                  {getFileIcon(doc.file_type)}
                  <div>
                    <p className={`font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>{doc.filename}</p>
                    <p className={`text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
                      {formatFileSize(doc.file_size)} • {doc.chunks_count} chunks
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    {doc.status === 'completed' && (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    )}
                    {doc.status === 'processing' && (
                      <Loader2 className="w-5 h-5 text-orange-500 animate-spin" />
                    )}
                    {doc.status === 'failed' && (
                      <AlertCircle className="w-5 h-5 text-red-500" />
                    )}
                    <span className={`text-sm font-medium ${
                      doc.status === 'completed' ? 'text-green-600' :
                      doc.status === 'processing' ? 'text-orange-600' :
                      'text-red-600'
                    }`}>
                      {doc.status === 'completed' ? 'Ready' :
                       doc.status === 'processing' ? 'Processing' :
                       'Failed'}
                    </span>
                  </div>
                  
                  <button
                    onClick={() => handleDeleteDocument(doc.id, doc.filename)}
                    className={`p-1 transition-colors ${
                      theme === 'dark'
                        ? 'text-gray-500 hover:text-red-400'
                        : 'text-gray-400 hover:text-red-500'
                    }`}
                    title="Delete document"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmation.isOpen}
        onClose={confirmation.hideConfirmation}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        type={confirmation.type}
        isLoading={confirmation.isLoading}
      />
    </div>
  );
}
