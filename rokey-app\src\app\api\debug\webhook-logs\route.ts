import { NextRequest, NextResponse } from 'next/server';

// Simple in-memory store for webhook logs (in production, you'd use a database)
let webhookLogs: Array<{
  id: string;
  timestamp: string;
  eventType: string;
  subscriptionId?: string;
  customerId?: string;
  priceId?: string;
  tier?: string;
  status?: string;
  details: any;
}> = [];

export async function GET() {
  // Return the last 20 webhook events
  const recentLogs = webhookLogs
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
    .slice(0, 20);

  return NextResponse.json({
    total: webhookLogs.length,
    recent: recentLogs,
    lastUpdated: new Date().toISOString()
  });
}

export async function POST(req: NextRequest) {
  try {
    const logEntry = await req.json();
    
    // Add timestamp and ID
    const entry = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      ...logEntry
    };

    // Add to logs (keep only last 100 entries)
    webhookLogs.push(entry);
    if (webhookLogs.length > 100) {
      webhookLogs = webhookLogs.slice(-100);
    }

    console.log('📝 Webhook log entry added:', entry);

    return NextResponse.json({ success: true, id: entry.id });
  } catch (error) {
    console.error('Error adding webhook log:', error);
    return NextResponse.json(
      { error: 'Failed to add log entry' },
      { status: 500 }
    );
  }
}

export async function DELETE() {
  // Clear all logs
  webhookLogs = [];
  return NextResponse.json({ success: true, message: 'All webhook logs cleared' });
}
