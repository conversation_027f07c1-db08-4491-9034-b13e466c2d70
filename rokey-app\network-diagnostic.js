#!/usr/bin/env node

/**
 * Network Connectivity Diagnostic Tool for RoKey App
 * Tests connectivity to all external APIs used by the application
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

// Test endpoints
const endpoints = [
  {
    name: 'OpenRouter API',
    url: 'https://openrouter.ai/api/v1/chat/completions',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer test-key',
      'User-Agent': 'RoKey/1.0 (Diagnostic)'
    }
  },
  {
    name: 'Google Gemini OpenAI API',
    url: 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer test-key',
      'User-Agent': 'RoKey/1.0 (Diagnostic)'
    }
  },
  {
    name: 'OpenAI API',
    url: 'https://api.openai.com/v1/chat/completions',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer test-key',
      'User-Agent': 'RoKey/1.0 (Diagnostic)'
    }
  },
  {
    name: 'Anthropic API',
    url: 'https://api.anthropic.com/v1/messages',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': 'test-key',
      'anthropic-version': '2023-06-01',
      'User-Agent': 'RoKey/1.0 (Diagnostic)'
    }
  },
  {
    name: 'DeepSeek API',
    url: 'https://api.deepseek.com/chat/completions',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer test-key',
      'User-Agent': 'RoKey/1.0 (Diagnostic)'
    }
  },
  {
    name: 'XAI/Grok API',
    url: 'https://api.x.ai/v1/chat/completions',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer test-key',
      'User-Agent': 'RoKey/1.0 (Diagnostic)'
    }
  }
];

// Simple connectivity test
function testConnectivity(endpoint) {
  return new Promise((resolve) => {
    const url = new URL(endpoint.url);
    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: url.pathname + url.search,
      method: endpoint.method,
      headers: endpoint.headers,
      timeout: 10000
    };

    const startTime = Date.now();
    const client = url.protocol === 'https:' ? https : http;
    
    const req = client.request(options, (res) => {
      const duration = Date.now() - startTime;
      resolve({
        success: true,
        status: res.statusCode,
        duration,
        error: null
      });
    });

    req.on('error', (error) => {
      const duration = Date.now() - startTime;
      resolve({
        success: false,
        status: null,
        duration,
        error: error.message
      });
    });

    req.on('timeout', () => {
      req.destroy();
      const duration = Date.now() - startTime;
      resolve({
        success: false,
        status: null,
        duration,
        error: 'Request timeout (10s)'
      });
    });

    // Send minimal test payload
    if (endpoint.method === 'POST') {
      req.write(JSON.stringify({ test: true }));
    }
    req.end();
  });
}

// DNS resolution test
function testDNS(hostname) {
  return new Promise((resolve) => {
    const dns = require('dns');
    dns.lookup(hostname, (err, address) => {
      if (err) {
        resolve({ success: false, error: err.message, address: null });
      } else {
        resolve({ success: true, error: null, address });
      }
    });
  });
}

// Main diagnostic function
async function runDiagnostics() {
  console.log('🔍 RoKey Network Connectivity Diagnostic');
  console.log('==========================================\n');

  // Test basic internet connectivity
  console.log('📡 Testing basic internet connectivity...');
  const googleTest = await testConnectivity({
    name: 'Google',
    url: 'https://www.google.com',
    method: 'GET',
    headers: { 'User-Agent': 'RoKey/1.0 (Diagnostic)' }
  });

  if (googleTest.success) {
    console.log('✅ Internet connectivity: OK');
  } else {
    console.log('❌ Internet connectivity: FAILED');
    console.log(`   Error: ${googleTest.error}`);
    console.log('\n🚨 No internet connectivity detected. Please check:');
    console.log('   - Network connection');
    console.log('   - Firewall settings');
    console.log('   - Proxy configuration');
    return;
  }

  console.log('\n🌐 Testing DNS resolution...');
  const dnsTests = [
    'openrouter.ai',
    'generativelanguage.googleapis.com',
    'api.openai.com',
    'api.anthropic.com',
    'api.deepseek.com',
    'api.x.ai'
  ];

  for (const hostname of dnsTests) {
    const dnsResult = await testDNS(hostname);
    if (dnsResult.success) {
      console.log(`✅ ${hostname} → ${dnsResult.address}`);
    } else {
      console.log(`❌ ${hostname} → ${dnsResult.error}`);
    }
  }

  console.log('\n🔌 Testing API endpoint connectivity...');
  for (const endpoint of endpoints) {
    console.log(`\nTesting ${endpoint.name}...`);
    const result = await testConnectivity(endpoint);
    
    if (result.success) {
      console.log(`✅ ${endpoint.name}: Connected (${result.duration}ms, HTTP ${result.status})`);
    } else {
      console.log(`❌ ${endpoint.name}: Failed (${result.duration}ms)`);
      console.log(`   Error: ${result.error}`);
    }
  }

  console.log('\n📋 Diagnostic Summary');
  console.log('=====================');
  console.log('If you see connection failures above, possible causes:');
  console.log('1. 🔥 Firewall blocking outbound HTTPS connections');
  console.log('2. 🌐 Corporate proxy requiring configuration');
  console.log('3. 🛡️ Antivirus software blocking Node.js network access');
  console.log('4. 🏢 Network policy restrictions');
  console.log('5. 📡 ISP or regional blocking of AI APIs');
  
  console.log('\n🔧 Suggested fixes:');
  console.log('1. Check Windows Firewall settings');
  console.log('2. Configure proxy settings if behind corporate firewall');
  console.log('3. Add Node.js to antivirus exceptions');
  console.log('4. Try running from different network (mobile hotspot)');
  console.log('5. Contact network administrator if in corporate environment');
}

// Run diagnostics
runDiagnostics().catch(console.error);
