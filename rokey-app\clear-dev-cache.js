#!/usr/bin/env node

/**
 * Clear Next.js development cache script
 * Run this when experiencing 307 redirect loops or other caching issues
 */

const fs = require('fs');
const path = require('path');

function deleteFolderRecursive(folderPath) {
  if (fs.existsSync(folderPath)) {
    fs.readdirSync(folderPath).forEach((file) => {
      const curPath = path.join(folderPath, file);
      if (fs.lstatSync(curPath).isDirectory()) {
        deleteFolderRecursive(curPath);
      } else {
        fs.unlinkSync(curPath);
      }
    });
    fs.rmdirSync(folderPath);
  }
}

console.log('🧹 Clearing Next.js development cache...');

const cachePaths = [
  '.next',
  'node_modules/.cache',
  '.next/cache',
  '.next/server',
  '.next/static'
];

let clearedCount = 0;

cachePaths.forEach(cachePath => {
  const fullPath = path.join(process.cwd(), cachePath);
  if (fs.existsSync(fullPath)) {
    try {
      deleteFolderRecursive(fullPath);
      console.log(`✅ Cleared: ${cachePath}`);
      clearedCount++;
    } catch (error) {
      console.log(`⚠️  Could not clear ${cachePath}: ${error.message}`);
    }
  } else {
    console.log(`ℹ️  Not found: ${cachePath}`);
  }
});

console.log(`\n🎉 Cache clearing complete! Cleared ${clearedCount} cache directories.`);
console.log('\n💡 Now run: npm run dev');
console.log('\n📝 If you still experience issues, try:');
console.log('   1. Close your browser completely');
console.log('   2. Clear browser cache (Ctrl+Shift+Delete)');
console.log('   3. Restart the dev server');
