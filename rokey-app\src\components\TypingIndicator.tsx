'use client';

import React from 'react';
import { 
  CpuChipIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

interface TypingIndicatorProps {
  senderName: string;
  roleId?: string;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({ senderName, roleId }) => {
  const getRoleColor = (roleId?: string) => {
    if (!roleId || roleId === 'moderator') return 'from-blue-500 to-blue-600'; // Moderator
    
    // Generate consistent colors based on role name
    const colors = [
      'from-green-500 to-green-600',
      'from-purple-500 to-purple-600',
      'from-orange-500 to-orange-600',
      'from-pink-500 to-pink-600',
      'from-indigo-500 to-indigo-600',
      'from-teal-500 to-teal-600',
      'from-red-500 to-red-600',
      'from-yellow-500 to-yellow-600',
    ];
    
    const hash = roleId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  const getRoleIcon = (roleId?: string) => {
    if (!roleId || roleId === 'moderator') {
      return <SparklesIcon className="w-4 h-4" />;
    }
    return <CpuChipIcon className="w-4 h-4" />;
  };

  const getTypingMessage = (senderName: string) => {
    const messages = [
      'is thinking...',
      'is working on this...',
      'is analyzing...',
      'is processing...',
      'is crafting a response...',
    ];
    
    // Use sender name to consistently pick a message
    const hash = senderName.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return messages[hash % messages.length];
  };

  const roleColor = getRoleColor(roleId);
  const isFromModerator = !roleId || roleId === 'moderator';

  return (
    <div className="flex justify-start mb-4 opacity-75">
      <div className="flex items-start space-x-3 max-w-[85%]">
        {/* Avatar */}
        <div className={`flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ${roleColor} flex items-center justify-center text-white shadow-sm animate-pulse`}>
          {getRoleIcon(roleId)}
        </div>

        {/* Typing Content */}
        <div className="flex-1 min-w-0">
          {/* Sender Info */}
          <div className="flex items-center space-x-2 mb-1">
            <span className={`text-sm font-semibold ${
              isFromModerator ? 'text-blue-700' : 'text-gray-700'
            }`}>
              {senderName}
            </span>
            <span className="text-xs text-gray-500">
              {getTypingMessage(senderName)}
            </span>
          </div>

          {/* Typing Bubble */}
          <div className={`inline-block px-4 py-3 rounded-2xl shadow-sm ${
            isFromModerator 
              ? 'bg-blue-50 border border-blue-100' 
              : 'bg-gray-50 border border-gray-100'
          }`}>
            <div className="flex items-center space-x-1">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
