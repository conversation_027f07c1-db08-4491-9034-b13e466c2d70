export default function TrainingLoading() {
  return (
    <div className="min-h-screen bg-[#faf8f5] p-6">
      <div className="max-w-6xl mx-auto">
        <div className="animate-pulse space-y-8">
          {/* Header */}
          <div className="space-y-4">
            <div className="h-10 bg-gray-200 rounded w-1/3"></div>
            <div className="h-6 bg-gray-200 rounded w-2/3"></div>
          </div>

          {/* Form */}
          <div className="bg-white rounded-2xl shadow-lg p-8 space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-4">
              <div className="h-12 bg-gray-200 rounded"></div>
              <div className="h-32 bg-gray-200 rounded"></div>
              <div className="h-12 bg-gray-200 rounded w-1/3"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
