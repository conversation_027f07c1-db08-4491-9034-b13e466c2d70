// RouKey Service Worker - Advanced Caching Strategy
const CACHE_VERSION = Date.now(); // Use timestamp for cache busting
const CACHE_NAME = `roukey-v${CACHE_VERSION}`;
const STATIC_CACHE = `roukey-static-v${CACHE_VERSION}`;
const API_CACHE = `roukey-api-v${CACHE_VERSION}`;
const IMAGE_CACHE = `roukey-images-v${CACHE_VERSION}`;

// Development mode detection
const isDevelopment = self.location.hostname === 'localhost' ||
                     self.location.hostname === '127.0.0.1' ||
                     self.location.hostname === '[::1]';

// Cache strategies for different resource types
const CACHE_STRATEGIES = {
  // Static assets - cache first, network fallback
  static: [
    '/',
    '/pricing',
    '/features',
    '/auth/signin',
    '/auth/signup',
    '/manifest.json',
    '/roukey_logo.png',
    '/openai_logo.jpg',
    '/claude_logo.png',
    '/gemini_logo.png',
    '/deepseek_logo.png',
    '/mistral_logo.png'
  ],
  
  // API responses - network first, cache fallback
  api: [
    '/api/system-status',
    '/api/custom-configs'
  ],
  
  // Images - cache first
  images: /\.(png|jpg|jpeg|svg|webp|gif)$/,
  
  // Fonts and CSS - cache first
  assets: /\.(css|js|woff|woff2|ttf|eot)$/
};

// Install event - cache critical resources
self.addEventListener('install', (event) => {
  console.log('🔧 Service Worker installing...');
  
  event.waitUntil(
    Promise.all([
      // Cache static pages
      caches.open(STATIC_CACHE).then((cache) => {
        console.log('📦 Caching static resources...');
        return cache.addAll(CACHE_STRATEGIES.static);
      }),
      
      // Cache images
      caches.open(IMAGE_CACHE).then((cache) => {
        console.log('🖼️ Caching images...');
        return cache.addAll([
          '/roukey_logo.png',
          '/openai_logo.jpg',
          '/claude_logo.png',
          '/gemini_logo.png',
          '/deepseek_logo.png',
          '/mistral_logo.png'
        ]);
      })
    ]).then(() => {
      console.log('✅ Service Worker installed successfully');
      self.skipWaiting();
    })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('🚀 Service Worker activating...');
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME && 
              cacheName !== STATIC_CACHE && 
              cacheName !== API_CACHE && 
              cacheName !== IMAGE_CACHE) {
            console.log('🗑️ Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('✅ Service Worker activated');
      return self.clients.claim();
    })
  );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip external requests
  if (url.origin !== self.location.origin) {
    return;
  }

  event.respondWith(handleRequest(request));
});

async function handleRequest(request) {
  const url = new URL(request.url);
  const pathname = url.pathname;
  
  try {
    // Next.js static assets - Always fetch fresh in development
    if (pathname.startsWith('/_next/static/')) {
      if (isDevelopment) {
        // In development, never cache Next.js assets to avoid version conflicts
        return fetch(request);
      }
      // In production, cache with version-aware strategy
      return await nextJsAssetStrategy(request);
    }

    // API requests - Network first, cache fallback
    if (pathname.startsWith('/api/')) {
      return await networkFirstStrategy(request, API_CACHE);
    }
    
    // Images - Cache first, network fallback
    if (CACHE_STRATEGIES.images.test(pathname)) {
      return await cacheFirstStrategy(request, IMAGE_CACHE);
    }
    
    // Static assets (CSS, JS, fonts) - Different strategy for dev vs prod
    if (CACHE_STRATEGIES.assets.test(pathname)) {
      // In development, always fetch fresh to avoid cache conflicts
      if (isDevelopment) {
        return await networkFirstStrategy(request, STATIC_CACHE);
      }
      // In production, cache first for performance
      return await cacheFirstStrategy(request, STATIC_CACHE);
    }
    
    // Pages - Different strategy for dev vs prod
    if (CACHE_STRATEGIES.static.includes(pathname) || pathname === '/') {
      // In development, always fetch fresh
      if (isDevelopment) {
        return await networkFirstStrategy(request, STATIC_CACHE);
      }
      // In production, stale while revalidate
      return await staleWhileRevalidateStrategy(request, STATIC_CACHE);
    }
    
    // Default - Network first
    return await networkFirstStrategy(request, CACHE_NAME);
    
  } catch (error) {
    console.error('❌ Service Worker fetch error:', error);
    return fetch(request);
  }
}

// Cache first strategy - for static assets
async function cacheFirstStrategy(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);
  
  if (cachedResponse) {
    // Notify about cache hit
    notifyClients('CACHE_HIT', request.url);
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.warn('⚠️ Network failed for:', request.url);
    throw error;
  }
}

// Network first strategy - for API calls
async function networkFirstStrategy(request, cacheName) {
  const cache = await caches.open(cacheName);
  
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      // Cache successful responses
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.warn('⚠️ Network failed, trying cache for:', request.url);
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      notifyClients('CACHE_FALLBACK', request.url);
      return cachedResponse;
    }
    throw error;
  }
}

// Stale while revalidate - for pages
async function staleWhileRevalidateStrategy(request, cacheName) {
  const cache = await caches.open(cacheName);
  const cachedResponse = await cache.match(request);

  // Always try to update cache in background
  const networkPromise = fetch(request).then((networkResponse) => {
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  }).catch(() => {
    // Network failed, but we might have cache
  });

  // Return cached version immediately if available
  if (cachedResponse) {
    notifyClients('CACHE_HIT', request.url);
    return cachedResponse;
  }

  // Otherwise wait for network
  return networkPromise;
}

// Next.js asset strategy - version-aware caching
async function nextJsAssetStrategy(request) {
  const url = new URL(request.url);
  const versionMatch = url.search.match(/v=(\d+)/);

  if (versionMatch) {
    const version = versionMatch[1];
    const versionedCacheName = `nextjs-assets-v${version}`;

    // Use cache first for versioned assets (they're immutable)
    return await cacheFirstStrategy(request, versionedCacheName);
  }

  // For non-versioned assets, always fetch fresh
  return fetch(request);
}

// Notify clients about cache events
function notifyClients(type, url) {
  self.clients.matchAll().then((clients) => {
    clients.forEach((client) => {
      client.postMessage({
        type,
        url,
        timestamp: Date.now()
      });
    });
  });
}

// Background sync for failed requests
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

async function doBackgroundSync() {
  console.log('🔄 Background sync triggered');
  // Implement background sync logic here if needed
}

// Push notifications (for future use)
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: '/roukey_logo.png',
      badge: '/roukey_logo.png',
      data: data.data
    };
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  event.waitUntil(
    self.clients.openWindow(event.notification.data?.url || '/')
  );
});

console.log('🔧 RouKey Service Worker loaded');
