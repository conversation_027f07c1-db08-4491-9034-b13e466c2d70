/**
 * URL utilities for handling development vs production environments
 */

/**
 * Get the base URL for the current environment
 * In development: http://localhost:3000
 * In production: https://roukey.online
 */
export function getBaseUrl(): string {
  // For client-side, we can check the window location
  if (typeof window !== 'undefined') {
    // If we're on localhost, use localhost
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      return `${window.location.protocol}//${window.location.host}`;
    }
    // Otherwise use the current origin
    return window.location.origin;
  }

  // For server-side, check NODE_ENV
  if (process.env.NODE_ENV === 'development') {
    return 'http://localhost:3000';
  }

  // Use environment variable or fallback to production URL
  return process.env.NEXT_PUBLIC_SITE_URL || 'https://roukey.online';
}

/**
 * Create an absolute URL for the current environment
 */
export function createAbsoluteUrl(path: string): string {
  const baseUrl = getBaseUrl();
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${cleanPath}`;
}

/**
 * Check if we're running in development mode
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development';
}

/**
 * Check if we're running in production mode
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}
