ALTER TABLE public.custom_api_configs
ADD COLUMN IF NOT EXISTS routing_strategy TEXT DEFAULT 'none',
ADD COLUMN IF NOT EXISTS routing_strategy_params JSONB DEFAULT NULL;

COMMENT ON COLUMN public.custom_api_configs.routing_strategy IS 'Defines the advanced routing strategy to be used for this configuration. Examples: none, intelligent_role, complexity_round_robin, auto_optimal, strict_fallback.';
COMMENT ON COLUMN public.custom_api_configs.routing_strategy_params IS 'JSONB blob to store strategy-specific parameters. E.g., for fallback: ordered list of API key IDs; for intelligent_role: example prompts or embedding references per role.';

-- You might want to add a CHECK constraint later if you want to strictly enforce values for routing_strategy
-- For example:
-- ALTER TABLE public.custom_api_configs
-- ADD CONSTRAINT check_routing_strategy_values
-- CHECK (routing_strategy IN ('none', 'intelligent_role', 'complexity_round_robin', 'auto_optimal', 'strict_fallback')); 