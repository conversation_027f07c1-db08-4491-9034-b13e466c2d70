import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { z } from 'zod';

const CustomRoleSchema = z.object({
  role_id: z.string().trim()
    .min(1, 'Role ID is required')
    .max(30, 'Role ID must be 30 characters or less')
    .regex(/^[a-zA-Z0-9_]+$/, 'Role ID can only contain letters, numbers, and underscores. No spaces or special characters.'),
  name: z.string().trim().min(1, 'Name is required').max(100, 'Name must be 100 characters or less'),
  description: z.string().trim().max(500, 'Description must be 500 characters or less').optional().nullable(),
});

// POST /api/user/custom-roles
// Creates a new global custom role for the authenticated user
export async function POST(request: NextRequest) {
  const supabase = await createSupabaseServerClientOnRequest();
  /*
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return NextResponse.json({ error: 'Unauthorized. Please log in.' }, { status: 401 });
  }
  const userId = user.id;
  */
  const userId = '00000000-0000-0000-0000-000000000000'; // Placeholder for testing

  let requestBody;
  try {
    requestBody = await request.json();
  } catch (e) {
    return NextResponse.json({ error: 'Invalid JSON request body.' }, { status: 400 });
  }

  const validationResult = CustomRoleSchema.safeParse(requestBody);
  if (!validationResult.success) {
    return NextResponse.json(
      { error: 'Invalid request body.', issues: validationResult.error.flatten().fieldErrors },
      { status: 400 }
    );
  }

  const { role_id, name, description } = validationResult.data;

  try {
    const { data: newCustomRole, error } = await supabase
      .from('user_custom_roles')
      .insert({
        user_id: userId,
        role_id,
        name,
        description,
      })
      .select()
      .single();

    if (error) {
      console.error('Supabase error creating global custom role:', error);
      if (error.code === '23505') { // Unique constraint violation (user_id, role_id)
        return NextResponse.json(
          { error: `You already have a custom role with ID '${role_id}'. Role IDs must be unique per user.` },
          { status: 409 } // Conflict
        );
      }
      return NextResponse.json({ error: 'Failed to create custom role.', details: error.message }, { status: 500 });
    }

    return NextResponse.json(newCustomRole, { status: 201 });

  } catch (e: any) {
    console.error('Error in POST /api/user/custom-roles:', e);
    return NextResponse.json({ error: 'An unexpected server error occurred.', details: e.message }, { status: 500 });
  }
}

// GET /api/user/custom-roles
// Lists all global custom roles for the authenticated user
export async function GET(request: NextRequest) {
  const supabase = await createSupabaseServerClientOnRequest();
  /*
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    return NextResponse.json({ error: 'Unauthorized. Please log in.' }, { status: 401 });
  }
  const userId = user.id;
  */
  const userId = '00000000-0000-0000-0000-000000000000'; // Placeholder for testing

  try {
    const { data: customRoles, error } = await supabase
      .from('user_custom_roles')
      .select('*') // Selects all columns, including id, role_id, name, description, user_id, created_at, updated_at
      .eq('user_id', userId)
      .order('name', { ascending: true });

    if (error) {
      console.error('Supabase error fetching global custom roles:', error);
      return NextResponse.json({ error: 'Failed to fetch custom roles.', details: error.message }, { status: 500 });
    }

    return NextResponse.json(customRoles || [], { status: 200 });

  } catch (e: any) {
    console.error('Error in GET /api/user/custom-roles:', e);
    return NextResponse.json({ error: 'An unexpected server error occurred.', details: e.message }, { status: 500 });
  }
} 