'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

interface SidebarContextType {
  isCollapsed: boolean;
  isHovered: boolean;
  isHoverDisabled: boolean;
  toggleSidebar: () => void;
  collapseSidebar: () => void;
  expandSidebar: () => void;
  setHovered: (hovered: boolean) => void;
  setHoverDisabled: (disabled: boolean) => void;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export function SidebarProvider({ children }: { children: React.ReactNode }) {
  const [isCollapsed, setIsCollapsed] = useState(true); // Always start collapsed
  const [isHovered, setIsHovered] = useState(false);
  const [isHoverDisabled, setIsHoverDisabled] = useState(false);

  // Remove localStorage persistence for hover-based sidebar
  // The sidebar will always start collapsed and expand on hover

  const toggleSidebar = () => setIsCollapsed(!isCollapsed);
  const collapseSidebar = () => setIsCollapsed(true);
  const expandSidebar = () => setIsCollapsed(false);
  const setHovered = (hovered: boolean) => {
    // Don't allow hover if disabled
    if (!isHoverDisabled) {
      setIsHovered(hovered);
    }
  };
  const setHoverDisabled = (disabled: boolean) => {
    setIsHoverDisabled(disabled);
    // If disabling hover, also clear current hover state
    if (disabled) {
      setIsHovered(false);
    }
  };

  return (
    <SidebarContext.Provider value={{
      isCollapsed,
      isHovered,
      isHoverDisabled,
      toggleSidebar,
      collapseSidebar,
      expandSidebar,
      setHovered,
      setHoverDisabled
    }}>
      {children}
    </SidebarContext.Provider>
  );
}

export function useSidebar() {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
}
