// Phase 2C: Messaging Performance Monitoring and Optimization
export class MessagingPerformanceTracker {
  private static instance: MessagingPerformanceTracker;
  private metrics: Map<string, MessagingMetric[]> = new Map();
  private readonly maxSamples = 50;

  // Phase 1 Optimization: Track parallel processing metrics
  private parallelMetrics: Map<string, {
    totalTime: number;
    llmTime: number;
    backgroundOperations: number;
    parallelEfficiency: number;
    firstTokenTime?: number;
    timestamp: number;
  }[]> = new Map();

  static getInstance(): MessagingPerformanceTracker {
    if (!MessagingPerformanceTracker.instance) {
      MessagingPerformanceTracker.instance = new MessagingPerformanceTracker();
    }
    return MessagingPerformanceTracker.instance;
  }

  // Phase 1 Optimization: Track parallel processing performance
  trackParallelFlow(data: {
    provider: string;
    model: string;
    totalTime: number;
    llmTime: number;
    backgroundOperations: number;
    parallelEfficiency: number;
    firstTokenTime?: number;
  }) {
    const key = `${data.provider}_${data.model}_parallel`;

    if (!this.parallelMetrics) {
      this.parallelMetrics = new Map();
    }

    if (!this.parallelMetrics.has(key)) {
      this.parallelMetrics.set(key, []);
    }

    const measurements = this.parallelMetrics.get(key)!;
    measurements.push({
      ...data,
      timestamp: Date.now()
    });

    // Keep only recent measurements
    if (measurements.length > this.maxSamples) {
      measurements.shift();
    }

    // Log parallel processing insights
    console.log(`🔄 [PARALLEL PERFORMANCE] ${data.provider}/${data.model}:`);
    console.log(`   ⏱️ Total: ${data.totalTime.toFixed(1)}ms`);
    console.log(`   🤖 LLM: ${data.llmTime.toFixed(1)}ms (${(data.llmTime/data.totalTime*100).toFixed(1)}%)`);
    console.log(`   🔧 Background Ops: ${data.backgroundOperations}`);
    console.log(`   ⚡ Parallel Efficiency: ${(data.parallelEfficiency*100).toFixed(1)}%`);
    if (data.firstTokenTime) {
      console.log(`   🚀 First Token: ${data.firstTokenTime.toFixed(1)}ms`);
    }
  }

  // Track complete messaging flow with enhanced timing
  trackMessagingFlow(data: {
    provider: string;
    model: string;
    messageLength: number;
    isStreaming: boolean;
    timings: {
      saveUserMessage: number;
      llmApiCall: number;
      saveAiResponse: number;
      refreshConversation: number;
      total: number;
      // Enhanced timing metrics
      timeToFirstToken?: number;
      streamingDuration?: number;
      backendProcessing?: number;
      frontendProcessing?: number;
    };
    success: boolean;
    error?: string;
    // Additional streaming metrics
    streamingMetrics?: {
      firstTokenReceived: number;
      totalTokens: number;
      averageTokenLatency: number;
      streamingComplete: number;
    };
  }) {
    const key = `${data.provider}_${data.model}`;
    
    if (!this.metrics.has(key)) {
      this.metrics.set(key, []);
    }
    
    const measurements = this.metrics.get(key)!;
    measurements.push({
      timestamp: Date.now(),
      ...data
    });
    
    // Keep only recent measurements
    if (measurements.length > this.maxSamples) {
      measurements.shift();
    }

    // Log performance insights
    this.logPerformanceInsights(data);
  }

  // Get performance statistics for a provider/model
  getStats(provider: string, model: string): MessagingStats | null {
    const key = `${provider}_${model}`;
    const measurements = this.metrics.get(key);
    
    if (!measurements || measurements.length === 0) return null;

    const successfulMeasurements = measurements.filter(m => m.success);
    if (successfulMeasurements.length === 0) return null;

    const totalTimes = successfulMeasurements.map(m => m.timings.total);
    const llmTimes = successfulMeasurements.map(m => m.timings.llmApiCall);
    const messageLengths = successfulMeasurements.map(m => m.messageLength);

    return {
      provider,
      model,
      sampleCount: successfulMeasurements.length,
      averageTotal: this.calculateAverage(totalTimes),
      averageLLM: this.calculateAverage(llmTimes),
      medianTotal: this.calculateMedian(totalTimes),
      medianLLM: this.calculateMedian(llmTimes),
      p95Total: this.calculatePercentile(totalTimes, 95),
      p95LLM: this.calculatePercentile(llmTimes, 95),
      minTotal: Math.min(...totalTimes),
      maxTotal: Math.max(...totalTimes),
      averageMessageLength: this.calculateAverage(messageLengths),
      streamingUsage: successfulMeasurements.filter(m => m.isStreaming).length / successfulMeasurements.length,
      errorRate: (measurements.length - successfulMeasurements.length) / measurements.length,
      recentTrend: this.calculateTrend(totalTimes.slice(-10))
    };
  }

  // Get overall performance summary
  getSummary(): MessagingPerformanceSummary {
    const allProviders = new Set<string>();
    const allStats: MessagingStats[] = [];

    for (const key of this.metrics.keys()) {
      const [provider, model] = key.split('_');
      allProviders.add(provider);
      const stats = this.getStats(provider, model);
      if (stats) allStats.push(stats);
    }

    if (allStats.length === 0) {
      return {
        totalProviders: 0,
        totalModels: 0,
        overallAverageTime: 0,
        fastestProvider: null,
        slowestProvider: null,
        recommendations: ['No messaging data available yet']
      };
    }

    const overallAverageTime = this.calculateAverage(allStats.map(s => s.averageTotal));
    const sortedBySpeed = [...allStats].sort((a, b) => a.averageTotal - b.averageTotal);

    return {
      totalProviders: allProviders.size,
      totalModels: allStats.length,
      overallAverageTime,
      fastestProvider: sortedBySpeed[0],
      slowestProvider: sortedBySpeed[sortedBySpeed.length - 1],
      recommendations: this.generateRecommendations(allStats)
    };
  }

  // Generate performance recommendations
  private generateRecommendations(stats: MessagingStats[]): string[] {
    const recommendations: string[] = [];

    // Check for slow providers
    const slowProviders = stats.filter(s => s.averageTotal > 5000);
    if (slowProviders.length > 0) {
      recommendations.push(`Consider switching from slow providers: ${slowProviders.map(s => s.provider).join(', ')}`);
    }

    // Check streaming usage
    const lowStreamingUsage = stats.filter(s => s.streamingUsage < 0.5);
    if (lowStreamingUsage.length > 0) {
      recommendations.push('Enable streaming for better perceived performance');
    }

    // Check error rates
    const highErrorRates = stats.filter(s => s.errorRate > 0.1);
    if (highErrorRates.length > 0) {
      recommendations.push(`High error rates detected for: ${highErrorRates.map(s => s.provider).join(', ')}`);
    }

    // Performance targets
    const targetTime = 2000; // 2 seconds target
    const fastEnoughProviders = stats.filter(s => s.averageTotal <= targetTime);
    if (fastEnoughProviders.length === 0) {
      recommendations.push('No providers meeting 2s target - consider optimizing or switching providers');
    } else {
      recommendations.push(`Fast providers (≤2s): ${fastEnoughProviders.map(s => s.provider).join(', ')}`);
    }

    return recommendations.length > 0 ? recommendations : ['Performance looks good!'];
  }

  // Log performance insights in real-time with enhanced first token tracking
  private logPerformanceInsights(data: any) {
    const { provider, model, timings, isStreaming, streamingMetrics } = data;

    // Enhanced logging for streaming performance
    if (isStreaming && timings.timeToFirstToken) {
      console.log(`🚀 First Token Time: ${timings.timeToFirstToken}ms (${provider}/${model})`);

      // Log first token performance categories
      if (timings.timeToFirstToken < 500) {
        console.log(`⚡ EXCELLENT first token speed (${timings.timeToFirstToken}ms): ${provider}/${model}`);
      } else if (timings.timeToFirstToken < 1000) {
        console.log(`✅ GOOD first token speed (${timings.timeToFirstToken}ms): ${provider}/${model}`);
      } else if (timings.timeToFirstToken < 2000) {
        console.warn(`⚠️ SLOW first token speed (${timings.timeToFirstToken}ms): ${provider}/${model}`);
      } else {
        console.error(`🐌 VERY SLOW first token speed (${timings.timeToFirstToken}ms): ${provider}/${model}`);
      }

      // Log streaming completion metrics
      if (streamingMetrics) {
        const avgTokenLatency = streamingMetrics.averageTokenLatency;
        console.log(`📊 Streaming Stats: ${streamingMetrics.totalTokens} tokens, avg ${avgTokenLatency.toFixed(1)}ms/token`);
      }
    }

    // Log slow responses
    if (timings.total > 10000) {
      console.warn(`🐌 Slow messaging (${timings.total}ms): ${provider}/${model}`);
    }

    // Log very fast responses
    if (timings.total < 1000) {
      console.log(`⚡ Fast messaging (${timings.total}ms): ${provider}/${model}`);
    }

    // Enhanced LLM vs infrastructure breakdown
    const infrastructureTime = timings.total - timings.llmApiCall;
    const llmPercentage = (timings.llmApiCall / timings.total) * 100;

    if (infrastructureTime > 2000) {
      console.warn(`🔧 High infrastructure overhead (${infrastructureTime}ms, ${100 - llmPercentage}%)`);
    }

    // Log backend vs frontend processing breakdown
    if (timings.backendProcessing && timings.frontendProcessing) {
      const backendPercent = (timings.backendProcessing / timings.total) * 100;
      const frontendPercent = (timings.frontendProcessing / timings.total) * 100;
      console.log(`⚖️ Processing Split: Backend ${timings.backendProcessing}ms (${backendPercent.toFixed(1)}%), Frontend ${timings.frontendProcessing}ms (${frontendPercent.toFixed(1)}%)`);
    }

    // Log streaming benefits
    if (!isStreaming && timings.total > 3000) {
      console.log(`💡 Consider enabling streaming for ${provider}/${model} (${timings.total}ms)`);
    }
  }

  // Utility methods
  private calculateAverage(numbers: number[]): number {
    return numbers.reduce((sum, n) => sum + n, 0) / numbers.length;
  }

  private calculateMedian(numbers: number[]): number {
    const sorted = [...numbers].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 
      ? (sorted[mid - 1] + sorted[mid]) / 2 
      : sorted[mid];
  }

  private calculatePercentile(numbers: number[], percentile: number): number {
    const sorted = [...numbers].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[Math.max(0, index)];
  }

  private calculateTrend(recentNumbers: number[]): 'improving' | 'stable' | 'degrading' {
    if (recentNumbers.length < 5) return 'stable';
    
    const firstHalf = recentNumbers.slice(0, Math.floor(recentNumbers.length / 2));
    const secondHalf = recentNumbers.slice(Math.floor(recentNumbers.length / 2));
    
    const firstAvg = this.calculateAverage(firstHalf);
    const secondAvg = this.calculateAverage(secondHalf);
    
    const changePercent = ((secondAvg - firstAvg) / firstAvg) * 100;
    
    if (changePercent < -10) return 'improving';
    if (changePercent > 10) return 'degrading';
    return 'stable';
  }

  // Export data for analysis
  exportData(): Record<string, MessagingMetric[]> {
    const exported: Record<string, MessagingMetric[]> = {};
    for (const [key, values] of this.metrics.entries()) {
      exported[key] = [...values];
    }
    return exported;
  }

  // Clear all metrics
  clear(): void {
    this.metrics.clear();
  }
}

// Types
interface MessagingMetric {
  timestamp: number;
  provider: string;
  model: string;
  messageLength: number;
  isStreaming: boolean;
  timings: {
    saveUserMessage: number;
    llmApiCall: number;
    saveAiResponse: number;
    refreshConversation: number;
    total: number;
    // Enhanced timing metrics
    timeToFirstToken?: number;
    streamingDuration?: number;
    backendProcessing?: number;
    frontendProcessing?: number;
  };
  success: boolean;
  error?: string;
  // Additional streaming metrics
  streamingMetrics?: {
    firstTokenReceived: number;
    totalTokens: number;
    averageTokenLatency: number;
    streamingComplete: number;
  };
}

interface MessagingStats {
  provider: string;
  model: string;
  sampleCount: number;
  averageTotal: number;
  averageLLM: number;
  medianTotal: number;
  medianLLM: number;
  p95Total: number;
  p95LLM: number;
  minTotal: number;
  maxTotal: number;
  averageMessageLength: number;
  streamingUsage: number;
  errorRate: number;
  recentTrend: 'improving' | 'stable' | 'degrading';
}

interface MessagingPerformanceSummary {
  totalProviders: number;
  totalModels: number;
  overallAverageTime: number;
  fastestProvider: MessagingStats | null;
  slowestProvider: MessagingStats | null;
  recommendations: string[];
}

// React hook for messaging performance monitoring
export function useMessagingPerformance() {
  const tracker = MessagingPerformanceTracker.getInstance();
  
  return {
    trackFlow: tracker.trackMessagingFlow.bind(tracker),
    getStats: tracker.getStats.bind(tracker),
    getSummary: tracker.getSummary.bind(tracker),
    exportData: tracker.exportData.bind(tracker),
    clear: tracker.clear.bind(tracker)
  };
}

// Console logging utility for messaging performance
export function logMessagingReport() {
  const tracker = MessagingPerformanceTracker.getInstance();
  const summary = tracker.getSummary();
  
  console.group('🚀 Messaging Performance Report (Phase 2C)');
  console.log(`Overall Average: ${summary.overallAverageTime}ms`);
  
  if (summary.fastestProvider) {
    console.log(`Fastest: ${summary.fastestProvider.provider} (${summary.fastestProvider.averageTotal}ms)`);
  }
  
  if (summary.slowestProvider) {
    console.log(`Slowest: ${summary.slowestProvider.provider} (${summary.slowestProvider.averageTotal}ms)`);
  }
  
  console.group('💡 Recommendations:');
  summary.recommendations.forEach(rec => console.log(`• ${rec}`));
  console.groupEnd();
  console.groupEnd();
}

// Enhanced timing tracker for first token measurements
export class FirstTokenTracker {
  private static instance: FirstTokenTracker;
  private timingData: Map<string, {
    requestStart: number;
    firstTokenReceived?: number;
    streamComplete?: number;
    tokenCount: number;
    provider: string;
    model: string;
  }> = new Map();

  static getInstance(): FirstTokenTracker {
    if (!FirstTokenTracker.instance) {
      FirstTokenTracker.instance = new FirstTokenTracker();
    }
    return FirstTokenTracker.instance;
  }

  // Start tracking a request
  startRequest(requestId: string, provider: string, model: string): void {
    this.timingData.set(requestId, {
      requestStart: performance.now(),
      tokenCount: 0,
      provider,
      model
    });
    console.log(`🎯 Starting first token tracking for ${provider}/${model} (ID: ${requestId})`);
  }

  // Mark when first token is received
  markFirstToken(requestId: string): number | null {
    const data = this.timingData.get(requestId);
    if (!data) {
      console.warn(`⚠️ No tracking data found for request ${requestId}`);
      return null;
    }

    const firstTokenTime = performance.now() - data.requestStart;
    data.firstTokenReceived = performance.now();

    console.log(`🚀 FIRST TOKEN RECEIVED: ${firstTokenTime.toFixed(1)}ms (${data.provider}/${data.model})`);
    return firstTokenTime;
  }

  // Track token received during streaming
  trackToken(requestId: string): void {
    const data = this.timingData.get(requestId);
    if (data) {
      data.tokenCount++;
    }
  }

  // Mark when streaming is complete and get final metrics
  completeStream(requestId: string): {
    timeToFirstToken: number;
    totalStreamTime: number;
    totalTokens: number;
    averageTokenLatency: number;
  } | null {
    const data = this.timingData.get(requestId);
    if (!data || !data.firstTokenReceived) {
      console.warn(`⚠️ Incomplete tracking data for request ${requestId}`);
      return null;
    }

    const streamComplete = performance.now();
    data.streamComplete = streamComplete;

    const timeToFirstToken = data.firstTokenReceived - data.requestStart;
    const totalStreamTime = streamComplete - data.requestStart;
    const streamingDuration = streamComplete - data.firstTokenReceived;
    const averageTokenLatency = data.tokenCount > 1 ? streamingDuration / (data.tokenCount - 1) : 0;

    const metrics = {
      timeToFirstToken,
      totalStreamTime,
      totalTokens: data.tokenCount,
      averageTokenLatency
    };

    console.log(`📊 STREAMING COMPLETE: ${data.provider}/${data.model}`);
    console.log(`   ⏱️ Time to First Token: ${timeToFirstToken.toFixed(1)}ms`);
    console.log(`   🔄 Total Stream Time: ${totalStreamTime.toFixed(1)}ms`);
    console.log(`   🎯 Total Tokens: ${data.tokenCount}`);
    console.log(`   📈 Avg Token Latency: ${averageTokenLatency.toFixed(1)}ms/token`);

    // Clean up tracking data
    this.timingData.delete(requestId);

    return metrics;
  }

  // Get current status of a request
  getStatus(requestId: string): string {
    const data = this.timingData.get(requestId);
    if (!data) return 'Not tracked';
    if (!data.firstTokenReceived) return 'Waiting for first token';
    if (!data.streamComplete) return 'Streaming in progress';
    return 'Complete';
  }

  // Clear all tracking data
  clear(): void {
    this.timingData.clear();
  }
}

// Enhanced logging function with first token insights
export function logFirstTokenReport() {
  console.log('\n📊 === FIRST TOKEN PERFORMANCE REPORT ===');
  console.log('Use firstTokenTracker.startRequest(), markFirstToken(), and completeStream() to track timing');
  console.log('Current active trackings:', FirstTokenTracker.getInstance()['timingData'].size);
  console.log('==========================================\n');
}

// Export singleton instances
export const messagingPerformanceTracker = MessagingPerformanceTracker.getInstance();
export const firstTokenTracker = FirstTokenTracker.getInstance();
