'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import {
  BoltIcon,
  CodeBracketIcon,
  CpuChipIcon,
  RocketLaunchIcon,
  HeartIcon,
  TrophyIcon
} from '@heroicons/react/24/outline';
import LandingNavbar from '@/components/landing/LandingNavbar';
import Footer from '@/components/landing/Footer';
import EnhancedGridBackground from '@/components/landing/EnhancedGridBackground';



const principles = [
  {
    icon: BoltIcon,
    title: "Get intelligent routing",
    description: "Not just any model - the RIGHT model for each task. Smart algorithms match your requests to optimal AI models automatically."
  },
  {
    icon: CodeBracketIcon,
    title: "Build with role-based logic",
    description: "Define custom roles and let <PERSON><PERSON><PERSON><PERSON> intelligently route based on context. Coding, writing, analysis - each gets the perfect model."
  },
  {
    icon: RocketLaunchIcon,
    title: "Scale without limits",
    description: "Unlimited requests across 300+ models with intelligent failover. Your AI infrastructure that actually thinks."
  }
];

export default function AboutDeveloperPage() {
  return (
    <>
      <style jsx>{`
        .perspective-1000 {
          perspective: 1000px;
        }
        .rotate-y-12 {
          transform: rotateY(-12deg) rotateX(5deg);
          transition: transform 0.3s ease;
        }
        .rotate-y-12:hover {
          transform: rotateY(-8deg) rotateX(2deg) scale(1.02);
        }
      `}</style>
      <div className="min-h-screen bg-gradient-to-br from-[#040716] to-[#1C051C] relative overflow-hidden">
      <LandingNavbar />

      {/* Enhanced Grid Background */}
      <EnhancedGridBackground
        gridSize={45}
        opacity={0.06}
        color="#ff6b35"
        variant="premium"
        animated={true}
        className="absolute inset-0"
      />

      <main className="pt-20 relative z-10">
        {/* Hero Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              {/* Image Side */}
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                className="relative flex justify-center"
              >
                <div className="relative">
                  {/* Main card with 3D effect */}
                  <div className="relative bg-gradient-to-br from-blue-600 to-purple-700 rounded-3xl p-8 shadow-2xl transform perspective-1000 rotate-y-12">
                    {/* Background pattern */}
                    <div className="absolute inset-0 opacity-20 rounded-3xl">
                      <div
                        style={{
                          backgroundImage: `
                            linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                            linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)
                          `,
                          backgroundSize: '20px 20px'
                        }}
                        className="w-full h-full rounded-3xl"
                      />
                    </div>

                    <div className="relative z-10">
                      <div className="w-80 h-80 mx-auto rounded-2xl overflow-hidden shadow-2xl">
                        <img
                          src="/founder.jpg"
                          alt="Okoro David Chukwunyerem - RouKey Founder"
                          className="w-full h-full object-cover"
                          loading="lazy"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Name badge positioned below the card */}
                  <div className="mt-8 text-center">
                    <div className="inline-block bg-gray-800/90 backdrop-blur-sm rounded-2xl px-8 py-4 border border-gray-600/50 shadow-xl">
                      <div className="text-white font-bold text-xl tracking-wide">
                        HEY, I'M <span className="text-[#ff6b35]">OKORO DAVID</span>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Content Side */}
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="space-y-8"
              >
                <div>
                  <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
                    From Rate Limits to
                    <span className="text-[#ff6b35] block">Unlimited AI</span>
                  </h1>
                  
                  <div className="space-y-6 text-lg text-gray-300 leading-relaxed">
                    <p>
                      In early <span className="text-[#ff6b35] font-bold">2025</span>, I was deep in a coding session when I kept hitting rate limits on Gemini. Every time I got into the flow, <span className="text-white font-bold">boom - rate limit exceeded</span>. It was killing my productivity.
                    </p>

                    <p>
                      I started with a simple Round Robin router, but then I realized something bigger: <span className="text-white font-bold">what if routing could be intelligent?</span> What if it could automatically choose the <span className="text-[#ff6b35] font-bold">best model for each specific task</span> - routing coding questions to Claude, creative tasks to GPT, and analysis to specialized models?
                    </p>

                    <p>
                      That's when I knew I wasn't just solving rate limits - I was building the future of AI routing. With my experience in <span className="text-[#ff6b35] font-bold">complex systems and game logic</span>, I developed smart algorithms that understand context, roles, and optimal model selection.
                    </p>

                    <p>
                      <span className="text-[#ff6b35] font-bold">RouKey</span> became the world's first truly intelligent AI gateway - not just cycling through models, but <span className="text-white font-bold">intelligently matching each request to the perfect model</span>. Now thousands of developers get better results, faster responses, and unlimited access. Why? Because with RouKey, you can:
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Principles Section */}
        <section className="py-20">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {principles.map((principle, index) => (
                <motion.div
                  key={principle.title}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 hover:bg-white/10 transition-all duration-300 group"
                >
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <principle.icon className="w-6 h-6 text-white" />
                      </div>
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-white mb-3">
                        {index + 1}. {principle.title}
                      </h3>
                      <p className="text-gray-300 leading-relaxed">
                        {principle.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>



        {/* Bottom Quote */}
        <section className="py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-3xl p-12"
            >
              <p className="text-2xl text-gray-300 leading-relaxed mb-8">
                What started as a simple fix for my own rate limit frustration became the tool that <span className="text-[#ff6b35] font-bold">unlocks unlimited AI potential</span> for thousands of developers. <span className="text-white font-bold">Your next breakthrough is just one API call away!</span>
              </p>
              
              <div className="flex justify-center">
                <Link href="/pricing">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-12 py-4 rounded-2xl font-bold text-xl hover:shadow-2xl transition-all duration-200 shadow-lg cursor-pointer"
                  >
                    Get Started Now
                  </motion.div>
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
      </div>
    </>
  );
}
