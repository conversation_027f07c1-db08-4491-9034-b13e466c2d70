'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useRef, useCallback } from 'react';

interface PrefetchOptions {
  priority?: 'high' | 'low';
  delay?: number;
  condition?: () => boolean;
}

interface PrefetchEntry {
  route: string;
  timestamp: number;
  prefetched: boolean;
}

class RoutePrefetchManager {
  private prefetchedRoutes = new Map<string, PrefetchEntry>();
  private router: any = null;
  private prefetchQueue: Array<{ route: string; options: PrefetchOptions }> = [];
  private isProcessing = false;

  setRouter(router: any) {
    this.router = router;
  }

  async prefetchRoute(route: string, options: PrefetchOptions = {}) {
    if (!this.router) return;

    const { priority = 'low', delay = 0, condition } = options;

    // Check condition if provided
    if (condition && !condition()) return;

    // Check if already prefetched recently (within 5 minutes)
    const existing = this.prefetchedRoutes.get(route);
    if (existing && existing.prefetched && Date.now() - existing.timestamp < 300000) {
      return;
    }

    // Add to queue
    this.prefetchQueue.push({ route, options });
    
    // Mark as queued
    this.prefetchedRoutes.set(route, {
      route,
      timestamp: Date.now(),
      prefetched: false,
    });

    // Process queue
    this.processQueue();
  }

  private async processQueue() {
    if (this.isProcessing || this.prefetchQueue.length === 0) return;

    this.isProcessing = true;

    // Sort by priority (high priority first)
    this.prefetchQueue.sort((a, b) => {
      const priorityOrder = { high: 0, low: 1 };
      return priorityOrder[a.options.priority || 'low'] - priorityOrder[b.options.priority || 'low'];
    });

    while (this.prefetchQueue.length > 0) {
      const { route, options } = this.prefetchQueue.shift()!;
      
      try {
        // Add delay if specified
        if (options.delay && options.delay > 0) {
          await new Promise(resolve => setTimeout(resolve, options.delay));
        }

        // Check if we should still prefetch (condition might have changed)
        if (options.condition && !options.condition()) {
          continue;
        }

        // Phase 2B: Enhanced prefetching with bundle preloading
        await this.router.prefetch(route);
        await this.prefetchBundles(route);

        // Mark as prefetched
        const entry = this.prefetchedRoutes.get(route);
        if (entry) {
          entry.prefetched = true;
          entry.timestamp = Date.now();
        }

        // Small delay between prefetches to avoid overwhelming the browser
        await new Promise(resolve => setTimeout(resolve, 50));

      } catch (error) {
        console.warn(`Failed to prefetch route ${route}:`, error);
      }
    }

    this.isProcessing = false;
  }

  // Phase 2B: Enhanced bundle prefetching
  private async prefetchBundles(route: string) {
    try {
      // Phase 2B Fix: Focus on route prefetching rather than manual bundle prefetching
      // Note: Next.js 14 uses different bundle structure, manual bundle prefetching can cause 404s
      // We rely on Next.js built-in prefetching instead

      // Preconnect to external domains for faster subsequent requests
      const preconnectDomains = ['https://fonts.googleapis.com', 'https://fonts.gstatic.com'];
      preconnectDomains.forEach(domain => {
        const existing = document.querySelector(`link[href="${domain}"][rel="preconnect"]`);
        if (existing) return;

        const preconnectLink = document.createElement('link');
        preconnectLink.rel = 'preconnect';
        preconnectLink.href = domain;
        preconnectLink.crossOrigin = 'anonymous';
        document.head.appendChild(preconnectLink);
      });
    } catch (error) {
      console.warn('Bundle prefetch failed:', error);
    }
  }

  // Clean up old entries
  cleanup() {
    const now = Date.now();
    const maxAge = 600000; // 10 minutes

    for (const [route, entry] of this.prefetchedRoutes.entries()) {
      if (now - entry.timestamp > maxAge) {
        this.prefetchedRoutes.delete(route);
      }
    }
  }
}

// Global instance
const prefetchManager = new RoutePrefetchManager();

export const useRoutePrefetch = () => {
  const router = useRouter();
  const cleanupInterval = useRef<NodeJS.Timeout>();

  useEffect(() => {
    prefetchManager.setRouter(router);

    // Set up cleanup interval
    cleanupInterval.current = setInterval(() => {
      prefetchManager.cleanup();
    }, 300000); // Clean up every 5 minutes

    return () => {
      if (cleanupInterval.current) {
        clearInterval(cleanupInterval.current);
      }
    };
  }, [router]);

  const prefetchRoute = useCallback((route: string, options?: PrefetchOptions) => {
    prefetchManager.prefetchRoute(route, options);
  }, []);

  const prefetchOnHover = useCallback((route: string, delay = 100) => {
    return {
      onMouseEnter: () => {
        prefetchRoute(route, { priority: 'high', delay });
      },
    };
  }, [prefetchRoute]);

  const prefetchOnVisible = useCallback((route: string, element: HTMLElement | null) => {
    if (!element) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            prefetchRoute(route, { priority: 'low', delay: 200 });
            observer.disconnect();
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(element);

    return () => observer.disconnect();
  }, [prefetchRoute]);

  return {
    prefetchRoute,
    prefetchOnHover,
    prefetchOnVisible,
  };
};

// Hook for intelligent prefetching based on user behavior
export const useIntelligentPrefetch = () => {
  const { prefetchRoute } = useRoutePrefetch();
  const userActivity = useRef({
    lastActivity: Date.now(),
    isIdle: false,
    mouseMovements: 0,
  });

  useEffect(() => {
    let idleTimer: NodeJS.Timeout;
    let activityTimer: NodeJS.Timeout;

    const resetIdleTimer = () => {
      userActivity.current.lastActivity = Date.now();
      userActivity.current.isIdle = false;
      
      clearTimeout(idleTimer);
      idleTimer = setTimeout(() => {
        userActivity.current.isIdle = true;
      }, 3000); // 3 seconds of inactivity = idle
    };

    const handleMouseMove = () => {
      userActivity.current.mouseMovements++;
      resetIdleTimer();
    };

    const handleKeyPress = () => {
      resetIdleTimer();
    };

    // Track user activity
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('keypress', handleKeyPress);
    document.addEventListener('click', resetIdleTimer);
    document.addEventListener('scroll', resetIdleTimer);

    // Reset mouse movement counter every 10 seconds
    activityTimer = setInterval(() => {
      userActivity.current.mouseMovements = 0;
    }, 10000);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('keypress', handleKeyPress);
      document.removeEventListener('click', resetIdleTimer);
      document.removeEventListener('scroll', resetIdleTimer);
      clearTimeout(idleTimer);
      clearInterval(activityTimer);
    };
  }, []);

  const prefetchWhenIdle = useCallback((routes: string[]) => {
    const checkAndPrefetch = () => {
      if (userActivity.current.isIdle && userActivity.current.mouseMovements < 5) {
        routes.forEach((route, index) => {
          prefetchRoute(route, {
            priority: 'low',
            delay: index * 500, // Stagger prefetches
            condition: () => userActivity.current.isIdle,
          });
        });
      }
    };

    // Check every 2 seconds
    const interval = setInterval(checkAndPrefetch, 2000);
    return () => clearInterval(interval);
  }, [prefetchRoute]);

  return {
    prefetchWhenIdle,
    isUserIdle: () => userActivity.current.isIdle,
  };
};
