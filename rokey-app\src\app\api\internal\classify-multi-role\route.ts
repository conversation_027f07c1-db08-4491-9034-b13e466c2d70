import { type NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    // Verify internal authorization
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    if (token !== process.env.ROKEY_API_ACCESS_TOKEN) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    const { messages, role, config_id } = await request.json();

    if (!messages || !config_id) {
      return NextResponse.json(
        { error: 'messages and config_id are required' },
        { status: 400 }
      );
    }

    // Get classification API key
    const classificationApiKey = process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;
    if (!classificationApiKey) {
      return NextResponse.json({
        isMultiRole: false,
        reason: 'No classification API key available'
      });
    }

    // Get available roles for this config
    const { data: roles, error: rolesError } = await supabase
      .from('roles')
      .select('*')
      .eq('custom_api_config_id', config_id)
      .eq('status', 'active');

    if (rolesError || !roles || roles.length === 0) {
      return NextResponse.json({
        isMultiRole: false,
        reason: 'No active roles found for this config'
      });
    }

    // Extract user prompt from messages
    const userPrompt = messages
      .filter((msg: any) => msg.role === 'user')
      .map((msg: any) => msg.content)
      .join(' ')
      .substring(0, 2000);

    if (!userPrompt.trim()) {
      return NextResponse.json({
        isMultiRole: false,
        reason: 'No user content found in messages'
      });
    }

    // Use the same multi-role detection logic as the main API
    const multiRoleResult = await detectMultiRoleTasks(userPrompt, roles, classificationApiKey);

    return NextResponse.json({
      isMultiRole: multiRoleResult.isMultiRole,
      roles: multiRoleResult.roles,
      reasoning: multiRoleResult.reasoning,
      rolesCount: multiRoleResult.roles.length
    });

  } catch (error) {
    console.error('Error in multi-role classification:', error);
    return NextResponse.json({
      isMultiRole: false,
      reason: 'Classification error'
    }, { status: 500 });
  }
}

// Multi-role detection function (copied from main API)
async function detectMultiRoleTasks(
  prompt: string,
  roles: any[],
  classificationApiKey: string
): Promise<{ isMultiRole: boolean; roles: Array<{ roleId: string; confidence: number; executionOrder: number }>; reasoning: string }> {

  // Format role information for the prompt
  const roleInfoForPrompt = roles.map(r =>
    `- Role ID: "${r.id}", Name: "${r.name}", Description: "${(r.description || 'N/A').substring(0,150)}"`
  ).join('\n');

  const multiRoleSystemPrompt = `You are RouKey's Multi-Role Task Analyzer. Your job is to determine if a user request requires multiple distinct specialized roles working together, or if it can be handled by a single role.

IMPORTANT GUIDELINES:
1. Only classify as multi-role if the task GENUINELY requires multiple distinct specialized skills
2. Simple requests that can be handled by one role should be classified as single-role
3. Consider the complexity and scope of the request
4. Look for tasks that involve multiple distinct phases or require different types of expertise

Examples of MULTI-ROLE tasks:
- "Research quantum computing, then write code, then create a business plan" (research + coding + business)
- "Analyze this data, create visualizations, and write a marketing strategy" (analysis + design + marketing)
- "Debug this code, optimize performance, and write documentation" (debugging + optimization + writing)

Examples of SINGLE-ROLE tasks:
- "Write a Python script" (coding only)
- "Explain quantum physics" (explanation only)
- "Create a business plan" (business only)
- "Brainstorm ideas" (creative thinking only)

Respond with a JSON object containing:
{
  "isMultiRole": boolean,
  "roles": [{"roleId": "role_id", "confidence": 0.8, "executionOrder": 1}],
  "reasoning": "explanation of why this is/isn't multi-role"
}`;

  const multiRoleUserMessage = `Available Roles:\n${roleInfoForPrompt}\n\nUser Request: "${prompt.substring(0, 2000)}"\n\nAnalyze this request: Does it require multiple distinct specialized roles working together, or can it be handled by a single role?`;

  const openAIMultiRolePayload = {
    model: 'gemini-2.0-flash-lite',
    messages: [
      { role: 'system', content: multiRoleSystemPrompt },
      { role: 'user', content: multiRoleUserMessage }
    ],
    temperature: 0.3,
    max_tokens: 800,
    response_format: { type: "json_object" }
  };

  try {
    const multiRoleResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/openai/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${classificationApiKey}`,
        'Connection': 'keep-alive',
        'User-Agent': 'RoKey/1.0 (Multi-Role-Detection)',
      },
      body: JSON.stringify(openAIMultiRolePayload),
    });

    if (!multiRoleResponse.ok) {
      console.warn(`[Multi-Role Detection] API error: ${multiRoleResponse.status}, falling back to single-role classification`);
      return { isMultiRole: false, roles: [], reasoning: "API error during multi-role detection" };
    }

    const multiRoleResult = await multiRoleResponse.json();
    let parsedResult;

    try {
      parsedResult = JSON.parse(multiRoleResult.choices[0].message.content);
      
      // Validate the result structure
      if (typeof parsedResult.isMultiRole !== 'boolean') {
        parsedResult.isMultiRole = false;
      }
      
      if (!Array.isArray(parsedResult.roles)) {
        parsedResult.roles = [];
      }
      
      // If it's a multi-role task, validate each role
      if (parsedResult.isMultiRole && parsedResult.roles.length > 0) {
        // Validate and match role IDs against available roles
        parsedResult.roles = parsedResult.roles.map((role: any) => {
          // Find the matching role in our available roles
          const matchedRole = roles.find(r => 
            r.id === role.roleId || 
            r.id.toLowerCase() === role.roleId.toLowerCase() ||
            (r.name && r.name.toLowerCase() === role.roleId.toLowerCase())
          );
          
          if (matchedRole) {
            return {
              ...role,
              roleId: matchedRole.id, // Use the correct casing from our available roles
              confidence: typeof role.confidence === 'number' ? role.confidence : 0.8
            };
          }
          
          // If no match found, return null to filter out later
          return null;
        }).filter(Boolean); // Remove any null entries
        
        // If we have no valid roles after filtering, fall back to single-role
        if (parsedResult.roles.length === 0) {
          parsedResult.isMultiRole = false;
          parsedResult.reasoning = "No valid roles matched after filtering";
        }
      }
      
      return parsedResult;
    } catch (error) {
      console.warn(`[Multi-Role Detection] Error parsing result: ${error}, content: ${multiRoleResult.choices?.[0]?.message?.content}`);
      return { isMultiRole: false, roles: [], reasoning: "Error parsing multi-role detection result" };
    }
  } catch (error) {
    console.warn(`[Multi-Role Detection] Error: ${error}, falling back to single-role classification`);
    return { isMultiRole: false, roles: [], reasoning: "Error during multi-role detection" };
  }
}
