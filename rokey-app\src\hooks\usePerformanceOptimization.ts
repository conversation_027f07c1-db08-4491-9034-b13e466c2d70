'use client';

import { useEffect, useRef, useState, useCallback } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  memoryUsage?: {
    used: number;
    total: number;
    limit: number;
  };
  bundleSize?: number;
  cacheHitRate?: number;
  navigationTime?: number;
}

interface PerformanceConfig {
  enableMonitoring?: boolean;
  enableMemoryTracking?: boolean;
  enableBundleAnalysis?: boolean;
  enableCacheTracking?: boolean;
  warningThresholds?: {
    renderTime?: number;
    memoryUsage?: number;
    bundleSize?: number;
  };
}

export function usePerformanceOptimization(
  componentName: string,
  config: PerformanceConfig = {}
) {
  const {
    enableMonitoring = true,
    enableMemoryTracking = true,
    enableBundleAnalysis = false,
    enableCacheTracking = true,
    warningThresholds = {
      renderTime: 100,
      memoryUsage: 50 * 1024 * 1024, // 50MB
      bundleSize: 1024 * 1024 // 1MB
    }
  } = config;

  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0
  });
  
  const renderStartTime = useRef<number>(0);
  const cacheHits = useRef<number>(0);
  const cacheRequests = useRef<number>(0);

  // Start performance measurement
  const startMeasurement = useCallback(() => {
    if (!enableMonitoring) return;
    renderStartTime.current = performance.now();
  }, [enableMonitoring]);

  // End performance measurement
  const endMeasurement = useCallback(() => {
    if (!enableMonitoring || !renderStartTime.current) return;
    
    const renderTime = performance.now() - renderStartTime.current;
    
    setMetrics(prev => ({
      ...prev,
      renderTime
    }));

    // Log warnings if thresholds exceeded
    if (renderTime > (warningThresholds.renderTime || 100)) {
      console.warn(`⚠️ ${componentName} slow render: ${renderTime.toFixed(2)}ms`);
    } else if (renderTime < 16) {
      console.log(`✅ ${componentName} fast render: ${renderTime.toFixed(2)}ms`);
    }

    renderStartTime.current = 0;
  }, [componentName, enableMonitoring, warningThresholds.renderTime]);

  // Memory usage tracking
  const trackMemoryUsage = useCallback(() => {
    if (!enableMemoryTracking || !('memory' in performance)) return;

    const memory = (performance as any).memory;
    const memoryUsage = {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      limit: memory.jsHeapSizeLimit
    };

    setMetrics(prev => ({
      ...prev,
      memoryUsage
    }));

    // Warning for high memory usage
    if (memoryUsage.used > (warningThresholds.memoryUsage || 50 * 1024 * 1024)) {
      console.warn(`⚠️ High memory usage in ${componentName}: ${(memoryUsage.used / 1024 / 1024).toFixed(2)}MB`);
    }
  }, [componentName, enableMemoryTracking, warningThresholds.memoryUsage]);

  // Bundle size analysis
  const analyzeBundleSize = useCallback(() => {
    if (!enableBundleAnalysis) return;

    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    let totalJSSize = 0;

    resources.forEach((resource) => {
      if (resource.name.includes('.js') && resource.transferSize) {
        totalJSSize += resource.transferSize;
      }
    });

    setMetrics(prev => ({
      ...prev,
      bundleSize: totalJSSize
    }));

    if (totalJSSize > (warningThresholds.bundleSize || 1024 * 1024)) {
      console.warn(`⚠️ Large bundle size: ${(totalJSSize / 1024 / 1024).toFixed(2)}MB`);
    }
  }, [enableBundleAnalysis, warningThresholds.bundleSize]);

  // Cache hit rate tracking
  const trackCacheHitRate = useCallback(() => {
    if (!enableCacheTracking) return;

    const hitRate = cacheRequests.current > 0 ? 
      (cacheHits.current / cacheRequests.current) * 100 : 0;

    setMetrics(prev => ({
      ...prev,
      cacheHitRate: hitRate
    }));
  }, [enableCacheTracking]);

  // Service Worker message handler for cache events
  useEffect(() => {
    if (!enableCacheTracking || typeof window === 'undefined') return;

    const handleSWMessage = (event: MessageEvent) => {
      if (event.data?.type === 'CACHE_HIT') {
        cacheHits.current++;
        cacheRequests.current++;
        trackCacheHitRate();
      } else if (event.data?.type === 'CACHE_MISS') {
        cacheRequests.current++;
        trackCacheHitRate();
      }
    };

    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', handleSWMessage);
      return () => {
        navigator.serviceWorker.removeEventListener('message', handleSWMessage);
      };
    }
  }, [enableCacheTracking, trackCacheHitRate]);

  // Navigation timing
  useEffect(() => {
    if (!enableMonitoring) return;

    const measureNavigation = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        const navigationTime = navigation.loadEventEnd - navigation.startTime;
        setMetrics(prev => ({
          ...prev,
          navigationTime
        }));
      }
    };

    // Measure after page load
    if (document.readyState === 'complete') {
      measureNavigation();
    } else {
      window.addEventListener('load', measureNavigation);
      return () => window.removeEventListener('load', measureNavigation);
    }
  }, [enableMonitoring]);

  // Periodic monitoring
  useEffect(() => {
    if (!enableMonitoring) return;

    const interval = setInterval(() => {
      trackMemoryUsage();
      analyzeBundleSize();
    }, 5000); // Check every 5 seconds

    return () => clearInterval(interval);
  }, [enableMonitoring, trackMemoryUsage, analyzeBundleSize]);

  // Performance optimization suggestions
  const getOptimizationSuggestions = useCallback(() => {
    const suggestions: string[] = [];

    if (metrics.renderTime > 100) {
      suggestions.push('Consider memoizing expensive calculations');
      suggestions.push('Use React.memo for component optimization');
      suggestions.push('Implement virtualization for large lists');
    }

    if (metrics.memoryUsage && metrics.memoryUsage.used > 50 * 1024 * 1024) {
      suggestions.push('Check for memory leaks');
      suggestions.push('Optimize image sizes and formats');
      suggestions.push('Implement proper cleanup in useEffect');
    }

    if (metrics.bundleSize && metrics.bundleSize > 1024 * 1024) {
      suggestions.push('Implement code splitting');
      suggestions.push('Use dynamic imports for heavy components');
      suggestions.push('Remove unused dependencies');
    }

    if (metrics.cacheHitRate !== undefined && metrics.cacheHitRate < 70) {
      suggestions.push('Improve caching strategy');
      suggestions.push('Implement service worker caching');
      suggestions.push('Use browser cache headers');
    }

    return suggestions;
  }, [metrics]);

  // Export performance data for debugging
  const exportMetrics = useCallback(() => {
    const exportData = {
      component: componentName,
      timestamp: new Date().toISOString(),
      metrics,
      suggestions: getOptimizationSuggestions(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    console.group(`📊 Performance Report: ${componentName}`);
    console.table(metrics);
    console.log('Suggestions:', getOptimizationSuggestions());
    console.groupEnd();

    return exportData;
  }, [componentName, metrics, getOptimizationSuggestions]);

  return {
    metrics,
    startMeasurement,
    endMeasurement,
    trackMemoryUsage,
    analyzeBundleSize,
    trackCacheHitRate,
    getOptimizationSuggestions,
    exportMetrics
  };
}

// Hook for monitoring page load performance
export function usePageLoadPerformance() {
  const [loadMetrics, setLoadMetrics] = useState<{
    domContentLoaded?: number;
    firstPaint?: number;
    firstContentfulPaint?: number;
    largestContentfulPaint?: number;
    timeToInteractive?: number;
  }>({});

  useEffect(() => {
    const measurePageLoad = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const paint = performance.getEntriesByType('paint');

      const metrics: any = {};

      if (navigation) {
        metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.startTime;
      }

      paint.forEach((entry) => {
        if (entry.name === 'first-paint') {
          metrics.firstPaint = entry.startTime;
        } else if (entry.name === 'first-contentful-paint') {
          metrics.firstContentfulPaint = entry.startTime;
        }
      });

      // Largest Contentful Paint
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          metrics.largestContentfulPaint = lastEntry.startTime;
          setLoadMetrics(prev => ({ ...prev, ...metrics }));
        });
        observer.observe({ entryTypes: ['largest-contentful-paint'] });
      }

      setLoadMetrics(prev => ({ ...prev, ...metrics }));
    };

    if (document.readyState === 'complete') {
      measurePageLoad();
    } else {
      window.addEventListener('load', measurePageLoad);
      return () => window.removeEventListener('load', measurePageLoad);
    }
  }, []);

  return loadMetrics;
}
