'use client';

import React from 'react';

export default function RoutingSetupLoadingSkeleton() {
  return (
    <div className="min-h-screen bg-cream animate-fade-in">
      <div className="container mx-auto px-6 py-8">
        {/* Header Skeleton */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="h-8 bg-gray-200 rounded w-20 animate-pulse"></div>
            </div>
            <div className="text-right">
              <div className="h-10 bg-gray-200 rounded w-64 mb-2 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-48 animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Error/Success Message Skeleton */}
        <div className="mb-6">
          <div className="h-12 bg-gray-200 rounded-lg animate-pulse"></div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Panel - Strategy Selection */}
          <div className="lg:col-span-1">
            <div className="card p-6">
              <div className="h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"></div>
              
              {/* Strategy Cards */}
              <div className="space-y-4">
                {[1, 2, 3, 4].map((index) => (
                  <div key={index} className="border border-gray-200 rounded-xl p-4 animate-pulse">
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 bg-gray-200 rounded-lg animate-pulse"></div>
                      <div className="flex-1">
                        <div className="h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"></div>
                        <div className="h-3 bg-gray-200 rounded w-24 mb-2 animate-pulse"></div>
                        <div className="h-3 bg-gray-200 rounded w-full animate-pulse"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Panel - Configuration Options */}
          <div className="lg:col-span-2">
            <div className="card p-8 min-h-[600px]">
              {/* Configuration Header */}
              <div className="mb-6">
                <div className="h-7 bg-gray-200 rounded w-48 mb-3 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
              </div>

              {/* Configuration Content */}
              <div className="space-y-6">
                {/* Form Fields */}
                <div className="space-y-4">
                  <div>
                    <div className="h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"></div>
                    <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                  
                  <div>
                    <div className="h-4 bg-gray-200 rounded w-32 mb-2 animate-pulse"></div>
                    <div className="h-32 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                </div>

                {/* API Keys Section */}
                <div className="border-t border-gray-200 pt-6">
                  <div className="h-5 bg-gray-200 rounded w-40 mb-4 animate-pulse"></div>
                  
                  <div className="space-y-3">
                    {[1, 2, 3].map((index) => (
                      <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-gray-200 rounded animate-pulse"></div>
                          <div>
                            <div className="h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"></div>
                            <div className="h-3 bg-gray-200 rounded w-32 animate-pulse"></div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="h-6 bg-gray-200 rounded w-16 animate-pulse"></div>
                          <div className="h-6 bg-gray-200 rounded w-12 animate-pulse"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Complexity Assignments Section */}
                <div className="border-t border-gray-200 pt-6">
                  <div className="h-5 bg-gray-200 rounded w-48 mb-4 animate-pulse"></div>
                  
                  <div className="grid grid-cols-5 gap-3 mb-4">
                    {[1, 2, 3, 4, 5].map((level) => (
                      <div key={level} className="text-center">
                        <div className="h-4 bg-gray-200 rounded w-16 mx-auto mb-2 animate-pulse"></div>
                        <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
                </div>

                {/* Save Button */}
                <div className="border-t border-gray-200 pt-6">
                  <div className="h-10 bg-gray-200 rounded w-40 animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Compact loading skeleton for quick transitions
export function CompactRoutingSetupLoadingSkeleton() {
  return (
    <div className="min-h-screen bg-cream animate-fade-in">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="h-8 bg-gray-200 rounded w-20 animate-pulse"></div>
          <div>
            <div className="h-8 bg-gray-200 rounded w-48 mb-1 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-32 animate-pulse"></div>
          </div>
        </div>

        {/* Quick Strategy Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          {[1, 2, 3, 4].map((index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-200 rounded animate-pulse"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-20 mb-1 animate-pulse"></div>
                  <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Configuration Panel */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="h-6 bg-gray-200 rounded w-40 mb-4 animate-pulse"></div>
          <div className="space-y-4">
            <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Strategy card skeleton for individual strategy loading
export function StrategyCardSkeleton() {
  return (
    <div className="border border-gray-200 rounded-xl p-4 animate-pulse">
      <div className="flex items-start space-x-3">
        <div className="w-8 h-8 bg-gray-200 rounded-lg animate-pulse"></div>
        <div className="flex-1">
          <div className="h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"></div>
          <div className="h-3 bg-gray-200 rounded w-24 mb-2 animate-pulse"></div>
          <div className="h-3 bg-gray-200 rounded w-full animate-pulse"></div>
        </div>
      </div>
    </div>
  );
}
