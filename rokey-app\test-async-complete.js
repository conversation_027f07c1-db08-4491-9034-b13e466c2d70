const fetch = require('node-fetch');

const API_KEY = 'rk_live_c5ae5a36_9Nl8iikIL2SDSNt6R4TujRKEg8xzUkK6';
const BASE_URL = 'https://roukey.online';

console.log('🚀 Starting RouKey Async Processing Tests');
console.log(`🔑 API Key: ${API_KEY.substring(0, 20)}...`);
console.log(`🌐 Base URL: ${BASE_URL}`);
console.log(`📅 Started at: ${new Date().toISOString()}`);

async function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function submitAsyncJob(requestData, testName) {
  console.log(`\n🧪 ${testName}`);
  console.log('==================================================');
  console.log('📤 Submitting async job...');
  console.log('📋 Request:', JSON.stringify(requestData, null, 2));

  const startTime = Date.now();
  
  try {
    const response = await fetch(`${BASE_URL}/api/external/v1/async/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
      },
      body: JSON.stringify(requestData),
    });

    const duration = Date.now() - startTime;
    console.log(`📊 Submit Status: ${response.status} ${response.statusText}`);
    console.log(`⏱️  Submit Duration: ${duration}ms`);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ Submit failed:', errorText);
      return null;
    }

    const result = await response.json();
    console.log('📥 Submit Response:', JSON.stringify(result, null, 2));
    
    if (result.job_id) {
      console.log(`✅ Job submitted successfully! Job ID: ${result.job_id}`);
      return result.job_id;
    } else {
      console.log('❌ No job_id in response');
      return null;
    }
  } catch (error) {
    console.log('❌ Submit error:', error.message);
    return null;
  }
}

async function pollJobStatus(jobId, maxAttempts = 30) {
  console.log(`\n🔄 Polling job status for: ${jobId}`);
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      const response = await fetch(`${BASE_URL}/api/external/v1/async/status/${jobId}`, {
        headers: {
          'X-API-Key': API_KEY,
        },
      });

      if (!response.ok) {
        console.log(`❌ Status check failed: ${response.status}`);
        return null;
      }

      const status = await response.json();
      console.log(`📊 Attempt ${attempt}: ${status.status} (${status.progress || 0}%)`);
      
      if (status.status === 'completed') {
        console.log('✅ Job completed!');
        return status;
      } else if (status.status === 'failed') {
        console.log('❌ Job failed:', status.error);
        return status;
      }
      
      // Wait before next poll
      await sleep(5000); // 5 seconds
      
    } catch (error) {
      console.log(`❌ Status check error: ${error.message}`);
    }
  }
  
  console.log('⏰ Polling timeout - job may still be running');
  return null;
}

async function getJobResult(jobId) {
  console.log(`\n📥 Retrieving result for job: ${jobId}`);
  
  try {
    const response = await fetch(`${BASE_URL}/api/external/v1/async/result/${jobId}`, {
      headers: {
        'X-API-Key': API_KEY,
      },
    });

    if (!response.ok) {
      console.log(`❌ Result retrieval failed: ${response.status}`);
      const errorText = await response.text();
      console.log('Error details:', errorText);
      return null;
    }

    const result = await response.json();
    console.log('📋 Result Response:', JSON.stringify(result, null, 2));
    
    if (result.choices && result.choices[0]) {
      const content = result.choices[0].message?.content || '';
      console.log(`📏 Response length: ${content.length} characters`);
      
      if (result.rokey_metadata && result.rokey_metadata.roles_used) {
        console.log(`🎭 Roles used: ${result.rokey_metadata.roles_used.join(', ') || 'none'}`);
      }
    }
    
    return result;
  } catch (error) {
    console.log('❌ Result retrieval error:', error.message);
    return null;
  }
}

async function testAsyncWorkflow(requestData, testName) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`🧪 ASYNC TEST: ${testName}`);
  console.log(`${'='.repeat(60)}`);
  
  // Step 1: Submit job
  const jobId = await submitAsyncJob(requestData, testName);
  if (!jobId) {
    console.log('❌ Test failed at submission');
    return false;
  }
  
  // Step 2: Poll status
  const finalStatus = await pollJobStatus(jobId);
  if (!finalStatus || finalStatus.status !== 'completed') {
    console.log('❌ Test failed at polling');
    return false;
  }
  
  // Step 3: Get result
  const result = await getJobResult(jobId);
  if (!result) {
    console.log('❌ Test failed at result retrieval');
    return false;
  }
  
  console.log('✅ Async workflow completed successfully!');
  return true;
}

async function runAsyncTests() {
  const tests = [
    {
      name: 'Complex Code Generation',
      data: {
        messages: [
          {
            role: 'user',
            content: 'Create a complete Python web scraper with error handling, rate limiting, and data export to CSV'
          }
        ],
        max_tokens: 2000
      }
    },
    {
      name: 'Multi-Role Task (Research + Code + Analysis)',
      data: {
        messages: [
          {
            role: 'user',
            content: 'Research the latest trends in AI, then write Python code to analyze sentiment in social media posts, and finally create a business strategy report'
          }
        ],
        max_tokens: 3000
      }
    },
    {
      name: 'Creative Writing + Technical Implementation',
      data: {
        messages: [
          {
            role: 'user',
            content: 'Brainstorm an innovative mobile app idea, write the technical specifications, create a development roadmap, and write sample code for the core features'
          }
        ],
        max_tokens: 2500
      }
    }
  ];

  let successCount = 0;
  
  for (const test of tests) {
    const success = await testAsyncWorkflow(test.data, test.name);
    if (success) successCount++;
    
    // Wait between tests
    if (test !== tests[tests.length - 1]) {
      console.log('\n⏳ Waiting 10 seconds before next test...');
      await sleep(10000);
    }
  }
  
  console.log(`\n${'='.repeat(60)}`);
  console.log('📊 ASYNC TEST SUMMARY');
  console.log(`${'='.repeat(60)}`);
  console.log(`✅ Successful: ${successCount}/${tests.length}`);
  console.log(`📈 Success Rate: ${((successCount / tests.length) * 100).toFixed(1)}%`);
  console.log(`📅 Completed at: ${new Date().toISOString()}`);
  
  if (successCount === tests.length) {
    console.log('🎉 All async tests passed!');
  } else {
    console.log('⚠️  Some async tests failed. Check the logs above for details.');
  }
}

// Run the tests
runAsyncTests().catch(console.error);
