import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';

// PUT /api/chat/messages/update-by-timestamp
// Updates a message by finding it using timestamp and conversation ID
export async function PUT(request: NextRequest) {
  const supabase = await createSupabaseServerClientOnRequest();

  try {
    const requestData = await request.json();
    const { conversation_id, timestamp, content } = requestData;

    if (!conversation_id || !timestamp || !content) {
      return NextResponse.json({ 
        error: 'conversation_id, timestamp, and content are required' 
      }, { status: 400 });
    }

    // Validate content structure
    if (!Array.isArray(content) || content.length === 0) {
      return NextResponse.json({ 
        error: 'Content must be a non-empty array' 
      }, { status: 400 });
    }

    // Convert timestamp to date for database comparison
    const targetDate = new Date(parseInt(timestamp));
    
    if (isNaN(targetDate.getTime())) {
      return NextResponse.json({ 
        error: 'Invalid timestamp format' 
      }, { status: 400 });
    }

    console.log(`✏️ [UPDATE-TIMESTAMP] Updating message at timestamp: ${timestamp} (${targetDate.toISOString()}) in conversation: ${conversation_id}`);

    // Find the message by timestamp and conversation ID
    // We'll look for messages created within a small time window around the timestamp
    const timeWindow = 5000; // 5 seconds window
    const startTime = new Date(targetDate.getTime() - timeWindow);
    const endTime = new Date(targetDate.getTime() + timeWindow);

    const { data: messages, error: findError } = await supabase
      .from('chat_messages')
      .select('id, created_at')
      .eq('conversation_id', conversation_id)
      .gte('created_at', startTime.toISOString())
      .lte('created_at', endTime.toISOString())
      .order('created_at', { ascending: true });

    if (findError) {
      console.error('Supabase error finding message:', findError);
      return NextResponse.json({ 
        error: 'Failed to find message', 
        details: findError.message 
      }, { status: 500 });
    }

    if (!messages || messages.length === 0) {
      console.log(`📝 [UPDATE-TIMESTAMP] No message found at timestamp ${timestamp}, this might be a new message`);
      return NextResponse.json({ 
        error: 'Message not found at specified timestamp',
        suggestion: 'This might be a new message that needs to be created instead of updated'
      }, { status: 404 });
    }

    // If multiple messages found, pick the closest one to the target timestamp
    let targetMessage = messages[0];
    if (messages.length > 1) {
      targetMessage = messages.reduce((closest, current) => {
        const closestDiff = Math.abs(new Date(closest.created_at).getTime() - targetDate.getTime());
        const currentDiff = Math.abs(new Date(current.created_at).getTime() - targetDate.getTime());
        return currentDiff < closestDiff ? current : closest;
      });
      console.log(`📝 [UPDATE-TIMESTAMP] Found ${messages.length} messages, using closest match: ${targetMessage.id}`);
    }

    // Update the message content
    const { data, error } = await supabase
      .from('chat_messages')
      .update({ content })
      .eq('id', targetMessage.id)
      .select()
      .single();

    if (error) {
      console.error('Supabase error updating message:', error);
      return NextResponse.json({ 
        error: 'Failed to update message', 
        details: error.message 
      }, { status: 500 });
    }

    console.log(`✅ [UPDATE-TIMESTAMP] Successfully updated message: ${targetMessage.id}`);

    // Update conversation's updated_at timestamp
    await supabase
      .from('chat_conversations')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', conversation_id);

    return NextResponse.json({ 
      success: true,
      message: `Updated message ${targetMessage.id}`,
      updated_message: data
    }, { status: 200 });

  } catch (e: any) {
    console.error('Error in PUT /api/chat/messages/update-by-timestamp:', e);
    if (e.name === 'SyntaxError') {
      return NextResponse.json({ 
        error: 'Invalid request body: Malformed JSON.' 
      }, { status: 400 });
    }
    return NextResponse.json({ 
      error: 'An unexpected error occurred', 
      details: e.message 
    }, { status: 500 });
  }
}
