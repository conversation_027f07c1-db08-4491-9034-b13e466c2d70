import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      );
    }

    console.log('🔍 DEBUG: Fetching all subscription data for user:', userId);

    // Get all subscriptions for this user
    const { data: subscriptions, error: subscriptionsError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single();

    // Get the most recent subscription
    const latestSubscription = subscriptions?.[0];

    // Get active subscription specifically
    const { data: activeSubscription, error: activeSubError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    const debugData = {
      userId,
      timestamp: new Date().toISOString(),
      profile: {
        data: profile,
        error: profileError?.message,
        subscription_tier: profile?.subscription_tier,
        subscription_status: profile?.subscription_status,
        user_status: profile?.user_status,
        updated_at: profile?.updated_at
      },
      subscriptions: {
        total_count: subscriptions?.length || 0,
        error: subscriptionsError?.message,
        all_subscriptions: subscriptions?.map(sub => ({
          id: sub.id,
          stripe_subscription_id: sub.stripe_subscription_id,
          tier: sub.tier,
          status: sub.status,
          created_at: sub.created_at,
          updated_at: sub.updated_at,
          current_period_start: sub.current_period_start,
          current_period_end: sub.current_period_end,
          cancel_at_period_end: sub.cancel_at_period_end
        }))
      },
      latest_subscription: latestSubscription ? {
        id: latestSubscription.id,
        stripe_subscription_id: latestSubscription.stripe_subscription_id,
        tier: latestSubscription.tier,
        status: latestSubscription.status,
        created_at: latestSubscription.created_at,
        updated_at: latestSubscription.updated_at,
        current_period_start: latestSubscription.current_period_start,
        current_period_end: latestSubscription.current_period_end,
        cancel_at_period_end: latestSubscription.cancel_at_period_end
      } : null,
      active_subscription: {
        data: activeSubscription ? {
          id: activeSubscription.id,
          stripe_subscription_id: activeSubscription.stripe_subscription_id,
          tier: activeSubscription.tier,
          status: activeSubscription.status,
          created_at: activeSubscription.created_at,
          updated_at: activeSubscription.updated_at,
          current_period_start: activeSubscription.current_period_start,
          current_period_end: activeSubscription.current_period_end,
          cancel_at_period_end: activeSubscription.cancel_at_period_end
        } : null,
        error: activeSubError?.message
      },
      tier_determination: {
        profile_tier: profile?.subscription_tier,
        latest_subscription_tier: latestSubscription?.tier,
        latest_subscription_status: latestSubscription?.status,
        active_subscription_tier: activeSubscription?.tier,
        active_subscription_status: activeSubscription?.status,
        final_tier_logic: activeSubscription?.tier && activeSubscription?.status === 'active'
          ? `Using active subscription tier: ${activeSubscription.tier}`
          : `Using profile tier: ${profile?.subscription_tier || 'free'}`
      }
    };

    console.log('🔍 DEBUG: Complete subscription data:', JSON.stringify(debugData, null, 2));

    return NextResponse.json(debugData);

  } catch (error) {
    console.error('🔍 DEBUG: Error fetching subscription data:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
