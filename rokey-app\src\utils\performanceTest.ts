// Simple performance testing utilities for development

export interface PerformanceMetrics {
  navigationStart: number;
  domContentLoaded: number;
  loadComplete: number;
  firstPaint?: number;
  firstContentfulPaint?: number;
  largestContentfulPaint?: number;
}

export function measurePagePerformance(): PerformanceMetrics | null {
  if (typeof window === 'undefined' || !window.performance) {
    return null;
  }

  const navigation = window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
  const paint = window.performance.getEntriesByType('paint');
  
  const metrics: PerformanceMetrics = {
    navigationStart: navigation.startTime,
    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.startTime,
    loadComplete: navigation.loadEventEnd - navigation.startTime,
  };

  // Add paint metrics if available
  paint.forEach((entry) => {
    if (entry.name === 'first-paint') {
      metrics.firstPaint = entry.startTime;
    } else if (entry.name === 'first-contentful-paint') {
      metrics.firstContentfulPaint = entry.startTime;
    }
  });

  // Get LCP if available
  try {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      metrics.largestContentfulPaint = lastEntry.startTime;
    });
    observer.observe({ entryTypes: ['largest-contentful-paint'] });
  } catch (e) {
    // LCP not supported
  }

  return metrics;
}

export function logPerformanceMetrics() {
  if (process.env.NODE_ENV !== 'development') return;

  setTimeout(() => {
    const metrics = measurePagePerformance();
    if (metrics) {
      console.group('🚀 Performance Metrics');
      console.log('DOM Content Loaded:', `${metrics.domContentLoaded.toFixed(2)}ms`);
      console.log('Load Complete:', `${metrics.loadComplete.toFixed(2)}ms`);
      if (metrics.firstPaint) {
        console.log('First Paint:', `${metrics.firstPaint.toFixed(2)}ms`);
      }
      if (metrics.firstContentfulPaint) {
        console.log('First Contentful Paint:', `${metrics.firstContentfulPaint.toFixed(2)}ms`);
      }
      if (metrics.largestContentfulPaint) {
        console.log('Largest Contentful Paint:', `${metrics.largestContentfulPaint.toFixed(2)}ms`);
      }
      console.groupEnd();

      // Performance warnings
      if (metrics.domContentLoaded > 2000) {
        console.warn('⚠️ Slow DOM Content Loaded time:', `${metrics.domContentLoaded.toFixed(2)}ms`);
      }
      if (metrics.firstContentfulPaint && metrics.firstContentfulPaint > 1500) {
        console.warn('⚠️ Slow First Contentful Paint:', `${metrics.firstContentfulPaint.toFixed(2)}ms`);
      }
    }
  }, 1000); // Wait for page to fully load
}

// Bundle size estimation
export function estimateBundleSize() {
  if (typeof window === 'undefined') return;

  const resources = window.performance.getEntriesByType('resource') as PerformanceResourceTiming[];
  let totalSize = 0;
  let jsSize = 0;
  let cssSize = 0;

  resources.forEach((resource) => {
    if (resource.transferSize) {
      totalSize += resource.transferSize;
      
      if (resource.name.includes('.js')) {
        jsSize += resource.transferSize;
      } else if (resource.name.includes('.css')) {
        cssSize += resource.transferSize;
      }
    }
  });

  console.group('📦 Bundle Size Estimation');
  console.log('Total Resources:', `${(totalSize / 1024).toFixed(2)} KB`);
  console.log('JavaScript:', `${(jsSize / 1024).toFixed(2)} KB`);
  console.log('CSS:', `${(cssSize / 1024).toFixed(2)} KB`);
  console.groupEnd();

  if (jsSize > 1024 * 1024) { // 1MB
    console.warn('⚠️ Large JavaScript bundle size:', `${(jsSize / 1024 / 1024).toFixed(2)} MB`);
  }
}

// Auto-run performance logging in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.addEventListener('load', () => {
    logPerformanceMetrics();
    estimateBundleSize();
  });
}
