/**
 * Admin endpoint to clean up invalid semantic cache entries
 * This removes cache entries that have invalid response structures like { note: "streamed" }
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create service role client for admin operations
function createServiceRoleClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

export async function POST(request: NextRequest) {
  try {
    // Simple admin authentication - check for admin key
    const adminKey = request.headers.get('x-admin-key');
    const expectedAdminKey = process.env.ROKEY_ADMIN_KEY || 'rokey-semantic-cache-cleanup-2024';
    if (adminKey !== expectedAdminKey) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createServiceRoleClient();

    // Find cache entries with invalid response structures
    const { data: invalidEntries, error: fetchError } = await supabase
      .from('semantic_cache')
      .select('id, response_data')
      .not('response_data', 'is', null);

    if (fetchError) {
      console.error('[Cache Cleanup] Error fetching cache entries:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch cache entries' }, { status: 500 });
    }

    if (!invalidEntries || invalidEntries.length === 0) {
      return NextResponse.json({ 
        success: true, 
        message: 'No cache entries found',
        deletedCount: 0 
      });
    }

    // Filter entries with invalid structures
    const entriesToDelete = invalidEntries.filter(entry => {
      const responseData = entry.response_data;
      
      // Check for invalid structures
      if (responseData && typeof responseData === 'object') {
        // Invalid: { note: "streamed" }
        if (responseData.note === 'streamed' && Object.keys(responseData).length === 1) {
          return true;
        }
        
        // Invalid: Missing expected OpenAI structure
        if (!responseData.choices && !responseData.content && !responseData.message) {
          return true;
        }
      }
      
      return false;
    });

    console.log(`[Cache Cleanup] Found ${entriesToDelete.length} invalid entries out of ${invalidEntries.length} total`);

    if (entriesToDelete.length === 0) {
      return NextResponse.json({ 
        success: true, 
        message: 'No invalid cache entries found',
        deletedCount: 0,
        totalEntries: invalidEntries.length
      });
    }

    // Delete invalid entries
    const idsToDelete = entriesToDelete.map(entry => entry.id);
    const { error: deleteError } = await supabase
      .from('semantic_cache')
      .delete()
      .in('id', idsToDelete);

    if (deleteError) {
      console.error('[Cache Cleanup] Error deleting invalid entries:', deleteError);
      return NextResponse.json({ error: 'Failed to delete invalid entries' }, { status: 500 });
    }

    console.log(`[Cache Cleanup] ✅ Deleted ${entriesToDelete.length} invalid cache entries`);

    return NextResponse.json({
      success: true,
      message: `Cleaned up ${entriesToDelete.length} invalid cache entries`,
      deletedCount: entriesToDelete.length,
      totalEntries: invalidEntries.length,
      remainingEntries: invalidEntries.length - entriesToDelete.length
    });

  } catch (error: any) {
    console.error('[Cache Cleanup] Error:', error);
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error.message 
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Semantic Cache Cleanup Endpoint',
    usage: {
      'POST /api/admin/cleanup-semantic-cache': {
        description: 'Clean up invalid semantic cache entries',
        headers: {
          'x-admin-key': 'Required admin key for authentication'
        },
        example: 'curl -X POST /api/admin/cleanup-semantic-cache -H "x-admin-key: your-admin-key"'
      }
    }
  });
}
