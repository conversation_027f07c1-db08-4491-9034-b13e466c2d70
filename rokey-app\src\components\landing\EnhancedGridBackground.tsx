'use client';

import { useEffect, useRef } from 'react';

interface EnhancedGridBackgroundProps {
  className?: string;
  gridSize?: number;
  opacity?: number;
  color?: string;
  animated?: boolean;
  glowEffect?: boolean;
  variant?: 'subtle' | 'tech' | 'premium';
}

export default function EnhancedGridBackground({ 
  className = '', 
  gridSize = 40,
  opacity = 0.1,
  color = '#000000',
  animated = false,
  glowEffect = false,
  variant = 'subtle'
}: EnhancedGridBackgroundProps) {
  
  const getVariantStyles = () => {
    // Convert color to rgba format for proper opacity handling
    const getRgbaColor = (baseColor: string, alpha: number) => {
      if (baseColor === '#000000') {
        return `rgba(0, 0, 0, ${alpha})`;
      } else if (baseColor === '#ffffff') {
        return `rgba(255, 255, 255, ${alpha})`;
      } else if (baseColor === '#ff6b35') {
        return `rgba(255, 107, 53, ${alpha})`;
      } else {
        // Fallback for other colors
        return `${baseColor}${Math.round(alpha * 255).toString(16).padStart(2, '0')}`;
      }
    };

    // Enhanced opacity for much more visible grids - reduced by 20%
    const enhancedOpacity = opacity * 3.2 * 0.8; // Reduced by 20% for better subtlety

    switch (variant) {
      case 'tech':
        return {
          backgroundImage: `
            linear-gradient(${getRgbaColor(color, enhancedOpacity)} 1px, transparent 1px),
            linear-gradient(90deg, ${getRgbaColor(color, enhancedOpacity)} 1px, transparent 1px),
            radial-gradient(circle at 50% 50%, ${getRgbaColor(color, enhancedOpacity * 0.5)} 2px, transparent 2px)
          `,
          backgroundSize: `${gridSize}px ${gridSize}px, ${gridSize}px ${gridSize}px, ${gridSize * 4}px ${gridSize * 4}px`,
          animation: animated ? 'tech-grid-move 30s linear infinite' : 'none',
          mask: `
            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),
            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)
          `,
          maskComposite: 'intersect',
          WebkitMask: `
            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),
            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)
          `,
          WebkitMaskComposite: 'source-in'
        };

      case 'premium':
        return {
          backgroundImage: `
            linear-gradient(${getRgbaColor(color, enhancedOpacity)} 0.5px, transparent 0.5px),
            linear-gradient(90deg, ${getRgbaColor(color, enhancedOpacity)} 0.5px, transparent 0.5px),
            linear-gradient(${getRgbaColor(color, enhancedOpacity * 0.7)} 1px, transparent 1px),
            linear-gradient(90deg, ${getRgbaColor(color, enhancedOpacity * 0.7)} 1px, transparent 1px)
          `,
          backgroundSize: `${gridSize}px ${gridSize}px, ${gridSize}px ${gridSize}px, ${gridSize * 5}px ${gridSize * 5}px, ${gridSize * 5}px ${gridSize * 5}px`,
          animation: animated ? 'premium-grid-float 40s ease-in-out infinite' : 'none',
          mask: `
            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),
            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),
            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)
          `,
          maskComposite: 'intersect',
          WebkitMask: `
            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),
            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),
            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)
          `,
          WebkitMaskComposite: 'source-in'
        };

      default: // subtle
        return {
          backgroundImage: `
            linear-gradient(${getRgbaColor(color, enhancedOpacity)} 1px, transparent 1px),
            linear-gradient(90deg, ${getRgbaColor(color, enhancedOpacity)} 1px, transparent 1px)
          `,
          backgroundSize: `${gridSize}px ${gridSize}px`,
          animation: animated ? 'subtle-grid-drift 25s linear infinite' : 'none',
          mask: `
            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),
            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)
          `,
          maskComposite: 'intersect',
          WebkitMask: `
            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),
            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)
          `,
          WebkitMaskComposite: 'source-in'
        };
    }
  };

  return (
    <>
      <div
        className={`absolute inset-0 pointer-events-none ${className}`}
        style={{
          ...getVariantStyles(),
          zIndex: 1,
          filter: glowEffect ? 'drop-shadow(0 0 10px rgba(255, 107, 53, 0.1))' : 'none',
        }}
      />
      
      {/* CSS Animations */}
      <style jsx>{`
        @keyframes subtle-grid-drift {
          0% { transform: translate(0, 0); }
          100% { transform: translate(${gridSize}px, ${gridSize}px); }
        }
        
        @keyframes tech-grid-move {
          0% { transform: translate(0, 0) rotate(0deg); }
          25% { transform: translate(${gridSize * 0.5}px, ${gridSize * 0.3}px) rotate(0.5deg); }
          50% { transform: translate(${gridSize}px, ${gridSize * 0.7}px) rotate(0deg); }
          75% { transform: translate(${gridSize * 0.3}px, ${gridSize}px) rotate(-0.5deg); }
          100% { transform: translate(0, 0) rotate(0deg); }
        }
        
        @keyframes premium-grid-float {
          0%, 100% { transform: translate(0, 0) scale(1); }
          25% { transform: translate(${gridSize * 0.2}px, ${gridSize * -0.1}px) scale(1.01); }
          50% { transform: translate(${gridSize * 0.1}px, ${gridSize * 0.2}px) scale(0.99); }
          75% { transform: translate(${gridSize * -0.1}px, ${gridSize * 0.1}px) scale(1.005); }
        }
      `}</style>
    </>
  );
}
