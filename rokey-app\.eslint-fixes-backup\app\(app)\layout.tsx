'use client';

import { SidebarProvider } from "@/contexts/SidebarContext";
import { NavigationProvider } from "@/contexts/NavigationContext";
import LayoutContent from "@/components/LayoutContent";

export default function AppLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex h-screen overflow-hidden">
      <SidebarProvider>
        <NavigationProvider>
          <LayoutContent>{children}</LayoutContent>
        </NavigationProvider>
      </SidebarProvider>
    </div>
  );
}
