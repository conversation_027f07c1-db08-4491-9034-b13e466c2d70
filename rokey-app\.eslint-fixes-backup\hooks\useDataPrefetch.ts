'use client';

import { useEffect, useRef, useCallback } from 'react';

interface PrefetchConfig {
  endpoint: string;
  key: string;
  priority: 'high' | 'medium' | 'low';
  maxAge: number; // in milliseconds
  condition?: () => boolean;
  dependencies?: string[];
}

interface CachedData {
  data: any;
  timestamp: number;
  endpoint: string;
}

class DataPrefetchManager {
  private cache = new Map<string, CachedData>();
  private prefetchQueue: PrefetchConfig[] = [];
  private isProcessing = false;
  private abortController: AbortController | null = null;

  async prefetchData(config: PrefetchConfig): Promise<void> {
    // Check if data is already cached and fresh
    const cached = this.cache.get(config.key);
    if (cached && Date.now() - cached.timestamp < config.maxAge) {
      return;
    }

    // Check condition if provided
    if (config.condition && !config.condition()) {
      return;
    }

    // Add to queue
    this.prefetchQueue.push(config);
    this.processQueue();
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.prefetchQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    this.abortController = new AbortController();

    try {
      while (this.prefetchQueue.length > 0) {
        const config = this.prefetchQueue.shift()!;
        
        // Check condition again before fetching
        if (config.condition && !config.condition()) {
          continue;
        }

        try {
          const response = await fetch(config.endpoint, {
            signal: this.abortController.signal,
            headers: {
              'X-Prefetch': 'true',
              'Cache-Control': 'max-age=300'
            }
          });

          if (response.ok) {
            const data = await response.json();
            this.cache.set(config.key, {
              data,
              timestamp: Date.now(),
              endpoint: config.endpoint
            });
            
            console.log(`📦 Prefetched data for ${config.key}`);
          }
        } catch (error) {
          if (error instanceof Error && error.name !== 'AbortError') {
            console.warn(`⚠️ Failed to prefetch ${config.key}:`, error);
          }
        }

        // Small delay between requests to avoid overwhelming the server
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    } finally {
      this.isProcessing = false;
      this.abortController = null;
    }
  }

  getCachedData(key: string): any | null {
    const cached = this.cache.get(key);
    return cached ? cached.data : null;
  }

  isCached(key: string, maxAge: number): boolean {
    const cached = this.cache.get(key);
    return cached ? Date.now() - cached.timestamp < maxAge : false;
  }

  clearCache(key?: string): void {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }

  abortPrefetch(): void {
    if (this.abortController) {
      this.abortController.abort();
    }
    this.prefetchQueue = [];
  }

  getStats() {
    return {
      cacheSize: this.cache.size,
      queueLength: this.prefetchQueue.length,
      isProcessing: this.isProcessing
    };
  }
}

const prefetchManager = new DataPrefetchManager();

export const useDataPrefetch = () => {
  const prefetchData = useCallback((config: PrefetchConfig) => {
    return prefetchManager.prefetchData(config);
  }, []);

  const getCachedData = useCallback((key: string) => {
    return prefetchManager.getCachedData(key);
  }, []);

  const isCached = useCallback((key: string, maxAge: number) => {
    return prefetchManager.isCached(key, maxAge);
  }, []);

  const clearCache = useCallback((key?: string) => {
    prefetchManager.clearCache(key);
  }, []);

  return {
    prefetchData,
    getCachedData,
    isCached,
    clearCache,
    getStats: () => prefetchManager.getStats()
  };
};

// Hook for automatic data prefetching based on common patterns
export const useAutoPrefetch = () => {
  const { prefetchData } = useDataPrefetch();
  const hasRun = useRef(false);

  useEffect(() => {
    if (hasRun.current) return;
    hasRun.current = true;

    // Prefetch critical data after a short delay
    const timer = setTimeout(() => {
      const criticalData: PrefetchConfig[] = [
        {
          endpoint: '/api/custom-configs',
          key: 'custom-configs',
          priority: 'high',
          maxAge: 300000, // 5 minutes
        },
        {
          endpoint: '/api/system-status',
          key: 'system-status',
          priority: 'high',
          maxAge: 60000, // 1 minute
        },
        {
          endpoint: '/api/analytics/summary',
          key: 'analytics-summary',
          priority: 'medium',
          maxAge: 600000, // 10 minutes
        },
        {
          endpoint: '/api/activity',
          key: 'recent-activity',
          priority: 'medium',
          maxAge: 120000, // 2 minutes
        }
      ];

      criticalData.forEach((config, index) => {
        // Stagger requests to avoid overwhelming the server
        setTimeout(() => {
          prefetchData(config);
        }, index * 200);
      });
    }, 1000); // Wait 1 second after component mount

    return () => clearTimeout(timer);
  }, [prefetchData]);
};

// Hook for prefetching data based on user navigation patterns
export const useSmartPrefetch = (currentPath: string) => {
  const { prefetchData } = useDataPrefetch();

  useEffect(() => {
    const prefetchBasedOnPath = () => {
      switch (currentPath) {
        case '/dashboard':
          // Prefetch likely next destinations from dashboard
          prefetchData({
            endpoint: '/api/logs?limit=10',
            key: 'recent-logs',
            priority: 'medium',
            maxAge: 180000, // 3 minutes
          });
          break;

        case '/playground':
          // Prefetch chat history for playground
          prefetchData({
            endpoint: '/api/chat/conversations',
            key: 'chat-conversations',
            priority: 'medium',
            maxAge: 300000, // 5 minutes
            condition: () => {
              // Only prefetch if user has custom configs
              const configs = prefetchManager.getCachedData('custom-configs');
              return configs && configs.length > 0;
            }
          });
          break;

        case '/logs':
          // Prefetch additional log pages
          prefetchData({
            endpoint: '/api/logs?page=2&limit=20',
            key: 'logs-page-2',
            priority: 'low',
            maxAge: 180000, // 3 minutes
          });
          break;

        case '/my-models':
          // Prefetch provider models
          prefetchData({
            endpoint: '/api/providers/list-models',
            key: 'provider-models',
            priority: 'medium',
            maxAge: 3600000, // 1 hour
          });
          break;
      }
    };

    // Delay prefetching to avoid interfering with current page load
    const timer = setTimeout(prefetchBasedOnPath, 2000);
    return () => clearTimeout(timer);
  }, [currentPath, prefetchData]);
};

// Export the manager for direct access if needed
export { prefetchManager };
