// Performance monitoring script for development
(function() {
  'use strict';
  
  if (typeof window === 'undefined') return;

  function measurePagePerformance() {
    if (!window.performance) return null;

    const navigation = window.performance.getEntriesByType('navigation')[0];
    const paint = window.performance.getEntriesByType('paint');
    
    const metrics = {
      navigationStart: navigation.navigationStart,
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
      loadComplete: navigation.loadEventEnd - navigation.navigationStart,
    };

    // Add paint metrics if available
    paint.forEach((entry) => {
      if (entry.name === 'first-paint') {
        metrics.firstPaint = entry.startTime;
      } else if (entry.name === 'first-contentful-paint') {
        metrics.firstContentfulPaint = entry.startTime;
      }
    });

    return metrics;
  }

  function logPerformanceMetrics() {
    setTimeout(() => {
      const metrics = measurePagePerformance();
      if (metrics) {
        console.group('🚀 RoKey Performance Metrics');
        console.log('DOM Content Loaded:', `${metrics.domContentLoaded.toFixed(2)}ms`);
        console.log('Load Complete:', `${metrics.loadComplete.toFixed(2)}ms`);
        if (metrics.firstPaint) {
          console.log('First Paint:', `${metrics.firstPaint.toFixed(2)}ms`);
        }
        if (metrics.firstContentfulPaint) {
          console.log('First Contentful Paint:', `${metrics.firstContentfulPaint.toFixed(2)}ms`);
        }
        console.groupEnd();

        // Performance warnings
        if (metrics.domContentLoaded > 2000) {
          console.warn('⚠️ Slow DOM Content Loaded time:', `${metrics.domContentLoaded.toFixed(2)}ms`);
        }
        if (metrics.firstContentfulPaint && metrics.firstContentfulPaint > 1500) {
          console.warn('⚠️ Slow First Contentful Paint:', `${metrics.firstContentfulPaint.toFixed(2)}ms`);
        }

        // Success indicators
        if (metrics.domContentLoaded < 1000) {
          console.log('✅ Fast page load!');
        }
        if (metrics.firstContentfulPaint && metrics.firstContentfulPaint < 1000) {
          console.log('✅ Fast first paint!');
        }
      }
    }, 1000);
  }

  function estimateBundleSize() {
    const resources = window.performance.getEntriesByType('resource');
    let totalSize = 0;
    let jsSize = 0;
    let cssSize = 0;

    resources.forEach((resource) => {
      if (resource.transferSize) {
        totalSize += resource.transferSize;
        
        if (resource.name.includes('.js')) {
          jsSize += resource.transferSize;
        } else if (resource.name.includes('.css')) {
          cssSize += resource.transferSize;
        }
      }
    });

    console.group('📦 Bundle Size Analysis');
    console.log('Total Resources:', `${(totalSize / 1024).toFixed(2)} KB`);
    console.log('JavaScript:', `${(jsSize / 1024).toFixed(2)} KB`);
    console.log('CSS:', `${(cssSize / 1024).toFixed(2)} KB`);
    console.groupEnd();

    if (jsSize > 1024 * 1024) { // 1MB
      console.warn('⚠️ Large JavaScript bundle:', `${(jsSize / 1024 / 1024).toFixed(2)} MB`);
    } else if (jsSize < 500 * 1024) { // 500KB
      console.log('✅ Optimized JavaScript bundle size!');
    }
  }

  // Monitor navigation performance
  let navigationStartTime = Date.now();
  
  function trackNavigation() {
    const currentTime = Date.now();
    const navigationTime = currentTime - navigationStartTime;
    
    if (navigationTime > 100) { // Only log if navigation took more than 100ms
      console.log(`🔄 Navigation took: ${navigationTime}ms`);
      
      if (navigationTime > 2000) {
        console.warn('⚠️ Slow navigation detected');
      } else if (navigationTime < 500) {
        console.log('✅ Fast navigation!');
      }
    }
    
    navigationStartTime = currentTime;
  }

  // Track route changes for SPA navigation
  let currentPath = window.location.pathname;
  setInterval(() => {
    if (window.location.pathname !== currentPath) {
      currentPath = window.location.pathname;
      trackNavigation();
    }
  }, 100);

  // Run performance monitoring
  window.addEventListener('load', () => {
    logPerformanceMetrics();
    estimateBundleSize();
  });

  // Monitor fetch requests for API performance
  const originalFetch = window.fetch;
  window.fetch = function(...args) {
    const startTime = performance.now();
    const url = args[0];

    // Don't intercept Supabase requests to avoid authentication issues
    const urlString = typeof url === 'string' ? url : (url && url.toString ? url.toString() : '');
    if (urlString.includes('supabase.co') || urlString.includes('hpkzzhpufhbxtxqaugjh')) {
      console.log('🔄 Bypassing performance monitor for Supabase request:', urlString);
      return originalFetch.apply(this, args);
    }

    return originalFetch.apply(this, args).then(response => {
      const duration = performance.now() - startTime;

      // Only log internal API routes, not external services
      if (typeof url === 'string' && url.startsWith('/api/')) {
        console.log(`📡 API ${response.status} ${url}: ${duration.toFixed(2)}ms`);

        if (duration > 1000) {
          console.warn(`⚠️ Slow API request: ${url} took ${duration.toFixed(2)}ms`);
        } else if (duration < 100) {
          console.log(`✅ Fast API response: ${url}`);
        }
      }

      return response;
    }).catch(error => {
      const duration = performance.now() - startTime;

      // Only log errors for internal API routes
      if (typeof url === 'string' && url.startsWith('/api/')) {
        console.error(`❌ API error ${url}: ${duration.toFixed(2)}ms`, error);
      }

      throw error;
    });
  };

  // Monitor service worker status
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.ready.then(() => {
      console.log('✅ Service Worker active');
    });

    navigator.serviceWorker.addEventListener('message', (event) => {
      if (event.data && event.data.type === 'CACHE_HIT') {
        console.log('📦 Cache hit:', event.data.url);
      }
    });
  }

  // Expose performance utilities
  window.RoKeyPerf = {
    clearCaches: async function() {
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map(name => caches.delete(name)));
        console.log('🗑️ All caches cleared');
      }
    },

    measureRender: function(name, fn) {
      const start = performance.now();
      const result = fn();
      const duration = performance.now() - start;
      console.log(`📊 ${name} render: ${duration.toFixed(2)}ms`);
      return result;
    },

    getMemoryUsage: function() {
      if (performance.memory) {
        const memory = performance.memory;
        return {
          used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
          total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
          limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`
        };
      }
      return 'Memory API not available';
    },

    logCurrentMetrics: function() {
      logPerformanceMetrics();
      estimateBundleSize();
      console.log('Memory:', this.getMemoryUsage());
    }
  };

  // Add performance info to console
  console.log('🔧 RoKey Performance Monitor Active');
  console.log('📊 Check console for performance metrics after page load');
  console.log('🛠️ Use window.RoKeyPerf for debugging utilities');
})();
