// Fallback endpoint for synthesis when streaming fails
// This endpoint returns a regular JSON response instead of a stream

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { broadcastOrchestrationEvent } from '@/utils/orchestrationUtils';
import { EnhancedModerator } from '@/utils/moderatorUtils';
import crypto from 'crypto';

interface RouteParams {
  params: Promise<{
    executionId: string;
  }>;
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  const { executionId } = await params;
  
  console.log(`[Synthesis Fallback] Starting for execution ${executionId}`);
  
  if (!executionId) {
    return NextResponse.json(
      { error: 'Execution ID is required' },
      { status: 400 }
    );
  }

  const supabase = await createSupabaseServerClientOnRequest();

  try {
    // Verify execution exists
    const { data: execution, error } = await supabase
      .from('orchestration_executions')
      .select('*')
      .eq('id', executionId)
      .single();

    if (error || !execution) {
      console.error(`[Synthesis Fallback] Execution not found: ${error?.message || 'No data'}`);
      return NextResponse.json(
        { error: 'Orchestration execution not found' },
        { status: 404 }
      );
    }

    // Get all completed steps for synthesis
    const { data: allSteps, error: stepsError } = await supabase
      .from('orchestration_steps')
      .select('step_number, role_id, response, prompt')
      .eq('execution_id', executionId)
      .eq('status', 'completed')
      .order('step_number', { ascending: true });

    if (stepsError || !allSteps || allSteps.length === 0) {
      console.error(`[Synthesis Fallback] No completed steps found: ${stepsError?.message || 'Empty result'}`);
      return NextResponse.json(
        { error: 'No completed steps found for synthesis' },
        { status: 400 }
      );
    }
    
    console.log(`[Synthesis Fallback] Found ${allSteps.length} completed steps for synthesis`);

    // Extract the original prompt from the first step
    const originalPrompt = allSteps[0]?.prompt?.split('"')[1] || 'user request';

    // Create synthesis prompt
    const synthesisPrompt = `You are the final moderator synthesizing the work of multiple AI specialists who collaborated on this request: "${originalPrompt}"

Here are the outputs from each specialist:

${allSteps.map(s => `**${s.role_id.toUpperCase()} (Step ${s.step_number}):**
${s.response}

`).join('\n')}

Your task is to:
1. Combine these outputs into a single, cohesive, and complete response
2. Ensure the final result fully addresses the original user request
3. Present it in a clear, well-structured format
4. Include any necessary explanations or instructions for the user

Provide the final, polished response that the user will receive:`;

    // Use the classification API key for synthesis
    const classificationApiKey = process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;
    if (!classificationApiKey) {
      return NextResponse.json(
        { error: 'Classification API key not found' },
        { status: 500 }
      );
    }
    
    // Create a moderator instance for generating commentary
    const moderator = new EnhancedModerator(classificationApiKey, executionId);

    // Make the direct API call to Gemini - NON-STREAMING
    console.log(`[Synthesis Fallback] Making direct API call to Gemini (non-streaming)`);
    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/openai/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${classificationApiKey}`,
        'User-Agent': 'RoKey/1.0 (Synthesis)',
        'Origin': 'https://rokey.app',
        'Cache-Control': 'no-cache',
      },
      body: JSON.stringify({
        model: 'gemini-2.0-flash-001',
        messages: [{ role: 'user', content: synthesisPrompt }],
        stream: false, // Non-streaming
        temperature: 0.3,
        max_tokens: 8000 // Restore original limit for complete synthesis
      })
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Could not read error response');
      console.error(`[Synthesis Fallback] API error: ${response.status}, ${errorText}`);
      return NextResponse.json(
        { error: `Synthesis API call failed: ${response.status}, ${errorText}` },
        { status: response.status }
      );
    }

    // Parse the response
    const responseData = await response.json();
    const result = responseData.choices?.[0]?.message?.content || 
                  responseData.choices?.[0]?.text || 
                  'Synthesis completed but no content was returned.';

    console.log(`[Synthesis Fallback] API call successful, got result: ${result.length} chars`);

    // Update the database with the result
    await supabase
      .from('orchestration_executions')
      .update({
        status: 'completed',
        completed_at: new Date().toISOString(),
        final_response: result
      })
      .eq('id', executionId);
    
    // Broadcast completion event
    const totalDuration = execution?.created_at ? Date.now() - new Date(execution.created_at).getTime() : 0;
    
    broadcastOrchestrationEvent(executionId, {
      id: crypto.randomUUID(),
      execution_id: executionId,
      type: 'orchestration_completed',
      timestamp: new Date().toISOString(),
      data: {
        commentary: moderator.generateLiveCommentary('orchestration_completed', { totalSteps: allSteps.length }),
        finalResult: result,
        totalSteps: allSteps.length,
        totalDuration: totalDuration
      }
    });

    // Return the result as JSON
    return NextResponse.json({ result });

  } catch (error) {
    console.error(`[Synthesis Fallback] Error: ${error}`);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}