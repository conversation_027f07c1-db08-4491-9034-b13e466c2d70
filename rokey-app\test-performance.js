// Performance test script for Phase 1 optimizations
const API_BASE = 'http://localhost:3000';
const API_TOKEN = 'Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13';

// Test configuration - replace with your actual config ID
const TEST_CONFIG_ID = 'your-config-id-here';

async function testApiPerformance() {
  console.log('🚀 Testing RoKey API Performance (Phase 1 Optimizations)');
  console.log('================================================\n');

  const testPayload = {
    custom_api_config_id: TEST_CONFIG_ID,
    messages: [
      {
        role: 'user',
        content: 'Hello! Can you tell me a short joke?'
      }
    ],
    stream: false,
    temperature: 0.7
  };

  const results = [];

  // Run 5 test requests
  for (let i = 1; i <= 5; i++) {
    console.log(`Test ${i}/5...`);
    
    const start = performance.now();
    
    try {
      const response = await fetch(`${API_BASE}/api/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${API_TOKEN}`,
          'Connection': 'keep-alive',
          'Keep-Alive': 'timeout=30, max=100',
        },
        body: JSON.stringify(testPayload),
        keepalive: true,
      });

      const duration = performance.now() - start;
      
      if (response.ok) {
        const data = await response.json();
        results.push({
          test: i,
          duration: Math.round(duration),
          status: 'success',
          model: data.model || 'unknown',
          tokens: data.usage?.total_tokens || 0
        });
        console.log(`✅ Test ${i}: ${Math.round(duration)}ms`);
      } else {
        const error = await response.text();
        results.push({
          test: i,
          duration: Math.round(duration),
          status: 'error',
          error: error.substring(0, 100)
        });
        console.log(`❌ Test ${i}: ${Math.round(duration)}ms (Error: ${response.status})`);
      }
    } catch (error) {
      const duration = performance.now() - start;
      results.push({
        test: i,
        duration: Math.round(duration),
        status: 'failed',
        error: error.message
      });
      console.log(`💥 Test ${i}: ${Math.round(duration)}ms (Failed: ${error.message})`);
    }

    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // Calculate statistics
  const successfulTests = results.filter(r => r.status === 'success');
  
  if (successfulTests.length > 0) {
    const durations = successfulTests.map(r => r.duration);
    const avgDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length;
    const minDuration = Math.min(...durations);
    const maxDuration = Math.max(...durations);
    
    console.log('\n📊 Performance Summary:');
    console.log('======================');
    console.log(`Successful requests: ${successfulTests.length}/5`);
    console.log(`Average response time: ${Math.round(avgDuration)}ms`);
    console.log(`Fastest response: ${minDuration}ms`);
    console.log(`Slowest response: ${maxDuration}ms`);
    
    // Performance assessment
    if (avgDuration < 500) {
      console.log('🎉 EXCELLENT: Sub-500ms average response time!');
    } else if (avgDuration < 1000) {
      console.log('✅ GOOD: Sub-1s average response time');
    } else if (avgDuration < 2000) {
      console.log('⚠️  FAIR: Response time could be improved');
    } else {
      console.log('🐌 SLOW: Response time needs optimization');
    }
  } else {
    console.log('\n❌ No successful requests to analyze');
  }

  console.log('\n📋 Detailed Results:');
  console.table(results);
}

// Test streaming performance
async function testStreamingPerformance() {
  console.log('\n🌊 Testing Streaming Performance');
  console.log('================================\n');

  const streamPayload = {
    custom_api_config_id: TEST_CONFIG_ID,
    messages: [
      {
        role: 'user',
        content: 'Write a short paragraph about the benefits of performance optimization.'
      }
    ],
    stream: true,
    temperature: 0.7
  };

  const start = performance.now();
  let firstChunkTime = null;
  let totalChunks = 0;

  try {
    const response = await fetch(`${API_BASE}/api/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_TOKEN}`,
        'Connection': 'keep-alive',
        'Keep-Alive': 'timeout=30, max=100',
      },
      body: JSON.stringify(streamPayload),
      keepalive: true,
    });

    if (response.ok && response.body) {
      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        
        if (firstChunkTime === null) {
          firstChunkTime = performance.now() - start;
        }
        
        if (done) break;
        
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ') && !line.includes('[DONE]')) {
            totalChunks++;
          }
        }
      }

      const totalTime = performance.now() - start;
      
      console.log(`✅ Streaming completed successfully`);
      console.log(`⚡ Time to first chunk: ${Math.round(firstChunkTime)}ms`);
      console.log(`📦 Total chunks received: ${totalChunks}`);
      console.log(`⏱️  Total streaming time: ${Math.round(totalTime)}ms`);
      
      if (firstChunkTime < 200) {
        console.log('🎉 EXCELLENT: Very fast first chunk!');
      } else if (firstChunkTime < 500) {
        console.log('✅ GOOD: Fast first chunk');
      } else {
        console.log('⚠️  SLOW: First chunk could be faster');
      }
    } else {
      console.log(`❌ Streaming failed: ${response.status}`);
    }
  } catch (error) {
    console.log(`💥 Streaming error: ${error.message}`);
  }
}

// Main test runner
async function runTests() {
  if (TEST_CONFIG_ID === 'your-config-id-here') {
    console.log('❌ Please update TEST_CONFIG_ID in the script with your actual config ID');
    return;
  }

  await testApiPerformance();
  await testStreamingPerformance();
  
  console.log('\n🏁 Performance testing completed!');
  console.log('\nPhase 1 Optimizations Applied:');
  console.log('• ✅ Removed playground proxy layer');
  console.log('• ✅ Optimized fetch configuration with keep-alive');
  console.log('• ✅ Async database logging');
  console.log('• ✅ Enhanced timeout handling');
  console.log('• ✅ Connection pooling headers');
}

runTests().catch(console.error);
