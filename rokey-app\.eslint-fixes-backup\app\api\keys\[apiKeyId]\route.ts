import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';

interface RouteParams {
  params: Promise<{
    apiKeyId: string; // Changed from id to apiKeyId
  }>;
}

// DELETE /api/keys/:apiKeyId
// Deletes a specific API key
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  const supabase = await createSupabaseServerClientOnRequest();
  const { apiKeyId } = await params; // Use (await params).apiKeyId

  if (!apiKeyId) {
    return NextResponse.json({ error: 'API Key ID is required' }, { status: 400 });
  }

  try {
    // TODO: Add user authentication check in Milestone 13 to ensure only the owner can delete.
    // const { data: { user } } = await supabase.auth.getUser();
    // if (!user) {
    //   return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    // }
    // Also ensure the apiKeyId belongs to a custom_config owned by the user.

    const { error, count } = await supabase
      .from('api_keys')
      .delete({ count: 'exact' })
      .eq('id', apiKeyId);
      // .eq('user_id', user.id); // This needs to be checked via custom_api_configs table in M13

    if (error) {
      console.error('Supabase error deleting key:', error);
      return NextResponse.json({ error: 'Failed to delete API key', details: error.message }, { status: 500 });
    }

    if (count === 0) {
      return NextResponse.json({ error: 'API key not found or already deleted.' }, { status: 404 });
    }

    return NextResponse.json({ message: 'API key deleted successfully' }, { status: 200 });

  } catch (e: any) {
    console.error('Error in DELETE /api/keys/[apiKeyId]:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
} 