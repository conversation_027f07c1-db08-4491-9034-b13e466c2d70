# 🔧 PDF Extraction Test Guide

## **What's Been Fixed**

I've implemented **real document extraction** using production-ready libraries:

### **✅ Real PDF Extraction**
- **Library**: `pdf-parse` - industry standard for PDF text extraction
- **Capability**: Extracts actual text content from PDF files
- **Handles**: Text-based PDFs (not scanned images)

### **✅ Real DOCX Extraction** 
- **Library**: `mammoth` - Microsoft Word document parser
- **Capability**: Extracts text from .docx files
- **Handles**: Modern Word documents with formatting

### **✅ Enhanced File Processing**
- **File type detection** from extensions (fallback for missing MIME types)
- **Content validation** and error handling
- **Size warnings** for large files
- **Detailed logging** for debugging

## **🧪 Testing Steps**

### **1. Test PDF Extraction**

1. **Create a test PDF** with actual text content (not scanned images)
2. **Go to Training page**
3. **Select a configuration**
4. **Upload the PDF file**
5. **Check browser console** for extraction logs:
   ```
   [PDF Extraction] Processing PDF: your-file.pdf
   [PDF Extraction] Successfully extracted 1234 characters from your-file.pdf
   [File Processing] ✅ Successfully extracted content from your-file.pdf
   ```

### **2. Test DOCX Extraction**

1. **Create a Word document** with text content
2. **Save as .docx format**
3. **Upload to Training page**
4. **Check console** for extraction logs:
   ```
   [DOCX Extraction] Processing DOCX: your-file.docx
   [DOCX Extraction] Successfully extracted 567 characters from your-file.docx
   ```

### **3. Test Knowledge Base Usage**

1. **Upload a PDF with specific information** (e.g., about Riftshot game)
2. **Add training prompts**
3. **Click "Start Training"**
4. **Go to Playground**
5. **Ask specific questions** about the PDF content
6. **Verify AI uses actual PDF content** (not just filename)

### **4. Debug Extraction Issues**

If extraction fails, check console for:

**PDF Issues:**
```
[PDF Extraction] Error processing PDF: Error message here
```
- **Scanned PDFs**: Won't work (need OCR)
- **Protected PDFs**: May fail due to restrictions
- **Image-only PDFs**: No extractable text

**DOCX Issues:**
```
[DOCX Extraction] Error processing DOCX: Error message here
```
- **Old .doc format**: Use .docx instead
- **Corrupted files**: Re-save the document

## **🔍 Verification Methods**

### **Method 1: Console Logs**
Check browser console for:
- `[PDF Extraction] Successfully extracted X characters`
- `[File Processing] Content preview (first 200 chars): [actual content]`

### **Method 2: Database Check**
Query your Supabase database:
```sql
SELECT 
  original_filename,
  file_type,
  LENGTH(extracted_content) as content_length,
  SUBSTRING(extracted_content, 1, 200) as content_preview
FROM training_files 
ORDER BY created_at DESC;
```

### **Method 3: API Debug Endpoint**
Call the debug endpoint:
```
GET /api/training/files?training_job_id=YOUR_JOB_ID&debug=true
```

## **📋 Expected Results**

### **Before Fix (Placeholder):**
```
extracted_content: "[PDF Document: riftshot.pdf] This PDF document contains important information..."
```

### **After Fix (Real Content):**
```
extracted_content: "Riftshot Game Design Document\n\nOverview:\nRiftshot is a fast-paced multiplayer shooter set in a sci-fi universe where players battle across dimensional rifts..."
```

## **🚨 Troubleshooting**

### **No Content Extracted**
- **Check file type**: Only text-based PDFs work
- **Try different PDF**: Some PDFs are image-only
- **Check file size**: Very large files may timeout

### **Partial Content**
- **PDF formatting**: Complex layouts may extract poorly
- **Try simpler PDF**: Plain text PDFs work best

### **Error Messages**
- **Check console logs** for specific error details
- **Verify file format** is supported
- **Try re-uploading** the file

## **🎯 Success Criteria**

✅ **Console shows successful extraction**
✅ **Database contains actual file content** (not placeholders)
✅ **AI references specific information** from uploaded files
✅ **Knowledge base works with real content**

The AI should now have **full access to your PDF content** and be able to answer specific questions about the information in your uploaded documents!
