'use client';

import { motion } from 'framer-motion';
import EnhancedGridBackground from './EnhancedGridBackground';
import {
  BoltIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  CpuChipIcon,
  ArrowPathIcon,
  ClockIcon,
  CurrencyDollarIcon,
  Cog6ToothIcon,
  NoSymbolIcon
} from '@heroicons/react/24/outline';

const features = [
  {
    icon: BoltIcon,
    title: "Intelligent Role Routing",
    description: "AI automatically classifies your prompts and routes them to the best model for each specific task - writing, coding, logic, or analysis.",
    color: "text-blue-600",
    bgColor: "bg-blue-50"
  },
  {
    icon: NoSymbolIcon,
    title: "Unlimited API Requests",
    description: "No request limits, no usage caps, no overage fees. Pay only for your own API costs while enjoying unlimited access to 300+ AI models.",
    color: "text-green-600",
    bgColor: "bg-green-50"
  },
  {
    icon: ShieldCheckIcon,
    title: "Enterprise Security",
    description: "Military-grade AES-256-GCM encryption for all API keys. Your credentials are stored securely and never exposed.",
    color: "text-purple-600",
    bgColor: "bg-purple-50"
  },
  {
    icon: ChartBarIcon,
    title: "Comprehensive Analytics",
    description: "Track costs, performance, token usage, and success rates across all your models with real-time dashboards and insights.",
    color: "text-orange-600",
    bgColor: "bg-orange-50"
  },
  {
    icon: ClockIcon,
    title: "Performance Optimization",
    description: "First-token tracking, latency monitoring, and intelligent caching ensure blazing-fast response times under 500ms.",
    color: "text-indigo-600",
    bgColor: "bg-indigo-50"
  },
  {
    icon: CurrencyDollarIcon,
    title: "Cost Optimization",
    description: "Automatic free-tier detection, budget alerts, and cost tracking help you optimize spending across all AI providers.",
    color: "text-emerald-600",
    bgColor: "bg-emerald-50"
  },
  {
    icon: CpuChipIcon,
    title: "300+ AI Models",
    description: "Access the latest models from OpenAI, Google, Anthropic, DeepSeek, xAI, Meta, and hundreds more through one unified API.",
    color: "text-red-600",
    bgColor: "bg-red-50"
  },
  {
    icon: Cog6ToothIcon,
    title: "Advanced Routing Strategies",
    description: "Load balancing, complexity-based routing, strict fallback sequences, and custom rules for enterprise-grade control.",
    color: "text-cyan-600",
    bgColor: "bg-cyan-50"
  }
];

export default function FeaturesSection() {
  return (
    <section id="features" className="relative overflow-hidden">
      {/* White Section */}
      <div className="bg-white py-20 relative">
        {/* Enhanced Grid Background */}
        <EnhancedGridBackground
          gridSize={45}
          opacity={0.06}
          color="#000000"
          variant="premium"
          animated={true}
          className="absolute inset-0"
        />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            {/* Section Header */}
            <div className="text-center mb-16">
              <motion.h2
                initial={{ opacity: 0, y: 15 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.3 }}
                className="text-4xl sm:text-5xl font-bold text-gray-900 mb-6 leading-tight"
              >
                Enterprise-Grade
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]">
                  {' '}AI Infrastructure
                </span>
              </motion.h2>
              <motion.p
                initial={{ opacity: 0, y: 15 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.3, delay: 0.05 }}
                className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed"
              >
                RouKey provides military-grade security, intelligent routing, and comprehensive analytics
                for the most demanding AI workloads. Built for scale, designed for performance.
              </motion.p>
            </div>

            {/* Features Grid - First Half */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              {features.slice(0, 4).map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 15 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200 hover:shadow-xl hover:border-[#ff6b35]/30 transition-all duration-300 group"
                >
                  <div className="w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <feature.icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-[#ff6b35] transition-colors duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </div>
        </div>
      </div>

      {/* Orange Gradient Section with Slanted Transition */}
      <div className="relative">
        {/* Slanted divider */}
        <div className="absolute top-0 left-0 w-full overflow-hidden leading-none">
          <svg
            className="relative block w-full h-20"
            data-name="Layer 1"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 1200 120"
            preserveAspectRatio="none"
          >
            <path
              d="M1200 120L0 16.48 0 0 1200 0 1200 120z"
              className="fill-white"
            ></path>
          </svg>
        </div>

        {/* Orange gradient background */}
        <div className="bg-gradient-to-br from-[#ff6b35] via-[#f7931e] to-[#ff6b35] py-32 relative">
          {/* Enhanced Grid Background with Orange Glow */}
          <EnhancedGridBackground
            gridSize={55}
            opacity={0.12}
            color="#ffffff"
            variant="tech"
            animated={true}
            glowEffect={true}
            className="absolute inset-0"
          />

          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            {/* Features Grid - Second Half with White Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.slice(4).map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 15 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className="bg-white rounded-2xl p-8 shadow-xl border border-white/20 hover:shadow-2xl hover:border-white/40 transition-all duration-300 group backdrop-blur-sm"
                >
                  <div className="w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <feature.icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-[#ff6b35] transition-colors duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
