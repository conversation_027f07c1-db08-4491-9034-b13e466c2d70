#!/usr/bin/env node

/**
 * Test script to verify the native HTTP implementation works
 */

const https = require('https');
const { URL } = require('url');

// Native Node.js HTTP request implementation (same as in our fix)
function makeHttpRequest(url, options) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : require('http');
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: 30000, // 30s timeout
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        // Create a Response-like object
        const response = {
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          statusText: res.statusMessage || '',
          headers: res.headers,
          json: async () => JSON.parse(data),
          text: async () => data,
        };
        
        resolve(response);
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Network request failed: ${error.message}`));
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout after 30 seconds'));
    });

    // Send request body if provided
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// Test the implementation
async function testNativeFetch() {
  console.log('🧪 Testing Native HTTP Implementation');
  console.log('====================================\n');

  const tests = [
    {
      name: 'OpenRouter API',
      url: 'https://openrouter.ai/api/v1/chat/completions',
      options: {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-key',
          'User-Agent': 'RoKey/1.0 (Test)'
        },
        body: JSON.stringify({ test: true })
      }
    },
    {
      name: 'Google Gemini OpenAI API',
      url: 'https://generativelanguage.googleapis.com/v1beta/openai/chat/completions',
      options: {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-key',
          'User-Agent': 'RoKey/1.0 (Test)'
        },
        body: JSON.stringify({ test: true })
      }
    }
  ];

  for (const test of tests) {
    try {
      console.log(`Testing ${test.name}...`);
      const startTime = Date.now();
      const response = await makeHttpRequest(test.url, test.options);
      const duration = Date.now() - startTime;
      
      console.log(`✅ ${test.name}: HTTP ${response.status} (${duration}ms)`);
      
      // Try to get response text (should be error message since we're using test keys)
      try {
        const text = await response.text();
        console.log(`   Response preview: ${text.substring(0, 100)}...`);
      } catch (e) {
        console.log(`   Response received but couldn't parse text`);
      }
      
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.message}`);
    }
    console.log('');
  }

  console.log('🎉 Native HTTP implementation test complete!');
  console.log('If you see HTTP status codes (401, 400, etc.) above, the fix is working!');
  console.log('The "fetch failed" errors should now be resolved in your app.');
}

testNativeFetch().catch(console.error);
