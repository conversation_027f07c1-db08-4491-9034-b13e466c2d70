'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { registerServiceWorker } from '@/utils/serviceWorker';
import LandingNavbar from '@/components/landing/LandingNavbar';
import HeroSection from '@/components/landing/HeroSection';
import AIIntegrationsSection from '@/components/landing/AIIntegrationsSection';
import FeaturesSection from '@/components/landing/FeaturesSection';
import RoutingVisualization from '@/components/landing/RoutingVisualization';
import TestimonialsSection from '@/components/landing/TestimonialsSection';
import CTASection from '@/components/landing/CTASection';
import Footer from '@/components/landing/Footer';
import EnhancedGridBackground from '@/components/landing/EnhancedGridBackground';
import { useInstantNavigation } from '@/hooks/useInstantNavigation';

export default function LandingPage() {
  const [isLoaded, setIsLoaded] = useState(false);

  // Initialize instant navigation for all pages
  useInstantNavigation();

  useEffect(() => {
    // Register service worker for caching
    registerServiceWorker({
      onSuccess: () => console.log('✅ Service Worker registered for caching'),
      onUpdate: () => console.log('🔄 New content available'),
      onError: (error) => console.warn('⚠️ Service Worker registration failed:', error)
    });

    setIsLoaded(true);
  }, []);

  // Prefetch likely next pages on hover
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const prefetchPages = () => {
        // Prefetch auth pages since users likely to sign up
        const link1 = document.createElement('link');
        link1.rel = 'prefetch';
        link1.href = '/pricing';
        document.head.appendChild(link1);

        const link2 = document.createElement('link');
        link2.rel = 'prefetch';
        link2.href = '/auth/signup';
        document.head.appendChild(link2);

        const link3 = document.createElement('link');
        link3.rel = 'prefetch';
        link3.href = '/features';
        document.head.appendChild(link3);
      };

      // Prefetch after initial load
      const prefetchTimer = setTimeout(prefetchPages, 1000);
      return () => clearTimeout(prefetchTimer);
    }
  }, []);

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center relative overflow-hidden" style={{
        background: `radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)`
      }}>
        {/* Enhanced Grid Background */}
        <div
          className="absolute inset-0 opacity-[0.08]"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 107, 53, 0.3) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 107, 53, 0.3) 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px',
            animation: 'gridPulse 4s ease-in-out infinite'
          }}
        />

        {/* Ambient glow effects */}
        <div className="absolute inset-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-[#ff6b35]/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-[#f7931e]/10 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        </div>

        <div className="text-center relative z-10">
          {/* Logo with enhanced glow effect */}
          <div className="mb-8 relative">
            <div className="relative inline-block">
              <Image
                src="/RouKey_Logo_GLOW.png"
                alt="RouKey"
                width={80}
                height={80}
                className="w-20 h-20 mx-auto transition-transform duration-300 hover:scale-110"
                priority
              />
              {/* Enhanced glow effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-full opacity-30 blur-xl animate-pulse"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-full opacity-20 blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
            </div>
          </div>

          {/* Enhanced spinner */}
          <div className="mb-6 relative">
            <div className="relative inline-block">
              {/* Outer ring */}
              <div className="animate-spin rounded-full h-16 w-16 border-2 border-transparent bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-1">
                <div className="rounded-full h-full w-full bg-[#040716]"></div>
              </div>
              {/* Inner spinning dot */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="w-3 h-3 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>

          {/* Enhanced text with gradient */}
          <div className="space-y-2">
            <h2 className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              RouKey
            </h2>
            <p className="text-gray-400 text-sm animate-pulse">Loading your intelligent router...</p>
          </div>

          {/* Loading progress dots */}
          <div className="flex justify-center space-x-2 mt-6">
            <div className="w-2 h-2 bg-[#ff6b35] rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-[#ff6b35] rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-2 h-2 bg-[#ff6b35] rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>

        {/* Custom CSS for grid animation */}
        <style jsx>{`
          @keyframes gridPulse {
            0%, 100% { opacity: 0.08; }
            50% { opacity: 0.12; }
          }
        `}</style>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden" style={{
      background: `radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)`
    }}>
      {/* Main Grid Background - Reduced Opacity */}
      <EnhancedGridBackground
        gridSize={60}
        opacity={0.032}
        color="#000000"
        variant="subtle"
        animated={true}
        className="fixed inset-0"
      />

      {/* Tech Grid Overlay for Hero Section */}
      <div className="absolute inset-0 z-0">
        <div className="h-screen relative">
          <EnhancedGridBackground
            gridSize={45}
            opacity={0.024}
            color="#ff6b35"
            variant="tech"
            animated={true}
            glowEffect={true}
            className="absolute inset-0"
          />
        </div>
      </div>

      {/* Premium Grid for Features Section */}
      <div className="absolute inset-0 z-0" style={{ top: '200vh' }}>
        <div className="h-screen relative">
          <EnhancedGridBackground
            gridSize={35}
            opacity={0.056}
            color="#000000"
            variant="premium"
            animated={true}
            className="absolute inset-0"
          />
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10">
        <LandingNavbar />

        <main>
          <HeroSection />
          <AIIntegrationsSection />
          <RoutingVisualization />
          <FeaturesSection />
          <CTASection />
          <TestimonialsSection />
        </main>

        <Footer />
      </div>
    </div>
  );

}
