import { AsyncJobManager, AsyncJob } from './jobManager';

export class AsyncProcessor {
  private jobManager: AsyncJobManager;
  private isProcessing: boolean = false;

  constructor() {
    this.jobManager = new AsyncJobManager();
  }

  /**
   * Processes an async job
   */
  async processJob(jobId: string): Promise<void> {
    const job = await this.jobManager.getJob(jobId);
    if (!job) {
      throw new Error(`Job ${jobId} not found`);
    }

    if (job.status !== 'pending') {
      throw new Error(`Job ${jobId} is not in pending status`);
    }

    try {
      // Mark job as started
      await this.jobManager.startJob(jobId);
      
      // Process the request
      const result = await this.executeRequest(job);
      
      // Mark as completed
      await this.jobManager.completeJob(jobId, result.response, result.rolesUsed);
      
      // Send webhook if configured
      if (job.webhook_url) {
        await this.sendWebhook(job.webhook_url, {
          job_id: jobId,
          status: 'completed',
          result: result.response,
          roles_used: result.rolesUsed
        });
      }

    } catch (error: any) {
      console.error(`Error processing job ${jobId}:`, error);
      await this.jobManager.failJob(jobId, error.message);
      
      // Send webhook for failure
      if (job.webhook_url) {
        await this.sendWebhook(job.webhook_url, {
          job_id: jobId,
          status: 'failed',
          error: error.message
        });
      }
    }
  }

  /**
   * Executes the actual request with progress tracking
   */
  private async executeRequest(job: AsyncJob): Promise<{
    response: any;
    rolesUsed: string[];
  }> {
    const { request_data } = job;
    
    // Update progress
    await this.jobManager.updateProgress(job.id, 20, 'Starting request processing...');

    // Make the internal API call
    const internalApiUrl = process.env.NEXT_PUBLIC_SITE_URL 
      ? `${process.env.NEXT_PUBLIC_SITE_URL}/api/v1/chat/completions`
      : 'http://localhost:3000/api/v1/chat/completions';

    // Update progress
    await this.jobManager.updateProgress(job.id, 40, 'Calling internal API...');

    const response = await fetch(internalApiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.ROKEY_API_ACCESS_TOKEN}`,
        'X-User-API-Key-ID': job.api_key_id,
        'X-Async-Job-ID': job.id,
      },
      body: JSON.stringify({
        ...request_data,
        _internal_user_id: job.user_id,
        _async_processing: true
      }),
    });

    // Update progress
    await this.jobManager.updateProgress(job.id, 70, 'Processing response...');

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Internal API error: ${response.status} ${errorText}`);
    }

    const responseData = await response.json();
    
    // Extract roles used from metadata
    const rolesUsed = responseData.rokey_metadata?.roles_used || [];

    // Update progress
    await this.jobManager.updateProgress(job.id, 90, 'Finalizing...');

    return {
      response: responseData,
      rolesUsed
    };
  }

  /**
   * Sends webhook notification
   */
  private async sendWebhook(webhookUrl: string, payload: any): Promise<void> {
    try {
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'RouKey-Async-Processor/1.0'
        },
        body: JSON.stringify({
          ...payload,
          timestamp: new Date().toISOString(),
          source: 'rokey-async'
        }),
      });

      if (!response.ok) {
        console.warn(`Webhook failed: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.warn('Webhook delivery failed:', error);
    }
  }

  /**
   * Processes pending jobs (call this periodically)
   */
  async processPendingJobs(maxConcurrent: number = 3): Promise<void> {
    if (this.isProcessing) {
      console.log('Already processing jobs, skipping...');
      return;
    }

    this.isProcessing = true;

    try {
      // Get pending jobs
      const { data: pendingJobs } = await this.jobManager['supabase']
        .from('async_jobs')
        .select('id')
        .eq('status', 'pending')
        .order('created_at', { ascending: true })
        .limit(maxConcurrent);

      if (!pendingJobs || pendingJobs.length === 0) {
        return;
      }

      console.log(`Processing ${pendingJobs.length} pending jobs...`);

      // Process jobs concurrently
      const promises = pendingJobs.map(job => 
        this.processJob(job.id).catch(error => {
          console.error(`Failed to process job ${job.id}:`, error);
        })
      );

      await Promise.all(promises);

    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Handles timeout for long-running jobs
   */
  async handleTimeouts(): Promise<void> {
    const timeoutThreshold = new Date(Date.now() - 10 * 60 * 1000); // 10 minutes

    const { data: timedOutJobs } = await this.jobManager['supabase']
      .from('async_jobs')
      .select('id, webhook_url')
      .eq('status', 'processing')
      .lt('started_at', timeoutThreshold.toISOString());

    if (!timedOutJobs || timedOutJobs.length === 0) {
      return;
    }

    console.log(`Handling ${timedOutJobs.length} timed out jobs...`);

    for (const job of timedOutJobs) {
      await this.jobManager.updateJob(job.id, {
        status: 'timeout',
        completed_at: new Date().toISOString(),
        error_message: 'Job timed out after 10 minutes'
      });

      // Send webhook for timeout
      if (job.webhook_url) {
        await this.sendWebhook(job.webhook_url, {
          job_id: job.id,
          status: 'timeout',
          error: 'Job timed out after 10 minutes'
        });
      }
    }
  }
}
