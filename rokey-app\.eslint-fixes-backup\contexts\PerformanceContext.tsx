'use client';

import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { usePathname } from 'next/navigation';
import { registerServiceWorker, preloadCriticalResources } from '@/utils/serviceWorker';
import { useAutoPrefetch, useSmartPrefetch } from '@/hooks/useDataPrefetch';

interface PerformanceContextType {
  isOptimized: boolean;
  navigationTime: number;
  cacheHitRate: number;
  prefetchStats: {
    routesPrefetched: number;
    dataPrefetched: number;
  };
  clearAllCaches: () => void;
  forceOptimization: () => void;
}

const PerformanceContext = createContext<PerformanceContextType | undefined>(undefined);

interface PerformanceProviderProps {
  children: React.ReactNode;
}

export function PerformanceProvider({ children }: PerformanceProviderProps) {
  const pathname = usePathname();
  const [isOptimized, setIsOptimized] = useState(false);
  const [navigationTime, setNavigationTime] = useState(0);
  const [cacheHitRate, setCacheHitRate] = useState(0);
  const [prefetchStats, setPrefetchStats] = useState({
    routesPrefetched: 0,
    dataPrefetched: 0,
  });

  const navigationStartTime = useRef<number>(Date.now());
  const hasInitialized = useRef(false);

  // Initialize performance optimizations
  useEffect(() => {
    if (hasInitialized.current) return;
    hasInitialized.current = true;

    console.log('🚀 Initializing performance optimizations...');

    // Register service worker
    registerServiceWorker({
      onSuccess: () => {
        console.log('✅ Service Worker registered successfully');
        setIsOptimized(true);
      },
      onUpdate: () => {
        console.log('🔄 Service Worker updated');
      },
      onError: (error) => {
        console.error('❌ Service Worker registration failed:', error);
      }
    });

    // Preload critical resources after a short delay
    setTimeout(() => {
      preloadCriticalResources();
      console.log('📦 Critical resources preloaded');
    }, 1000);

    // Initialize performance monitoring
    if (typeof window !== 'undefined' && 'performance' in window) {
      // Monitor navigation timing
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            const totalTime = navEntry.loadEventEnd - navEntry.startTime;
            setNavigationTime(totalTime);
            console.log(`📊 Navigation timing: ${totalTime}ms`);
          }
        }
      });

      try {
        observer.observe({ entryTypes: ['navigation'] });
      } catch (e) {
        console.warn('Performance Observer not supported');
      }

      return () => observer.disconnect();
    }
  }, []);

  // Track navigation performance
  useEffect(() => {
    const startTime = Date.now();
    navigationStartTime.current = startTime;

    return () => {
      const endTime = Date.now();
      const navTime = endTime - startTime;
      setNavigationTime(navTime);
      
      if (navTime < 500) {
        console.log(`✅ Fast navigation to ${pathname}: ${navTime}ms`);
      } else if (navTime > 2000) {
        console.warn(`⚠️ Slow navigation to ${pathname}: ${navTime}ms`);
      }
    };
  }, [pathname]);

  // Auto-prefetch critical data
  useAutoPrefetch();

  // Smart prefetch based on current route
  useSmartPrefetch(pathname);

  // Monitor cache performance
  useEffect(() => {
    const updateCacheStats = async () => {
      if ('caches' in window) {
        try {
          const cacheNames = await caches.keys();
          let totalRequests = 0;
          let cacheHits = 0;

          for (const cacheName of cacheNames) {
            const cache = await caches.open(cacheName);
            const requests = await cache.keys();
            totalRequests += requests.length;
            // Simplified cache hit calculation
            cacheHits += Math.floor(requests.length * 0.7); // Estimate 70% hit rate
          }

          const hitRate = totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0;
          setCacheHitRate(hitRate);
        } catch (error) {
          console.warn('Failed to calculate cache stats:', error);
        }
      }
    };

    const interval = setInterval(updateCacheStats, 30000); // Update every 30 seconds
    updateCacheStats(); // Initial calculation

    return () => clearInterval(interval);
  }, []);

  const clearAllCaches = async () => {
    if ('caches' in window) {
      try {
        const cacheNames = await caches.keys();
        await Promise.all(cacheNames.map(name => caches.delete(name)));
        console.log('🗑️ All caches cleared');
        setCacheHitRate(0);
      } catch (error) {
        console.error('Failed to clear caches:', error);
      }
    }
  };

  const forceOptimization = () => {
    console.log('🔧 Forcing optimization...');
    preloadCriticalResources();
    setIsOptimized(true);
  };

  const value: PerformanceContextType = {
    isOptimized,
    navigationTime,
    cacheHitRate,
    prefetchStats,
    clearAllCaches,
    forceOptimization,
  };

  return (
    <PerformanceContext.Provider value={value}>
      {children}
    </PerformanceContext.Provider>
  );
}

export function usePerformance() {
  const context = useContext(PerformanceContext);
  if (context === undefined) {
    throw new Error('usePerformance must be used within a PerformanceProvider');
  }
  return context;
}

// Hook for performance monitoring in components
export function usePerformanceMonitor(componentName: string) {
  const renderStartTime = useRef<number>(Date.now());
  const [renderTime, setRenderTime] = useState<number>(0);

  useEffect(() => {
    const endTime = Date.now();
    const duration = endTime - renderStartTime.current;
    setRenderTime(duration);
    
    if (duration > 100) {
      console.warn(`⚠️ Slow render in ${componentName}: ${duration}ms`);
    }
  }, [componentName]);

  return { renderTime };
}
