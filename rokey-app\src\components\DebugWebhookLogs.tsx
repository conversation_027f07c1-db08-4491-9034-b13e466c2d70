'use client';

import { useState, useEffect } from 'react';

interface WebhookLog {
  id: string;
  timestamp: string;
  eventType: string;
  subscriptionId?: string;
  customerId?: string;
  priceId?: string;
  tier?: string;
  status?: string;
  details: any;
}

interface WebhookLogsResponse {
  total: number;
  recent: WebhookLog[];
  lastUpdated: string;
}

export default function DebugWebhookLogs() {
  const [logs, setLogs] = useState<WebhookLogsResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(false);

  const fetchLogs = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/debug/webhook-logs', {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setLogs(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      console.error('Webhook logs fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const clearLogs = async () => {
    try {
      const response = await fetch('/api/debug/webhook-logs', {
        method: 'DELETE'
      });

      if (response.ok) {
        setLogs({ total: 0, recent: [], lastUpdated: new Date().toISOString() });
      }
    } catch (err) {
      console.error('Failed to clear logs:', err);
    }
  };

  useEffect(() => {
    fetchLogs();
  }, []);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(fetchLogs, 5000); // Refresh every 5 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const getEventTypeColor = (eventType: string) => {
    switch (eventType) {
      case 'customer.subscription.updated':
        return 'bg-blue-100 text-blue-800';
      case 'customer.subscription.created':
        return 'bg-green-100 text-green-800';
      case 'customer.subscription.deleted':
        return 'bg-red-100 text-red-800';
      case 'checkout.session.completed':
        return 'bg-purple-100 text-purple-800';
      case 'invoice.payment_succeeded':
        return 'bg-green-100 text-green-800';
      case 'invoice.payment_failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">🔗 Webhook Event Logs</h3>
        <div className="flex items-center space-x-2">
          <label className="flex items-center space-x-2 text-sm">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded"
            />
            <span>Auto-refresh</span>
          </label>
          <button
            onClick={fetchLogs}
            disabled={loading}
            className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Refresh'}
          </button>
          <button
            onClick={clearLogs}
            className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
          >
            Clear
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800 text-sm">Error: {error}</p>
        </div>
      )}

      {logs && (
        <div className="space-y-4">
          <div className="flex justify-between items-center text-sm text-gray-600">
            <span>Total events: {logs.total}</span>
            <span>Last updated: {new Date(logs.lastUpdated).toLocaleString()}</span>
          </div>

          {logs.recent.length > 0 ? (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {logs.recent.map((log) => (
                <div key={log.id} className="p-3 bg-gray-50 border rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getEventTypeColor(log.eventType)}`}>
                        {log.eventType}
                      </span>
                      {log.tier && (
                        <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs font-medium">
                          {log.tier}
                        </span>
                      )}
                      {log.status && (
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          log.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {log.status}
                        </span>
                      )}
                    </div>
                    <span className="text-xs text-gray-500">
                      {new Date(log.timestamp).toLocaleString()}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
                    {log.subscriptionId && (
                      <div>
                        <span className="font-medium">Subscription:</span>
                        <br />
                        <span className="font-mono bg-gray-200 px-1 rounded">{log.subscriptionId}</span>
                      </div>
                    )}
                    {log.customerId && (
                      <div>
                        <span className="font-medium">Customer:</span>
                        <br />
                        <span className="font-mono bg-gray-200 px-1 rounded">{log.customerId}</span>
                      </div>
                    )}
                    {log.priceId && (
                      <div>
                        <span className="font-medium">Price ID:</span>
                        <br />
                        <span className="font-mono bg-gray-200 px-1 rounded">{log.priceId}</span>
                      </div>
                    )}
                  </div>

                  {log.details && Object.keys(log.details).length > 0 && (
                    <details className="mt-2">
                      <summary className="text-xs text-gray-600 cursor-pointer hover:text-gray-800">
                        View details
                      </summary>
                      <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                        {JSON.stringify(log.details, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>No webhook events logged yet.</p>
              <p className="text-sm mt-1">Events will appear here when webhooks are received.</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
