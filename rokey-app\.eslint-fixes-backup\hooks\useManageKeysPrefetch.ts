'use client';

import { useState, useCallback, useRef } from 'react';

interface ManageKeysData {
  configDetails: any;
  apiKeys: any[];
  userCustomRoles: any[];
  models: any[];
  defaultChatKeyId: string | null;
}

interface PrefetchCache {
  [configId: string]: {
    data: ManageKeysData;
    timestamp: number;
    isLoading: boolean;
  };
}

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const prefetchCache: PrefetchCache = {};

export function useManageKeysPrefetch() {
  const [prefetchStatus, setPrefetchStatus] = useState<{
    [configId: string]: 'idle' | 'loading' | 'success' | 'error';
  }>({});

  const abortControllers = useRef<{ [configId: string]: AbortController }>({});

  // Check if data is cached and fresh
  const isCached = useCallback((configId: string) => {
    const cached = prefetchCache[configId];
    if (!cached) return false;
    
    const isExpired = Date.now() - cached.timestamp > CACHE_DURATION;
    return !isExpired && !cached.isLoading;
  }, []);

  // Get cached data
  const getCachedData = useCallback((configId: string): ManageKeysData | null => {
    const cached = prefetchCache[configId];
    if (!cached || cached.isLoading) return null;
    
    const isExpired = Date.now() - cached.timestamp > CACHE_DURATION;
    if (isExpired) {
      delete prefetchCache[configId];
      return null;
    }
    
    return cached.data;
  }, []);

  // Prefetch all data needed for manage keys page
  const prefetchManageKeysData = useCallback(async (configId: string, priority: 'high' | 'medium' | 'low' = 'medium') => {
    // Check if already cached
    if (isCached(configId)) {
      console.log(`🚀 [MANAGE KEYS PREFETCH] Using cached data for config: ${configId}`);
      return getCachedData(configId);
    }

    // Check if already loading
    if (prefetchCache[configId]?.isLoading) {
      console.log(`🔄 [MANAGE KEYS PREFETCH] Already loading config: ${configId}`);
      return null;
    }

    // Cancel any existing request for this config
    if (abortControllers.current[configId]) {
      abortControllers.current[configId].abort();
    }

    // Create new abort controller
    const abortController = new AbortController();
    abortControllers.current[configId] = abortController;

    // Set loading state
    prefetchCache[configId] = {
      data: {} as ManageKeysData,
      timestamp: Date.now(),
      isLoading: true
    };

    setPrefetchStatus(prev => ({ ...prev, [configId]: 'loading' }));

    try {
      console.log(`🚀 [MANAGE KEYS PREFETCH] Starting prefetch for config: ${configId} (priority: ${priority})`);

      // Add delay for lower priority requests
      if (priority === 'low') {
        await new Promise(resolve => setTimeout(resolve, 200));
      } else if (priority === 'medium') {
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      // Fetch all data in parallel
      const [
        configResponse,
        keysResponse,
        customRolesResponse,
        modelsResponse,
        defaultKeyResponse
      ] = await Promise.allSettled([
        fetch('/api/custom-configs', { signal: abortController.signal }),
        fetch(`/api/keys?custom_config_id=${configId}`, { signal: abortController.signal }),
        fetch('/api/user/custom-roles', { signal: abortController.signal }),
        fetch('/api/providers/list-models', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({}),
          signal: abortController.signal
        }),
        fetch(`/api/custom-configs/${configId}/default-chat-key`, { signal: abortController.signal })
      ]);

      // Process responses
      let configDetails = null;
      let apiKeys: any[] = [];
      let userCustomRoles: any[] = [];
      let models: any[] = [];
      let defaultChatKeyId: string | null = null;

      // Process config details
      if (configResponse.status === 'fulfilled' && configResponse.value.ok) {
        const allConfigs = await configResponse.value.json();
        configDetails = allConfigs.find((c: any) => c.id === configId);
      }

      // Process API keys
      if (keysResponse.status === 'fulfilled' && keysResponse.value.ok) {
        apiKeys = await keysResponse.value.json();
      }

      // Process custom roles
      if (customRolesResponse.status === 'fulfilled' && customRolesResponse.value.ok) {
        userCustomRoles = await customRolesResponse.value.json();
      }

      // Process models
      if (modelsResponse.status === 'fulfilled' && modelsResponse.value.ok) {
        const modelsData = await modelsResponse.value.json();
        models = modelsData.models || [];
      }

      // Process default chat key
      if (defaultKeyResponse.status === 'fulfilled' && defaultKeyResponse.value.ok) {
        const defaultKeyData = await defaultKeyResponse.value.json();
        defaultChatKeyId = defaultKeyData?.id || null;
      }

      const prefetchedData: ManageKeysData = {
        configDetails,
        apiKeys,
        userCustomRoles,
        models,
        defaultChatKeyId
      };

      // Cache the data
      prefetchCache[configId] = {
        data: prefetchedData,
        timestamp: Date.now(),
        isLoading: false
      };

      setPrefetchStatus(prev => ({ ...prev, [configId]: 'success' }));

      console.log(`✅ [MANAGE KEYS PREFETCH] Successfully prefetched data for config: ${configId}`, {
        configFound: !!configDetails,
        keysCount: apiKeys.length,
        customRolesCount: userCustomRoles.length,
        modelsCount: models.length,
        hasDefaultKey: !!defaultChatKeyId
      });

      return prefetchedData;

    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log(`🚫 [MANAGE KEYS PREFETCH] Aborted prefetch for config: ${configId}`);
        return null;
      }

      console.error(`❌ [MANAGE KEYS PREFETCH] Failed to prefetch data for config: ${configId}`, error);
      
      // Remove from cache on error
      delete prefetchCache[configId];
      setPrefetchStatus(prev => ({ ...prev, [configId]: 'error' }));
      
      return null;
    } finally {
      // Clean up abort controller
      delete abortControllers.current[configId];
    }
  }, [isCached, getCachedData]);

  // Prefetch on hover
  const createHoverPrefetch = useCallback((configId: string) => {
    return {
      onMouseEnter: () => {
        if (!isCached(configId)) {
          prefetchManageKeysData(configId, 'high');
        }
      }
    };
  }, [prefetchManageKeysData, isCached]);

  // Clear cache for a specific config
  const clearCache = useCallback((configId: string) => {
    delete prefetchCache[configId];
    setPrefetchStatus(prev => {
      const newStatus = { ...prev };
      delete newStatus[configId];
      return newStatus;
    });
  }, []);

  // Clear all cache
  const clearAllCache = useCallback(() => {
    Object.keys(prefetchCache).forEach(configId => {
      delete prefetchCache[configId];
    });
    setPrefetchStatus({});
  }, []);

  // Get prefetch status
  const getStatus = useCallback((configId: string) => {
    return prefetchStatus[configId] || 'idle';
  }, [prefetchStatus]);

  // Get cache info
  const getCacheInfo = useCallback(() => {
    return {
      cachedConfigs: Object.keys(prefetchCache),
      cacheSize: Object.keys(prefetchCache).length,
      totalCacheAge: Object.values(prefetchCache).reduce((total, cache) => {
        return total + (Date.now() - cache.timestamp);
      }, 0) / Object.keys(prefetchCache).length
    };
  }, []);

  return {
    prefetchManageKeysData,
    getCachedData,
    isCached,
    createHoverPrefetch,
    clearCache,
    clearAllCache,
    getStatus,
    getCacheInfo,
    prefetchStatus
  };
}
