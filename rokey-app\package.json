{"name": "rokey-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:clean": "node clear-dev-cache.js && next dev", "prebuild:disabled": "npm run lint", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "eslint .", "clear-cache": "node clear-dev-cache.js"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emailjs/browser": "^4.4.1", "@google-ai/generativelanguage": "^3.2.0", "@google/generative-ai": "^0.24.1", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@langchain/community": "^0.3.47", "@langchain/core": "^0.3.59", "@langchain/google-genai": "^0.1.12", "@langchain/langgraph": "^0.3.4", "@langchain/textsplitters": "^0.1.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@tailwindcss/line-clamp": "^0.4.4", "@types/multer": "^1.4.13", "@vercel/speed-insights": "^1.2.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "framer-motion": "^12.18.1", "google-auth-library": "^10.0.0-rc.2", "lucide-react": "^0.516.0", "mammoth": "^1.9.1", "multer": "^2.0.1", "next": "^15.0.3", "openai": "^5.0.1", "pdf-parse": "^1.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "react-tooltip": "^5.28.1", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "sonner": "^2.0.5", "stripe": "^17.7.0", "unist-util-visit": "^5.0.0", "zod": "^3.24.1"}, "devDependencies": {"@eslint/compat": "^1.2.0", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.15.0", "@next/bundle-analyzer": "^15.3.3", "@tailwindcss/postcss": "^4", "@types/canvas-confetti": "^1.9.0", "@types/node": "^22", "@types/pdf-parse": "^1.1.5", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^9.15.0", "eslint-config-next": "^15.3.3", "eslint-plugin-react-hooks": "^5.0.0", "ignore-loader": "^0.1.2", "tailwindcss": "^4", "typescript": "^5", "typescript-eslint": "^8.34.0"}, "overrides": {"eslint": "$eslint", "openai": "^5.0.1", "@browserbasehq/stagehand": {"openai": "^5.0.1"}}, "resolutions": {"openai": "^5.0.1", "@langchain/core": "^0.3.59"}}