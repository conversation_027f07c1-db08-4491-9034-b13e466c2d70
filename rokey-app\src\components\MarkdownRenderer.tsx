'use client';

import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { visit } from 'unist-util-visit';
import { Suspense, useState, useEffect, Component, ReactNode } from 'react';
import CopyButton from './CopyButton';

// Error Boundary component for handling markdown rendering errors
class ErrorBoundary extends Component<
  { children: ReactNode; onError: () => void },
  { hasError: boolean }
> {
  constructor(props: { children: ReactNode; onError: () => void }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch() {
    this.props.onError();
  }

  render() {
    if (this.state.hasError) {
      return null; // Let parent component handle the error display
    }
    return this.props.children;
  }
}

// Custom remark plugin to prevent code blocks from being wrapped in paragraphs
function remarkUnwrapCodeBlocks() {
  return (tree: any) => {
    visit(tree, 'paragraph', (node, index, parent) => {
      if (node.children.length === 1 && node.children[0].type === 'code' &&
          typeof index === 'number' && parent && parent.children) {
        // Replace paragraph containing only a code block with the code block itself
        parent.children[index] = node.children[0];
      }
    });
  };
}

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

export default function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    setHasError(false);
  }, [content]);

  if (hasError) {
    // Fallback to simple text rendering if markdown fails
    return (
      <div className={`markdown-content ${className}`}>
        <pre className="whitespace-pre-wrap text-sm text-white leading-relaxed">
          {content}
        </pre>
      </div>
    );
  }

  return (
    <div className={`markdown-content ${className}`}>
      <Suspense fallback={
        <div className="animate-pulse bg-gray-100 rounded p-4">
          <div className="h-4 bg-gray-300 rounded mb-2"></div>
          <div className="h-4 bg-gray-300 rounded mb-2 w-3/4"></div>
          <div className="h-4 bg-gray-300 rounded w-1/2"></div>
        </div>
      }>
        <ErrorBoundary onError={() => setHasError(true)}>
          <ReactMarkdown
            remarkPlugins={[remarkGfm, remarkUnwrapCodeBlocks]}
          components={{
        // Headers
        h1: ({ children }) => (
          <h1 className="text-xl font-bold mb-3 mt-4 first:mt-0 text-white">
            {children}
          </h1>
        ),
        h2: ({ children }) => (
          <h2 className="text-lg font-bold mb-2 mt-3 first:mt-0 text-white">
            {children}
          </h2>
        ),
        h3: ({ children }) => (
          <h3 className="text-base font-bold mb-2 mt-3 first:mt-0 text-white">
            {children}
          </h3>
        ),
        h4: ({ children }) => (
          <h4 className="text-sm font-bold mb-1 mt-2 first:mt-0 text-white">
            {children}
          </h4>
        ),

        // Paragraphs
        p: ({ children }) => (
          <p className="mb-3 last:mb-0 leading-relaxed text-white break-words">
            {children}
          </p>
        ),
        
        // Bold and italic
        strong: ({ children }) => (
          <strong className="font-bold text-white">
            {children}
          </strong>
        ),
        em: ({ children }) => (
          <em className="italic text-white">
            {children}
          </em>
        ),

        // Lists
        ul: ({ children }) => (
          <ul className="list-disc list-inside mb-3 space-y-1 text-white">
            {children}
          </ul>
        ),
        ol: ({ children }) => (
          <ol className="list-decimal list-inside mb-3 space-y-1 text-white">
            {children}
          </ol>
        ),
        li: ({ children }) => (
          <li className="leading-relaxed text-white">
            {children}
          </li>
        ),
        
        // Code blocks and inline code
        code: ({ node, inline, className, children, ...props }: any) => {
          const match = /language-(\w+)/.exec(className || '');
          const language = match ? match[1] : '';
          const codeContent = String(children).replace(/\n$/, '');

          if (!inline) {
            // Check if this is a short single-line code snippet that should be treated as enhanced inline
            const isShortSnippet = codeContent.length <= 60 && !codeContent.includes('\n') && !language;

            if (isShortSnippet) {
              // Treat short snippets as enhanced inline code with subtle highlighting
              return (
                <code className="bg-orange-50 text-orange-700 px-1.5 py-0.5 rounded text-sm font-mono border border-orange-200">
                  {codeContent}
                </code>
              );
            }

            // Handle actual code blocks (both with and without language detection)
            if (language) {
              // Code block with syntax highlighting
              return (
                <div className="my-3 rounded-lg overflow-hidden relative group">
                  {/* Copy button for code blocks - positioned on top right with better visibility */}
                  <div className="absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200">
                    <CopyButton
                      text={codeContent}
                      variant="code"
                      size="sm"
                      title="Copy code"
                      className="bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50"
                    />
                  </div>
                  <SyntaxHighlighter
                    style={oneDark}
                    language={language}
                    PreTag="div"
                    className="text-sm"
                    {...props}
                  >
                    {codeContent}
                  </SyntaxHighlighter>
                </div>
              );
            } else {
              // Multi-line code block without language (plain text code block)
              return (
                <div className="my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100">
                  {/* Copy button for plain code blocks */}
                  <div className="absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200">
                    <CopyButton
                      text={codeContent}
                      variant="code"
                      size="sm"
                      title="Copy code"
                      className="bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50"
                    />
                  </div>
                  <pre className="p-4 text-sm font-mono overflow-x-auto">
                    <code>{codeContent}</code>
                  </pre>
                </div>
              );
            }
          }
          
          return (
            <code
              className="bg-gray-800 text-gray-100 px-1.5 py-0.5 rounded text-sm font-mono"
              {...props}
            >
              {children}
            </code>
          );
        },

        // Blockquotes
        blockquote: ({ children }) => (
          <blockquote className="border-l-4 border-orange-500 pl-4 my-3 italic text-gray-300">
            {children}
          </blockquote>
        ),

        // Links
        a: ({ children, href }) => (
          <a
            href={href}
            target="_blank"
            rel="noopener noreferrer"
            className="text-orange-600 hover:text-orange-700 underline transition-colors duration-200"
          >
            {children}
          </a>
        ),
        
        // Tables
        table: ({ children }) => (
          <div className="overflow-x-auto my-3">
            <table className="min-w-full border border-gray-600 rounded-lg">
              {children}
            </table>
          </div>
        ),
        thead: ({ children }) => (
          <thead className="bg-gray-800">
            {children}
          </thead>
        ),
        tbody: ({ children }) => (
          <tbody className="divide-y divide-gray-600 bg-gray-900">
            {children}
          </tbody>
        ),
        tr: ({ children }) => (
          <tr className="hover:bg-gray-800">
            {children}
          </tr>
        ),
        th: ({ children }) => (
          <th className="px-3 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider border-b border-gray-600">
            {children}
          </th>
        ),
        td: ({ children }) => (
          <td className="px-3 py-2 text-sm text-white border-b border-gray-600">
            {children}
          </td>
        ),
        
        // Horizontal rule
        hr: () => (
          <hr className="my-4 border-gray-600" />
        ),
        }}
        >
          {content}
        </ReactMarkdown>
        </ErrorBoundary>
      </Suspense>
    </div>
  );
}
