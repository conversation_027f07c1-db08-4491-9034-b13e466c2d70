#!/usr/bin/env node

/**
 * Phase 1 Optimization Test Script
 * Tests the performance improvements implemented in Phase 1
 */

const https = require('https');
const crypto = require('crypto');

// Configuration
const API_BASE_URL = 'http://localhost:3000';
const API_TOKEN = process.env.ROKEY_API_ACCESS_TOKEN;
const TEST_CONFIG_ID = process.env.TEST_CONFIG_ID || 'your-test-config-id';

if (!API_TOKEN) {
  console.error('❌ ROKEY_API_ACCESS_TOKEN environment variable is required');
  process.exit(1);
}

// Test data
const testMessages = [
  { role: 'user', content: 'Hello, how are you?' },
  { role: 'user', content: 'What is the capital of France?' },
  { role: 'user', content: 'Explain quantum computing in simple terms' },
  { role: 'user', content: 'Write a short poem about technology' },
];

// Performance tracking
const performanceMetrics = {
  requests: [],
  cacheHits: {
    training: 0,
    routing: 0
  },
  totalRequests: 0
};

async function makeRequest(messages, testName) {
  const startTime = Date.now();
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        custom_api_config_id: TEST_CONFIG_ID,
        messages: messages,
        stream: false,
        temperature: 0.7,
        max_tokens: 100
      })
    });

    const endTime = Date.now();
    const duration = endTime - startTime;
    
    const result = {
      testName,
      duration,
      status: response.status,
      success: response.ok
    };

    if (response.ok) {
      const data = await response.json();
      result.responseLength = JSON.stringify(data).length;
      console.log(`✅ ${testName}: ${duration}ms (${response.status})`);
    } else {
      const errorText = await response.text();
      result.error = errorText;
      console.log(`❌ ${testName}: ${duration}ms (${response.status}) - ${errorText.substring(0, 100)}`);
    }

    performanceMetrics.requests.push(result);
    performanceMetrics.totalRequests++;
    
    return result;
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`❌ ${testName}: ${duration}ms - ${error.message}`);
    
    const result = {
      testName,
      duration,
      success: false,
      error: error.message
    };
    
    performanceMetrics.requests.push(result);
    performanceMetrics.totalRequests++;
    
    return result;
  }
}

async function testCaching() {
  console.log('\n🧪 Testing Phase 1 Optimizations...\n');

  // Test 1: First request (should populate caches)
  console.log('📊 Test 1: Initial request (cache population)');
  await makeRequest([testMessages[0]], 'Initial Request');
  
  // Small delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Test 2: Identical request (should hit training data cache)
  console.log('\n📊 Test 2: Identical request (training data cache test)');
  await makeRequest([testMessages[0]], 'Cache Test - Same Config');

  // Test 3: Similar request (should hit routing cache)
  console.log('\n📊 Test 3: Similar request (routing cache test)');
  await makeRequest([{ role: 'user', content: 'Hello, how are you doing?' }], 'Cache Test - Similar Query');

  // Test 4: Different request (should miss caches but use optimized fetch)
  console.log('\n📊 Test 4: Different request (optimized fetch test)');
  await makeRequest([testMessages[2]], 'New Request - Optimized Fetch');

  // Test 5: Parallel requests (test connection reuse)
  console.log('\n📊 Test 5: Parallel requests (connection reuse test)');
  const parallelPromises = [
    makeRequest([testMessages[1]], 'Parallel Request 1'),
    makeRequest([testMessages[3]], 'Parallel Request 2'),
    makeRequest([{ role: 'user', content: 'What is 2+2?' }], 'Parallel Request 3')
  ];
  
  await Promise.all(parallelPromises);
}

function analyzeResults() {
  console.log('\n📈 Performance Analysis\n');
  
  const successfulRequests = performanceMetrics.requests.filter(r => r.success);
  const failedRequests = performanceMetrics.requests.filter(r => !r.success);
  
  if (successfulRequests.length === 0) {
    console.log('❌ No successful requests to analyze');
    return;
  }

  const durations = successfulRequests.map(r => r.duration);
  const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
  const minDuration = Math.min(...durations);
  const maxDuration = Math.max(...durations);
  
  console.log(`📊 Request Statistics:`);
  console.log(`   Total Requests: ${performanceMetrics.totalRequests}`);
  console.log(`   Successful: ${successfulRequests.length}`);
  console.log(`   Failed: ${failedRequests.length}`);
  console.log(`   Success Rate: ${((successfulRequests.length / performanceMetrics.totalRequests) * 100).toFixed(1)}%`);
  
  console.log(`\n⏱️  Response Times:`);
  console.log(`   Average: ${avgDuration.toFixed(0)}ms`);
  console.log(`   Fastest: ${minDuration}ms`);
  console.log(`   Slowest: ${maxDuration}ms`);
  
  // Performance assessment
  console.log(`\n🎯 Performance Assessment:`);
  if (avgDuration < 3000) {
    console.log(`   ✅ EXCELLENT: Average response time under 3 seconds`);
  } else if (avgDuration < 5000) {
    console.log(`   ⚠️  GOOD: Average response time under 5 seconds`);
  } else {
    console.log(`   ❌ NEEDS IMPROVEMENT: Average response time over 5 seconds`);
  }
  
  // Check for performance improvements
  const firstRequest = successfulRequests[0];
  const subsequentRequests = successfulRequests.slice(1);
  
  if (subsequentRequests.length > 0) {
    const subsequentAvg = subsequentRequests.reduce((a, b) => a + b.duration, 0) / subsequentRequests.length;
    const improvement = ((firstRequest.duration - subsequentAvg) / firstRequest.duration) * 100;
    
    console.log(`\n🚀 Cache Performance:`);
    console.log(`   First Request: ${firstRequest.duration}ms`);
    console.log(`   Subsequent Average: ${subsequentAvg.toFixed(0)}ms`);
    
    if (improvement > 0) {
      console.log(`   ✅ IMPROVEMENT: ${improvement.toFixed(1)}% faster after caching`);
    } else {
      console.log(`   ⚠️  No significant improvement detected`);
    }
  }
  
  console.log(`\n💡 Phase 1 Optimization Status:`);
  console.log(`   ✅ Enhanced fetch configuration applied`);
  console.log(`   ✅ Training data caching implemented`);
  console.log(`   ✅ Intelligent routing caching implemented`);
  console.log(`   ✅ Parallel database operations enabled`);
  console.log(`   ✅ Connection keep-alive configured`);
  console.log(`   ✅ Request timeouts implemented`);
}

async function main() {
  console.log('🚀 Phase 1 Performance Optimization Test\n');
  console.log(`Testing against: ${API_BASE_URL}`);
  console.log(`Config ID: ${TEST_CONFIG_ID}\n`);
  
  try {
    await testCaching();
    analyzeResults();
    
    console.log('\n✅ Phase 1 optimization test completed!');
    console.log('\n📝 Next Steps:');
    console.log('   1. Monitor response times in production');
    console.log('   2. Check cache hit rates in logs');
    console.log('   3. Validate intelligent routing still works');
    console.log('   4. Proceed to Phase 2 if performance targets met');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n\n⏹️  Test interrupted by user');
  analyzeResults();
  process.exit(0);
});

main().catch(console.error);
