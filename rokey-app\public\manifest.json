{"name": "RouKey - Smart LLM Router", "short_name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Advanced LLM API key routing and management platform", "start_url": "/dashboard", "display": "standalone", "background_color": "#faf8f5", "theme_color": "#ea580c", "orientation": "portrait-primary", "scope": "/", "lang": "en", "categories": ["productivity", "developer", "utilities"], "icons": [{"src": "/favicon.ico", "sizes": "16x16 32x32", "type": "image/x-icon"}, {"src": "/icon-192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}], "shortcuts": [{"name": "Playground", "short_name": "Playground", "description": "Test your models", "url": "/playground", "icons": [{"src": "/favicon.ico", "sizes": "16x16 32x32"}]}, {"name": "Logs", "short_name": "Logs", "description": "View request history", "url": "/logs", "icons": [{"src": "/favicon.ico", "sizes": "16x16 32x32"}]}, {"name": "My Models", "short_name": "Models", "description": "Manage API keys", "url": "/my-models", "icons": [{"src": "/favicon.ico", "sizes": "16x16 32x32"}]}], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}}