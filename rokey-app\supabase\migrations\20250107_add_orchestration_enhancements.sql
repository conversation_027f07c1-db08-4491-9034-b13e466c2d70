-- Add enhanced orchestration columns for multi-role routing
-- Migration: 20250107_add_orchestration_enhancements.sql

-- Add assignment_type column to track how API keys were assigned
ALTER TABLE public.orchestration_steps 
ADD COLUMN IF NOT EXISTS assignment_type TEXT CHECK (assignment_type IN ('assigned', 'general_chat_fallback', 'first_available_fallback', 'none'));

-- Add confidence column to track role confidence scores
ALTER TABLE public.orchestration_steps 
ADD COLUMN IF NOT EXISTS confidence DECIMAL(3,2) DEFAULT 0.8 CHECK (confidence >= 0.0 AND confidence <= 1.0);

-- Add comments for the new columns
COMMENT ON COLUMN public.orchestration_steps.assignment_type IS 'How the API key was assigned: assigned (dedicated), general_chat_fallback, first_available_fallback, or none';
COMMENT ON COLUMN public.orchestration_steps.confidence IS 'Confidence score for the role classification (0.0-1.0)';

-- Add index for assignment_type for analytics
CREATE INDEX IF NOT EXISTS orchestration_steps_assignment_type_idx ON public.orchestration_steps(assignment_type);

-- Add index for confidence for analytics
CREATE INDEX IF NOT EXISTS orchestration_steps_confidence_idx ON public.orchestration_steps(confidence);
