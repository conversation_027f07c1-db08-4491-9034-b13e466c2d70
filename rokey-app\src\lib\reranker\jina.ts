/**
 * Multi-Key Jina Reranker m0 Implementation
 * Provides automatic key rotation and reranking for optimal RAG results
 */

interface JinaRerankerResponse {
  results: Array<{
    index: number;
    relevance_score: number;
    document: {
      text: string;
    };
  }>;
  usage: {
    total_tokens: number;
    prompt_tokens: number;
  };
}

interface RerankerKeyUsageStats {
  requests: number;
  tokens: number;
  lastUsed: Date;
  errors: number;
  lastError?: Date;
}

interface DocumentToRerank {
  content: string;
  document_id: string;
  similarity: number;
  metadata?: any;
}

interface RerankedResult {
  content: string;
  document_id: string;
  original_similarity: number;
  rerank_score: number;
  final_score: number;
  metadata?: any;
}

export class MultiKeyJinaReranker {
  private apiKeys: string[];
  private currentKeyIndex = 0;
  private keyUsage = new Map<string, RerankerKeyUsageStats>();
  private baseUrl = 'https://api.jina.ai/v1/rerank';
  private model = 'jina-reranker-m0';

  constructor() {
    // Initialize with multiple Jina API keys for high rate limits
    this.apiKeys = [
      process.env.JINA_API_KEY,
      process.env.JINA_API_KEY_2,
      process.env.JINA_API_KEY_3,
      process.env.JINA_API_KEY_4,
      process.env.JINA_API_KEY_5,
      process.env.JINA_API_KEY_6,
      process.env.JINA_API_KEY_7,
      process.env.JINA_API_KEY_9,
      process.env.JINA_API_KEY_10,
    ].filter(Boolean) as string[];

    if (this.apiKeys.length === 0) {
      throw new Error('No Jina API keys found in environment variables');
    }

    console.log(`[Jina Reranker] Initialized with ${this.apiKeys.length} API keys`);

    // Initialize usage stats for each key
    this.apiKeys.forEach(key => {
      this.keyUsage.set(key, {
        requests: 0,
        tokens: 0,
        lastUsed: new Date(0),
        errors: 0
      });
    });
  }

  /**
   * Get the next API key using round-robin rotation
   */
  private getNextKey(): string {
    const key = this.apiKeys[this.currentKeyIndex];
    this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;
    return key;
  }

  /**
   * Get the best available API key based on usage and error rates
   */
  private getBestKey(): string {
    // For now, use simple round-robin
    // TODO: Implement smart selection based on usage stats
    return this.getNextKey();
  }

  /**
   * Update usage statistics for a key
   */
  private updateKeyUsage(apiKey: string, tokens: number, isError = false) {
    const stats = this.keyUsage.get(apiKey);
    if (stats) {
      stats.requests++;
      stats.tokens += tokens;
      stats.lastUsed = new Date();
      
      if (isError) {
        stats.errors++;
        stats.lastError = new Date();
      }
    }
  }

  /**
   * Rerank documents based on query relevance using jina-reranker-m0
   */
  async rerankDocuments(
    query: string, 
    documents: DocumentToRerank[], 
    topN?: number
  ): Promise<RerankedResult[]> {
    if (!documents || documents.length === 0) {
      console.log('[Jina Reranker] No documents to rerank');
      return [];
    }

    const maxRetries = this.apiKeys.length;
    let lastError: Error | null = null;

    // Limit to maximum 2048 documents as per Jina API limits
    const documentsToRerank = documents.slice(0, 2048);
    const actualTopN = topN || Math.min(documentsToRerank.length, 10);

    console.log(`[Jina Reranker] Reranking ${documentsToRerank.length} documents for query: "${query.substring(0, 100)}..."`);

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const apiKey = this.getBestKey();
        
        console.log(`[Jina Reranker] Attempt ${attempt + 1}/${maxRetries} with key ${this.apiKeys.indexOf(apiKey) + 1}`);

        const response = await fetch(this.baseUrl, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: this.model,
            query: query,
            documents: documentsToRerank.map(doc => doc.content),
            top_n: actualTopN,
            return_documents: false // We already have the documents
          }),
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const data: JinaRerankerResponse = await response.json();
        
        if (!data.results || !Array.isArray(data.results)) {
          throw new Error('Invalid response format from Jina Reranker API');
        }

        // Update usage stats
        this.updateKeyUsage(apiKey, data.usage?.total_tokens || query.length);
        
        console.log(`[Jina Reranker] Success with key ${this.apiKeys.indexOf(apiKey) + 1} (${data.results.length} results, ${data.usage?.total_tokens || 'unknown'} tokens)`);
        
        // Map reranked results back to original documents with enhanced scoring
        const rerankedResults: RerankedResult[] = data.results.map(result => {
          const originalDoc = documentsToRerank[result.index];
          
          // Combine original similarity with rerank score for final scoring
          // Weight: 30% original similarity + 70% rerank score
          const finalScore = (originalDoc.similarity * 0.3) + (result.relevance_score * 0.7);
          
          return {
            content: originalDoc.content,
            document_id: originalDoc.document_id,
            original_similarity: originalDoc.similarity,
            rerank_score: result.relevance_score,
            final_score: finalScore,
            metadata: originalDoc.metadata
          };
        });

        // Sort by final score (highest first)
        rerankedResults.sort((a, b) => b.final_score - a.final_score);

        console.log(`[Jina Reranker] Reranking complete. Top result improved from similarity ${rerankedResults[0]?.original_similarity.toFixed(3)} to final score ${rerankedResults[0]?.final_score.toFixed(3)}`);
        
        // Log reranking improvements
        rerankedResults.forEach((result, index) => {
          console.log(`[Jina Reranker] Rank ${index + 1}: Original=${result.original_similarity.toFixed(3)}, Rerank=${result.rerank_score.toFixed(3)}, Final=${result.final_score.toFixed(3)}`);
        });

        return rerankedResults;

      } catch (error: any) {
        lastError = error as Error;
        console.log(`[Jina Reranker] Attempt ${attempt + 1} failed:`, error.message);
        
        // Update error stats
        const apiKey = this.apiKeys[this.currentKeyIndex - 1] || this.apiKeys[this.apiKeys.length - 1];
        this.updateKeyUsage(apiKey, 0, true);
        
        // If this is the last attempt, break and throw
        if (attempt === maxRetries - 1) {
          break;
        }
      }
    }

    // All keys failed - return original documents sorted by similarity
    console.error(`[Jina Reranker] All ${maxRetries} API keys failed. Falling back to original similarity ranking.`);
    console.error(`[Jina Reranker] Last error: ${lastError?.message || 'Unknown error'}`);
    
    // Fallback: return original documents with similarity as final score
    const fallbackResults: RerankedResult[] = documentsToRerank
      .slice(0, actualTopN)
      .map(doc => ({
        content: doc.content,
        document_id: doc.document_id,
        original_similarity: doc.similarity,
        rerank_score: doc.similarity, // Use similarity as rerank score
        final_score: doc.similarity,
        metadata: doc.metadata
      }))
      .sort((a, b) => b.final_score - a.final_score);

    return fallbackResults;
  }

  /**
   * Get usage statistics for all API keys
   */
  getUsageStats(): Record<string, RerankerKeyUsageStats> {
    const stats: Record<string, RerankerKeyUsageStats> = {};
    this.keyUsage.forEach((usage, key) => {
      const keyIndex = this.apiKeys.indexOf(key) + 1;
      stats[`key_${keyIndex}`] = { ...usage };
    });
    return stats;
  }

  /**
   * Reset usage statistics
   */
  resetUsageStats(): void {
    this.apiKeys.forEach(key => {
      this.keyUsage.set(key, {
        requests: 0,
        tokens: 0,
        lastUsed: new Date(0),
        errors: 0
      });
    });
    console.log('[Jina Reranker] Usage statistics reset');
  }
}

// Export singleton instance
export const jinaReranker = new MultiKeyJinaReranker();
