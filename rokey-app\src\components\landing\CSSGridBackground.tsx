'use client';

interface CSSGridBackgroundProps {
  className?: string;
  gridSize?: number;
  opacity?: number;
  color?: string;
  variant?: 'dots' | 'lines' | 'both';
}

export default function CSSGridBackground({ 
  className = '', 
  gridSize = 40,
  opacity = 0.1,
  color = '#000000',
  variant = 'lines'
}: CSSGridBackgroundProps) {
  
  const getBackgroundStyle = () => {
    const colorWithOpacity = `${color}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`;
    
    switch (variant) {
      case 'dots':
        return {
          backgroundImage: `radial-gradient(circle, ${colorWithOpacity} 1px, transparent 1px)`,
          backgroundSize: `${gridSize}px ${gridSize}px`,
        };
      
      case 'lines':
        return {
          backgroundImage: `
            linear-gradient(to right, ${colorWithOpacity} 1px, transparent 1px),
            linear-gradient(to bottom, ${colorWithOpacity} 1px, transparent 1px)
          `,
          backgroundSize: `${gridSize}px ${gridSize}px`,
        };
      
      case 'both':
        return {
          backgroundImage: `
            radial-gradient(circle, ${colorWithOpacity} 1px, transparent 1px),
            linear-gradient(to right, ${colorWithOpacity} 0.5px, transparent 0.5px),
            linear-gradient(to bottom, ${colorWithOpacity} 0.5px, transparent 0.5px)
          `,
          backgroundSize: `${gridSize}px ${gridSize}px, ${gridSize}px ${gridSize}px, ${gridSize}px ${gridSize}px`,
        };
      
      default:
        return {};
    }
  };

  return (
    <div
      className={`absolute inset-0 pointer-events-none ${className}`}
      style={{
        ...getBackgroundStyle(),
        zIndex: 1,
      }}
    />
  );
}
