-- Fix RLS policies for orchestration tables to allow multi-role orchestration

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can manage their own orchestration executions" ON orchestration_executions;
DROP POLICY IF EXISTS "Users can manage their own orchestration steps" ON orchestration_steps;

-- Create more permissive RLS policies for orchestration_executions
CREATE POLICY "Users can manage their own orchestration executions" ON orchestration_executions
FOR ALL USING (
  auth.uid() IS NOT NULL AND (
    user_id = auth.uid() OR 
    user_id = '00000000-0000-0000-0000-000000000000'::uuid
  )
);

-- Create more permissive RLS policies for orchestration_steps
CREATE POLICY "Users can manage their own orchestration steps" ON orchestration_steps
FOR ALL USING (
  auth.uid() IS NOT NULL AND 
  execution_id IN (
    SELECT id FROM orchestration_executions 
    WHERE user_id = auth.uid() OR user_id = '00000000-0000-0000-0000-000000000000'::uuid
  )
);

-- Ensure RLS is enabled
ALTER TABLE orchestration_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE orchestration_steps ENABLE ROW LEVEL SECURITY;
