'use client';

import React, { startTransition } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface InstantLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  prefetch?: boolean;
}

export default function InstantLink({ 
  href, 
  children, 
  className = '', 
  prefetch = true 
}: InstantLinkProps) {
  const router = useRouter();

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    
    // Use startTransition for instant navigation
    startTransition(() => {
      router.push(href);
    });
  };

  return (
    <Link 
      href={href} 
      className={className}
      onClick={handleClick}
      prefetch={prefetch}
    >
      {children}
    </Link>
  );
}
