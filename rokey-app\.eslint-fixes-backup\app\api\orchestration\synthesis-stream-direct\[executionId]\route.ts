// Server-Sent Events endpoint for orchestration synthesis phase
// This endpoint follows the same pattern as the main orchestration stream

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { GoogleGenerativeAI } from '@google/generative-ai';
import crypto from 'crypto';

// Initialize Google Generative AI
const genAI = new GoogleGenerativeAI(process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY || '');

// In-memory storage for active connections
const activeConnections = new Map();

interface RouteParams {
  params: Promise<{
    executionId: string;
  }>;
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  const { executionId } = await params;
  console.log(`[Synthesis Direct] Starting synthesis stream for execution: ${executionId}`);

  if (!executionId) {
    return NextResponse.json(
      { error: 'Execution ID is required' },
      { status: 400 }
    );
  }

  const supabase = await createSupabaseServerClientOnRequest();

  // Create a new ReadableStream for the response
  const stream = new ReadableStream({
    async start(controller) {
      // Add this connection to active connections
      const clientId = Math.random().toString(36).substring(7);
      if (!activeConnections.has(executionId)) {
        activeConnections.set(executionId, new Set());
      }
      activeConnections.get(executionId).add({ id: clientId, controller });

      // Send initial event
      const startEvent = {
        id: crypto.randomUUID(),
        execution_id: executionId,
        type: 'synthesis_started',
        timestamp: new Date().toISOString(),
        data: {
          commentary: 'Starting synthesis of all specialist outputs...',
          timestamp: new Date().toISOString(),
          directStreamUrl: `/api/orchestration/synthesis-stream-direct/${executionId}`
        }
      };
      controller.enqueue(
        new TextEncoder().encode(`event: synthesis_started\ndata: ${JSON.stringify(startEvent)}\n\n`)
      );

      try {
        // Fetch the execution details from the database
        const { data: execution, error: fetchError } = await supabase
          .from('orchestration_executions')
          .select('*')
          .eq('id', executionId)
          .single();

        if (fetchError || !execution) {
          throw new Error(fetchError?.message || 'Execution not found');
        }

        // Get all completed steps for this execution
        const { data: steps, error: stepsError } = await supabase
          .from('orchestration_steps')
          .select('*')
          .eq('execution_id', executionId)
          .eq('status', 'completed')
          .order('created_at', { ascending: true });

        if (stepsError || !steps || steps.length === 0) {
          throw new Error(stepsError?.message || 'No completed steps found for execution');
        }

        console.log(`[Synthesis Direct] Found ${steps.length} completed steps for synthesis`);

        // Construct the synthesis prompt
        const synthesisPrompt = `
You are an expert synthesizer. Your task is to combine the following expert analyses into a single, coherent, and comprehensive response.

${steps.map((step: any, index: number) => {
  return `## ${step.role_name || `Expert ${index + 1}`} (${step.model_name || 'Unknown Model'})\n\n${step.output || 'No output from this expert.'}`;
}).join('\n\n')}

Please synthesize the above analyses into a well-structured response that addresses the original request. Be sure to maintain all key insights and technical details while presenting them in a clear and organized manner.`;

        console.log(`[Synthesis Direct] Redirecting synthesis to chat completions endpoint`);

        // Instead of handling synthesis directly, redirect to the chat completions endpoint
        // This ensures the frontend receives the stream from the expected endpoint
        const origin = request.nextUrl.origin;
        const synthesisUrl = `${origin}/api/v1/chat/completions`;

        console.log(`[Synthesis Direct] Making synthesis request to: ${synthesisUrl}`);

        // Make a request to the chat completions endpoint with synthesis header
        const synthesisResponse = await fetch(synthesisUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.ROKEY_API_ACCESS_TOKEN}`,
            'X-Synthesis-Execution-Id': executionId,
            'User-Agent': 'RoKey/1.0 (Synthesis-Redirect)',
          },
          body: JSON.stringify({
            messages: [{ role: 'user', content: 'synthesis_request' }],
            stream: true,
            custom_api_config_id: 'synthesis-internal' // Special config for synthesis
          })
        });

        if (!synthesisResponse.ok) {
          throw new Error(`Synthesis request failed: ${synthesisResponse.status} ${synthesisResponse.statusText}`);
        }

        console.log(`[Synthesis Direct] Synthesis request successful, streaming response`);

        // Stream the response from chat completions to the client
        if (synthesisResponse.body) {
          const reader = synthesisResponse.body.getReader();
          const decoder = new TextDecoder();
          const encoder = new TextEncoder();
          let chunkCount = 0;

          try {
            while (true) {
              const { done, value } = await reader.read();
              if (done) break;

              chunkCount++;
              const chunk = decoder.decode(value, { stream: true });

              // Parse the chunk to extract streaming data and format as SSE
              const lines = chunk.split('\n');
              for (const line of lines) {
                if (line.startsWith('data: ') && !line.includes('[DONE]')) {
                  try {
                    const jsonData = line.substring(6);
                    const parsed = JSON.parse(jsonData);

                    // Forward the parsed data as a proper SSE event
                    const sseData = `data: ${JSON.stringify(parsed)}\n\n`;
                    controller.enqueue(encoder.encode(sseData));

                    console.log(`[Synthesis Direct] Forwarded SSE chunk: ${jsonData.length} chars`);
                  } catch (parseError) {
                    // If we can't parse as JSON, forward the original line as SSE
                    const sseData = `${line}\n\n`;
                    controller.enqueue(encoder.encode(sseData));
                  }
                } else if (line.includes('[DONE]')) {
                  // Forward the DONE message as SSE
                  const sseData = `data: [DONE]\n\n`;
                  controller.enqueue(encoder.encode(sseData));
                  console.log(`[Synthesis Direct] Forwarded [DONE] message`);
                }
              }
            }

            console.log(`[Synthesis Direct] Synthesis streaming completed, forwarded ${chunkCount} chunks`);
          } finally {
            reader.releaseLock();
          }
        }

      } catch (error) {
        console.error('[Synthesis Direct] Error during synthesis:', error);
        
        // Send error event
        const errorEvent = {
          id: crypto.randomUUID(),
          execution_id: executionId,
          type: 'synthesis_error',
          timestamp: new Date().toISOString(),
          data: {
            error: error instanceof Error ? error.message : 'Unknown error during synthesis',
            timestamp: new Date().toISOString()
          }
        };
        controller.enqueue(
          new TextEncoder().encode(`data: ${JSON.stringify(errorEvent)}\n\n`)
        );
      } finally {
        // Clean up the connection
        const clients = activeConnections.get(executionId);
        if (clients) {
          clients.delete({ id: clientId });
          if (clients.size === 0) {
            activeConnections.delete(executionId);
          }
        }
        controller.close();
      }
    },
    cancel() {
      // Clean up if the client disconnects
      console.log(`[Synthesis Direct] Client disconnected from execution: ${executionId}`);
    }
  });

  // Return the stream with appropriate headers for SSE
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'X-Accel-Buffering': 'no' // Disable buffering in Nginx
    }
  });
}