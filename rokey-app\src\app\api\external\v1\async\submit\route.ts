import { type NextRequest, NextResponse } from 'next/server';
import { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';
import { AsyncJobManager } from '@/lib/async/jobManager';
import { z } from 'zod';

const AsyncRequestSchema = z.object({
  model: z.string().optional().default('gpt-3.5-turbo'),
  messages: z.array(
    z.object({
      role: z.enum(['user', 'assistant', 'system']),
      content: z.union([z.string(), z.array(z.any())]),
    })
  ).min(1),
  temperature: z.number().min(0).max(2).optional(),
  max_tokens: z.number().int().positive().optional(),
  top_p: z.number().min(0).max(1).optional(),
  frequency_penalty: z.number().min(-2).max(2).optional(),
  presence_penalty: z.number().min(-2).max(2).optional(),
  stop: z.union([z.string(), z.array(z.string())]).optional(),
  role: z.string().optional(),
  webhook_url: z.string().url().optional(),
}).catchall(z.any());

const authMiddleware = new ApiKeyAuthMiddleware();
const jobManager = new AsyncJobManager();

export async function POST(request: NextRequest) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig, ipAddress } = authResult;

    // 2. Check permissions
    if (!authMiddleware.hasPermission(userApiKey!, 'chat')) {
      return NextResponse.json(
        {
          error: {
            message: 'API key does not have chat permission',
            type: 'permission_error',
            code: 'insufficient_permissions'
          }
        },
        { status: 403 }
      );
    }

    // 3. Parse and validate request body
    const rawBody = await request.json();
    const validationResult = AsyncRequestSchema.safeParse(rawBody);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: {
            message: 'Invalid request body',
            type: 'invalid_request_error',
            code: 'invalid_request',
            details: validationResult.error.flatten().fieldErrors
          }
        },
        { status: 400 }
      );
    }

    const requestData = validationResult.data;

    // 4. Prepare request for async processing
    const asyncRequest = {
      custom_api_config_id: userConfig!.id,
      messages: requestData.messages,
      temperature: requestData.temperature,
      max_tokens: requestData.max_tokens,
      role: requestData.role,
      model: requestData.model,
      top_p: requestData.top_p,
      frequency_penalty: requestData.frequency_penalty,
      presence_penalty: requestData.presence_penalty,
      stop: requestData.stop,
      _internal_user_id: userConfig!.user_id,
    };

    // 5. Create async job
    const job = await jobManager.createJob(
      userConfig!.user_id,
      userApiKey!.id,
      asyncRequest,
      requestData.webhook_url
    );

    // 6. Log usage asynchronously
    authMiddleware.logApiUsage(
      userApiKey!,
      request,
      {
        statusCode: 202,
        modelUsed: requestData.model,
        providerUsed: 'async_processing',
      },
      ipAddress
    ).catch((error: any) => {
      console.error('Failed to log API usage:', error);
    });

    // 7. Trigger async processing (fire and forget)
    fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/internal/async/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.ROKEY_API_ACCESS_TOKEN}`,
      },
      body: JSON.stringify({ job_id: job.id }),
    }).catch(error => {
      console.error('Failed to trigger async processing:', error);
    });

    // 8. Return job information
    return NextResponse.json({
      job_id: job.id,
      status: job.status,
      estimated_completion: job.estimated_completion,
      created_at: job.created_at,
      progress_percentage: job.progress_percentage,
      polling_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/external/v1/async/status/${job.id}`,
      result_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/external/v1/async/result/${job.id}`,
      webhook_configured: !!requestData.webhook_url,
      message: 'Job submitted for async processing. Use polling_url to check status or configure webhook_url for notifications.'
    }, { 
      status: 202,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      }
    });

  } catch (error) {
    console.error('Error in async submit API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// Handle CORS preflight requests
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      'Access-Control-Max-Age': '86400',
    },
  });
}
