export default function LogsLoading() {
  return (
    <div className="min-h-screen w-full bg-[#040716] text-white overflow-x-hidden">
      {/* Header Section */}
      <div className="border-b border-gray-800/50">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              <div className="animate-pulse bg-gray-800 h-8 w-32 rounded"></div>
              <div className="flex items-center space-x-1">
                <div className="animate-pulse bg-gray-800 h-6 w-16 rounded"></div>
                <div className="animate-pulse bg-gray-800 h-6 w-16 rounded"></div>
              </div>
            </div>
            <div className="animate-pulse bg-gray-800 h-10 w-32 rounded"></div>
          </div>

          {/* Welcome Message */}
          <div className="mt-6">
            <div className="animate-pulse bg-gray-800 h-6 w-80 rounded mb-2"></div>
            <div className="animate-pulse bg-gray-800 h-4 w-96 rounded"></div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-6 border border-gray-800/50 mb-8">
          <div className="mb-6">
            <div className="animate-pulse bg-gray-800 h-6 w-32 rounded mb-2"></div>
            <div className="animate-pulse bg-gray-800 h-4 w-48 rounded"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="animate-pulse bg-gray-800 h-4 w-20 rounded"></div>
                <div className="animate-pulse bg-gray-800 h-10 w-full rounded"></div>
              </div>
            ))}
          </div>
          <div className="flex justify-end space-x-3 mt-6">
            <div className="animate-pulse bg-gray-800 h-10 w-24 rounded"></div>
            <div className="animate-pulse bg-gray-800 h-10 w-20 rounded"></div>
          </div>
        </div>

        {/* Table */}
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg border border-gray-800/50 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm">
              <thead className="bg-gray-800/50 border-b border-gray-700">
                <tr>
                  {Array.from({ length: 11 }).map((_, i) => (
                    <th key={i} className="px-6 py-4">
                      <div className="animate-pulse bg-gray-800 h-4 w-20 rounded"></div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700 bg-gray-900/30">
                {Array.from({ length: 8 }).map((_, i) => (
                  <tr key={i} className="hover:bg-gray-800/30">
                    {Array.from({ length: 11 }).map((_, j) => (
                      <td key={j} className="px-6 py-4">
                        <div className="animate-pulse bg-gray-800 h-4 w-16 rounded"></div>
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-lg p-4 border border-gray-800/50 mt-6">
          <div className="flex items-center justify-between">
            <div className="animate-pulse bg-gray-800 h-4 w-32 rounded"></div>
            <div className="flex space-x-2">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="animate-pulse bg-gray-800 h-8 w-16 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
