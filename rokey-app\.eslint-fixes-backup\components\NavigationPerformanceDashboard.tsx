'use client';

import React, { useState, useEffect } from 'react';
import { useNavigation } from '@/contexts/NavigationContext';
import { useNavigationCache, useNavigationPerformance } from '@/hooks/useNavigationCache';
import { usePredictiveNavigation } from '@/hooks/usePredictiveNavigation';
import { useAdvancedPreloading } from '@/hooks/useAdvancedPreloading';
import { useBreadcrumb } from '@/hooks/useBreadcrumb';

export default function NavigationPerformanceDashboard() {
  const [isClient, setIsClient] = useState(false);
  const [isDevelopment, setIsDevelopment] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  const { 
    isNavigating, 
    targetRoute, 
    navigationHistory,
    isPageCached 
  } = useNavigation();
  
  const {
    metrics: cacheMetrics,
    getPerformanceInsights,
    cacheSize
  } = useNavigationCache();
  
  const {
    getAverageTime,
    getPerformanceGrade,
    navigationTimes
  } = useNavigationPerformance();

  const {
    predictions,
    insights: learningInsights,
    isLearning,
    userBehavior
  } = usePredictiveNavigation();

  const {
    getStatus: getPreloadStatus,
    isPreloading
  } = useAdvancedPreloading();

  const { currentPage, pageTitle } = useBreadcrumb();

  // Handle hydration safely
  useEffect(() => {
    setIsClient(true);
    setIsDevelopment(process.env.NODE_ENV === 'development');
  }, []);

  // Don't render anything until client-side hydration is complete
  if (!isClient || !isDevelopment) {
    return null;
  }

  const preloadStatus = getPreloadStatus();
  const performanceGrade = getPerformanceGrade();
  const avgTime = getAverageTime();

  return (
    <div className="fixed bottom-4 left-4 bg-white border border-gray-300 rounded-lg shadow-lg z-50 max-w-md">
      {/* Header */}
      <div 
        className="p-3 bg-gray-50 rounded-t-lg cursor-pointer flex items-center justify-between"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <h3 className="font-semibold text-gray-900 text-sm">Navigation Performance</h3>
        <div className="flex items-center space-x-2">
          <span className={`text-xs px-2 py-1 rounded-full ${
            performanceGrade === 'A' ? 'bg-green-100 text-green-800' :
            performanceGrade === 'B' ? 'bg-blue-100 text-blue-800' :
            performanceGrade === 'C' ? 'bg-yellow-100 text-yellow-800' :
            'bg-red-100 text-red-800'
          }`}>
            Grade: {performanceGrade}
          </span>
          <span className="text-gray-400 text-xs">
            {isExpanded ? '▼' : '▶'}
          </span>
        </div>
      </div>

      {isExpanded && (
        <div className="p-4 space-y-4 max-h-96 overflow-y-auto">
          {/* Current Page Info */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-800 text-sm">Current Page</h4>
            <div className="bg-blue-50 p-3 rounded">
              <div className="text-sm font-medium text-blue-900">{currentPage.title}</div>
              <div className="text-xs text-blue-700">{currentPage.subtitle}</div>
              <div className="text-xs text-blue-600 mt-1 font-mono">{currentPage.path}</div>
            </div>
          </div>

          {/* Current Status */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-800 text-sm">System Status</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="bg-gray-50 p-2 rounded">
                <div className="text-gray-600">Navigation</div>
                <div className={isNavigating ? 'text-orange-600' : 'text-green-600'}>
                  {isNavigating ? '🔄 Active' : '✅ Ready'}
                </div>
              </div>
              <div className="bg-gray-50 p-2 rounded">
                <div className="text-gray-600">Preloading</div>
                <div className={isPreloading ? 'text-blue-600' : 'text-gray-600'}>
                  {isPreloading ? '⚡ Active' : '💤 Idle'}
                </div>
              </div>
              <div className="bg-gray-50 p-2 rounded">
                <div className="text-gray-600">Learning</div>
                <div className={isLearning ? 'text-purple-600' : 'text-gray-600'}>
                  {isLearning ? '🧠 Active' : '📚 Training'}
                </div>
              </div>
              <div className="bg-gray-50 p-2 rounded">
                <div className="text-gray-600">Cache Size</div>
                <div className="text-gray-800">{cacheSize} pages</div>
              </div>
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-800 text-sm">Performance Metrics</h4>
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-600">Average Time:</span>
                <span className="font-mono">{avgTime.toFixed(1)}ms</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Cache Hit Rate:</span>
                <span className="font-mono">{cacheMetrics.cacheHitRate.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Total Navigations:</span>
                <span className="font-mono">{navigationHistory.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Fast Navigations:</span>
                <span className="font-mono">{cacheMetrics.fastNavigations}</span>
              </div>
            </div>
          </div>

          {/* Predictions */}
          {predictions.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium text-gray-800 text-sm">Predictions</h4>
              <div className="space-y-1">
                {predictions.slice(0, 3).map((route, index) => (
                  <div key={route} className="flex items-center justify-between text-xs">
                    <span className="text-blue-600">{route}</span>
                    <span className="text-gray-500">#{index + 1}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Preload Status */}
          {(preloadStatus.activePreloads.length > 0 || preloadStatus.queuedPreloads.length > 0) && (
            <div className="space-y-2">
              <h4 className="font-medium text-gray-800 text-sm">Preloading</h4>
              <div className="space-y-1 text-xs">
                {preloadStatus.activePreloads.length > 0 && (
                  <div>
                    <span className="text-blue-600">Active:</span>
                    <div className="ml-2">
                      {preloadStatus.activePreloads.map(route => (
                        <div key={route} className="text-blue-600">⚡ {route}</div>
                      ))}
                    </div>
                  </div>
                )}
                {preloadStatus.queuedPreloads.length > 0 && (
                  <div>
                    <span className="text-gray-600">Queued:</span>
                    <div className="ml-2">
                      {preloadStatus.queuedPreloads.slice(0, 3).map(route => (
                        <div key={route} className="text-gray-600">⏳ {route}</div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* User Behavior Insights */}
          {learningInsights.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium text-gray-800 text-sm">Learning Insights</h4>
              <div className="space-y-1 text-xs text-purple-600">
                {learningInsights.map((insight, index) => (
                  <div key={index}>🧠 {insight}</div>
                ))}
              </div>
            </div>
          )}

          {/* Performance Insights */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-800 text-sm">Optimization Tips</h4>
            <div className="space-y-1 text-xs text-green-600">
              {getPerformanceInsights().map((insight, index) => (
                <div key={index}>💡 {insight}</div>
              ))}
            </div>
          </div>

          {/* Navigation History */}
          {navigationHistory.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium text-gray-800 text-sm">Recent History</h4>
              <div className="space-y-1 text-xs">
                {navigationHistory.slice(-5).reverse().map((route, index) => (
                  <div key={`${route}-${index}`} className="flex items-center justify-between">
                    <span className="text-gray-600">{route}</span>
                    <span className={`text-xs ${isPageCached(route) ? 'text-green-600' : 'text-gray-400'}`}>
                      {isPageCached(route) ? '💾' : '🔄'}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Strategy Overview */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-800 text-sm">Current Strategy</h4>
            <div className="text-xs space-y-1">
              <div className="flex justify-between">
                <span className="text-gray-600">Immediate:</span>
                <span className="text-red-600">{preloadStatus.strategy.immediate.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">On Idle:</span>
                <span className="text-blue-600">{preloadStatus.strategy.onIdle.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Background:</span>
                <span className="text-gray-600">{preloadStatus.strategy.background.length}</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
