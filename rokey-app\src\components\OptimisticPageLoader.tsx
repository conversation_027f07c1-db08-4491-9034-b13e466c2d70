'use client';

import { useEffect, useState, useRef } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useNavigationSafe } from '@/contexts/NavigationContext';

// Page skeleton components for instant loading
const DashboardSkeleton = () => (
  <div className="space-y-6 animate-pulse">
    <div className="h-8 bg-gray-200 rounded w-1/3"></div>
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {[1, 2, 3].map((i) => (
        <div key={i} className="bg-white p-6 rounded-lg border">
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
        </div>
      ))}
    </div>
    <div className="bg-white p-6 rounded-lg border">
      <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
      <div className="space-y-3">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="h-4 bg-gray-200 rounded"></div>
        ))}
      </div>
    </div>
  </div>
);

const PricingSkeleton = () => (
  <div className="space-y-8 animate-pulse">
    <div className="text-center">
      <div className="h-12 bg-gray-200 rounded w-1/2 mx-auto mb-4"></div>
      <div className="h-6 bg-gray-200 rounded w-3/4 mx-auto"></div>
    </div>
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      {[1, 2, 3].map((i) => (
        <div key={i} className="bg-white p-8 rounded-2xl border-2">
          <div className="text-center mb-8">
            <div className="h-6 bg-gray-200 rounded w-1/2 mx-auto mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto mb-4"></div>
            <div className="h-10 bg-gray-200 rounded w-1/3 mx-auto"></div>
          </div>
          <div className="space-y-3 mb-8">
            {[1, 2, 3, 4, 5].map((j) => (
              <div key={j} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-12 bg-gray-200 rounded"></div>
        </div>
      ))}
    </div>
  </div>
);

const FeaturesSkeleton = () => (
  <div className="space-y-8 animate-pulse">
    <div className="text-center py-20 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="h-16 bg-gray-200 rounded w-2/3 mx-auto mb-6"></div>
      <div className="h-6 bg-gray-200 rounded w-3/4 mx-auto"></div>
    </div>
    <div className="py-20">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <div key={i} className="bg-white rounded-2xl p-8 border">
            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-gray-200 rounded-xl"></div>
              <div className="flex-1">
                <div className="h-6 bg-gray-200 rounded w-1/2 mb-3"></div>
                <div className="h-4 bg-gray-200 rounded mb-4"></div>
                <div className="space-y-2">
                  {[1, 2, 3].map((j) => (
                    <div key={j} className="h-3 bg-gray-200 rounded w-3/4"></div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

const AuthSkeleton = () => (
  <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50">
    <div className="bg-white p-8 rounded-2xl shadow-lg border w-full max-w-md animate-pulse">
      <div className="text-center mb-8">
        <div className="h-8 bg-gray-200 rounded w-1/2 mx-auto mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
      </div>
      <div className="space-y-4">
        <div className="h-12 bg-gray-200 rounded"></div>
        <div className="h-12 bg-gray-200 rounded"></div>
        <div className="h-12 bg-gray-200 rounded"></div>
        <div className="h-12 bg-gray-200 rounded"></div>
      </div>
    </div>
  </div>
);

const PlaygroundSkeleton = () => (
  <div className="space-y-6 animate-pulse">
    <div className="h-8 bg-gray-200 rounded w-1/4"></div>
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div className="bg-white p-6 rounded-lg border">
        <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-4 bg-gray-200 rounded"></div>
          ))}
        </div>
        <div className="h-32 bg-gray-200 rounded mt-4"></div>
        <div className="h-10 bg-gray-200 rounded mt-4"></div>
      </div>
      <div className="bg-white p-6 rounded-lg border">
        <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
        <div className="h-64 bg-gray-200 rounded"></div>
      </div>
    </div>
  </div>
);

const GenericSkeleton = () => (
  <div className="space-y-6 animate-pulse">
    <div className="h-8 bg-gray-200 rounded w-1/3"></div>
    <div className="h-4 bg-gray-200 rounded w-2/3"></div>
    <div className="bg-white p-6 rounded-lg border">
      <div className="space-y-4">
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className="h-4 bg-gray-200 rounded"></div>
        ))}
      </div>
    </div>
  </div>
);

interface OptimisticPageLoaderProps {
  targetRoute: string;
  children: React.ReactNode;
}

export default function OptimisticPageLoader({ targetRoute, children }: OptimisticPageLoaderProps) {
  const [showSkeleton, setShowSkeleton] = useState(true);
  const [isContentReady, setIsContentReady] = useState(false);
  const pathname = usePathname();
  const timeoutRef = useRef<NodeJS.Timeout>();

  // Use safe navigation hook that returns null instead of throwing
  const navigationContext = useNavigationSafe();

  const { isPageCached } = navigationContext || {
    isPageCached: () => false
  };

  // Determine which skeleton to show based on target route
  const getSkeletonComponent = (route: string) => {
    if (route.startsWith('/dashboard')) return <DashboardSkeleton />;
    if (route.startsWith('/pricing')) return <PricingSkeleton />;
    if (route.startsWith('/features')) return <FeaturesSkeleton />;
    if (route.startsWith('/auth/')) return <AuthSkeleton />;
    if (route.startsWith('/playground')) return <PlaygroundSkeleton />;
    return <GenericSkeleton />;
  };

  useEffect(() => {
    // If we've reached the target route, start transitioning to real content
    if (pathname === targetRoute) {
      const isCached = isPageCached(targetRoute);
      const delay = isCached ? 50 : 200; // Faster for cached pages
      
      timeoutRef.current = setTimeout(() => {
        setIsContentReady(true);
        // Hide skeleton after a brief moment to show real content
        setTimeout(() => setShowSkeleton(false), 100);
      }, delay);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [pathname, targetRoute, isPageCached]);

  // Reset state when target route changes
  useEffect(() => {
    setShowSkeleton(true);
    setIsContentReady(false);
  }, [targetRoute]);

  // If we're not at the target route yet, show skeleton
  if (pathname !== targetRoute && showSkeleton) {
    return (
      <div className="optimistic-loading-container">
        {getSkeletonComponent(targetRoute)}
      </div>
    );
  }

  // If we're at the target route but content isn't ready, show skeleton with fade
  if (pathname === targetRoute && showSkeleton && !isContentReady) {
    return (
      <div className="optimistic-loading-container">
        {getSkeletonComponent(targetRoute)}
      </div>
    );
  }

  // Show real content with fade-in
  return (
    <div className={`transition-opacity duration-300 ${isContentReady ? 'opacity-100' : 'opacity-0'}`}>
      {children}
    </div>
  );
}

// Note: useOptimisticNavigation hook is exported from OptimisticLink.tsx to avoid duplication
