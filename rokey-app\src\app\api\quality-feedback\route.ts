import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { z } from 'zod';

// Schema for quality feedback
const QualityFeedbackSchema = z.object({
  custom_api_config_id: z.string().uuid(),
  api_key_id: z.string().uuid(),
  model_used: z.string(),
  provider: z.string(),
  prompt_text: z.string().max(2000),
  response_text: z.string().max(10000),
  user_rating: z.number().int().min(1).max(5),
  response_time_ms: z.number().int().positive().optional(),
  tokens_prompt: z.number().int().positive().optional(),
  tokens_completion: z.number().int().positive().optional(),
  cost_usd: z.number().positive().optional(),
  user_copied_response: z.boolean().optional().default(false),
  user_regenerated: z.boolean().optional().default(false),
  user_asked_followup: z.boolean().optional().default(false),
  conversation_continued: z.boolean().optional().default(false),
  routing_strategy: z.string().optional().default('unknown'),
  was_ab_test: z.boolean().optional().default(false),
  ab_test_group: z.enum(['control', 'test']).optional(),
});

// POST endpoint to submit quality feedback
export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClientFromRequest(request);
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = QualityFeedbackSchema.parse(body);

    // Calculate quality score based on user rating and behavioral signals
    let qualityScore = validatedData.user_rating * 2; // Base score (1-5 rating -> 2-10 score)

    // Adjust based on behavioral signals
    if (validatedData.user_copied_response) qualityScore += 0.5;
    if (validatedData.conversation_continued) qualityScore += 0.5;
    if (validatedData.user_regenerated) qualityScore -= 1.0;
    if (validatedData.user_asked_followup) qualityScore -= 0.5;

    // Ensure score stays within bounds
    qualityScore = Math.max(1.0, Math.min(10.0, qualityScore));

    // Calculate cost per quality point if cost is provided
    let costPerQualityPoint = null;
    if (validatedData.cost_usd && validatedData.cost_usd > 0) {
      costPerQualityPoint = validatedData.cost_usd / qualityScore;
    }

    // Classify task category based on prompt content
    const taskCategory = classifyTaskCategory(validatedData.prompt_text);

    // Classify prompt complexity (simple heuristic)
    const promptComplexityLevel = classifyPromptComplexityHeuristic(validatedData.prompt_text);

    // Insert quality metrics
    const { data: qualityMetric, error: insertError } = await supabase
      .from('routing_quality_metrics')
      .insert([{
        user_id: user.id,
        custom_api_config_id: validatedData.custom_api_config_id,
        api_key_id: validatedData.api_key_id,
        model_used: validatedData.model_used,
        provider: validatedData.provider,
        prompt_text: validatedData.prompt_text,
        prompt_complexity_level: promptComplexityLevel,
        task_category: taskCategory,
        quality_score: qualityScore,
        user_rating: validatedData.user_rating,
        response_length: validatedData.response_text.length,
        response_time_ms: validatedData.response_time_ms,
        tokens_prompt: validatedData.tokens_prompt,
        tokens_completion: validatedData.tokens_completion,
        cost_usd: validatedData.cost_usd,
        cost_per_quality_point: costPerQualityPoint,
        user_copied_response: validatedData.user_copied_response,
        user_regenerated: validatedData.user_regenerated,
        user_asked_followup: validatedData.user_asked_followup,
        conversation_continued: validatedData.conversation_continued,
        routing_strategy: validatedData.routing_strategy,
        was_ab_test: validatedData.was_ab_test,
        ab_test_group: validatedData.ab_test_group,
      }])
      .select()
      .single();

    if (insertError) {
      console.error('[Quality Feedback] Error inserting metrics:', insertError);
      return NextResponse.json({ error: 'Failed to save feedback' }, { status: 500 });
    }

    // Update cost optimization profile
    await updateCostOptimizationProfile(user.id, validatedData.custom_api_config_id, supabase);

    // Update A/B test metrics if applicable
    if (validatedData.was_ab_test && validatedData.ab_test_group) {
      await updateABTestMetrics(
        user.id, 
        validatedData.custom_api_config_id, 
        validatedData.ab_test_group, 
        qualityScore, 
        validatedData.cost_usd,
        supabase
      );
    }

    console.log(`[Quality Feedback] Recorded feedback for ${validatedData.model_used}: rating=${validatedData.user_rating}, quality_score=${qualityScore}`);

    return NextResponse.json({
      success: true,
      message: 'Quality feedback recorded successfully',
      qualityScore,
      taskCategory,
      complexityLevel: promptComplexityLevel
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Invalid request data', 
        details: error.errors 
      }, { status: 400 });
    }

    console.error('[Quality Feedback] Unexpected error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Helper function to classify task category
function classifyTaskCategory(promptText: string): string {
  const prompt = promptText.toLowerCase();
  
  if (prompt.includes('code') || prompt.includes('function') || prompt.includes('programming') || 
      prompt.includes('debug') || prompt.includes('algorithm')) {
    return 'coding';
  }
  
  if (prompt.includes('write') || prompt.includes('essay') || prompt.includes('article') || 
      prompt.includes('story') || prompt.includes('blog')) {
    return 'writing';
  }
  
  if (prompt.includes('analyze') || prompt.includes('analysis') || prompt.includes('compare') || 
      prompt.includes('evaluate') || prompt.includes('research')) {
    return 'analysis';
  }
  
  if (prompt.includes('explain') || prompt.includes('how') || prompt.includes('what') || 
      prompt.includes('why') || prompt.includes('define')) {
    return 'explanation';
  }
  
  if (prompt.includes('solve') || prompt.includes('calculate') || prompt.includes('math') || 
      prompt.includes('equation') || prompt.includes('problem')) {
    return 'reasoning';
  }
  
  return 'chat';
}

// Helper function to classify prompt complexity using heuristics
function classifyPromptComplexityHeuristic(promptText: string): number {
  let complexity = 1;
  
  // Length factor
  if (promptText.length > 500) complexity += 1;
  if (promptText.length > 1000) complexity += 1;
  
  // Complexity indicators
  const complexityKeywords = [
    'analyze', 'compare', 'evaluate', 'synthesize', 'create', 'design',
    'algorithm', 'optimization', 'architecture', 'framework', 'system',
    'research', 'comprehensive', 'detailed', 'thorough', 'complex'
  ];
  
  const keywordCount = complexityKeywords.filter(keyword => 
    promptText.toLowerCase().includes(keyword)
  ).length;
  
  complexity += Math.min(keywordCount, 2);
  
  // Multiple questions or requirements
  const questionMarks = (promptText.match(/\?/g) || []).length;
  if (questionMarks > 1) complexity += 1;
  
  // Lists or numbered items
  if (promptText.includes('1.') || promptText.includes('•') || promptText.includes('-')) {
    complexity += 1;
  }
  
  return Math.min(complexity, 5);
}

// Helper function to update cost optimization profile
async function updateCostOptimizationProfile(
  userId: string, 
  configId: string, 
  supabase: any
): Promise<void> {
  try {
    // Get current profile
    const { data: profile } = await supabase
      .from('cost_optimization_profiles')
      .select('*')
      .eq('user_id', userId)
      .eq('custom_api_config_id', configId)
      .single();

    const learningPhaseRequests = (profile?.learning_phase_requests || 0) + 1;
    const learningPhaseCompleted = learningPhaseRequests >= 50;

    if (profile) {
      // Update existing profile
      await supabase
        .from('cost_optimization_profiles')
        .update({
          learning_phase_requests: learningPhaseRequests,
          learning_phase_completed: learningPhaseCompleted,
          total_requests: (profile.total_requests || 0) + 1
        })
        .eq('id', profile.id);
    } else {
      // Create new profile
      await supabase
        .from('cost_optimization_profiles')
        .insert([{
          user_id: userId,
          custom_api_config_id: configId,
          learning_phase_requests: learningPhaseRequests,
          learning_phase_completed: learningPhaseCompleted,
          total_requests: 1
        }]);
    }
  } catch (error) {
    console.error('[Cost Profile] Error updating profile:', error);
  }
}

// Helper function to update A/B test metrics
async function updateABTestMetrics(
  userId: string,
  configId: string,
  testGroup: 'control' | 'test',
  qualityScore: number,
  cost: number | undefined,
  supabase: any
): Promise<void> {
  try {
    const { data: abTest } = await supabase
      .from('ab_test_assignments')
      .select('*')
      .eq('user_id', userId)
      .eq('custom_api_config_id', configId)
      .single();

    if (abTest) {
      const updateData: any = {};
      
      if (testGroup === 'test') {
        updateData.test_avg_quality = calculateNewAverage(
          abTest.test_avg_quality, 
          abTest.test_requests, 
          qualityScore
        );
        if (cost) {
          updateData.test_avg_cost = calculateNewAverage(
            abTest.test_avg_cost, 
            abTest.test_requests, 
            cost
          );
        }
      } else {
        updateData.control_avg_quality = calculateNewAverage(
          abTest.control_avg_quality, 
          abTest.control_requests, 
          qualityScore
        );
        if (cost) {
          updateData.control_avg_cost = calculateNewAverage(
            abTest.control_avg_cost, 
            abTest.control_requests, 
            cost
          );
        }
      }

      await supabase
        .from('ab_test_assignments')
        .update(updateData)
        .eq('id', abTest.id);
    }
  } catch (error) {
    console.error('[A/B Metrics] Error updating metrics:', error);
  }
}

// Helper function to calculate new average
function calculateNewAverage(
  currentAvg: number | null, 
  currentCount: number, 
  newValue: number
): number {
  if (!currentAvg || currentCount === 0) return newValue;
  return ((currentAvg * currentCount) + newValue) / (currentCount + 1);
}
