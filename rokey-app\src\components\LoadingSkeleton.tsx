'use client';

import React from 'react';

interface LoadingSkeletonProps {
  className?: string;
  variant?: 'text' | 'circular' | 'rectangular';
  width?: string | number;
  height?: string | number;
  lines?: number;
}

export const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({
  className = '',
  variant = 'text',
  width = '100%',
  height = '1rem',
  lines = 1
}) => {
  const baseClasses = 'animate-pulse bg-gray-200 rounded';
  
  const getVariantClasses = () => {
    switch (variant) {
      case 'circular':
        return 'rounded-full';
      case 'rectangular':
        return 'rounded-lg';
      case 'text':
      default:
        return 'rounded';
    }
  };

  const style = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
  };

  if (lines > 1) {
    return (
      <div className={`space-y-2 ${className}`}>
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={`${baseClasses} ${getVariantClasses()}`}
            style={{
              ...style,
              width: index === lines - 1 ? '75%' : style.width, // Last line shorter
            }}
          />
        ))}
      </div>
    );
  }

  return (
    <div
      className={`${baseClasses} ${getVariantClasses()} ${className}`}
      style={style}
    />
  );
};

// Specific skeleton components for common use cases
export const ChatHistorySkeleton: React.FC = () => (
  <div className="space-y-2 p-4">
    {Array.from({ length: 8 }).map((_, index) => (
      <div key={index} className="p-3 rounded-xl border border-gray-100 animate-pulse">
        <div className="flex items-center justify-between mb-2">
          <LoadingSkeleton height="1rem" width="60%" />
          <LoadingSkeleton height="0.75rem" width="3rem" />
        </div>
        <LoadingSkeleton height="0.75rem" width="80%" className="mb-1" />
        <div className="flex justify-between items-center">
          <LoadingSkeleton width="4rem" height="0.75rem" />
          <LoadingSkeleton variant="circular" width={16} height={16} />
        </div>
      </div>
    ))}
  </div>
);

// Enhanced chat history skeleton with staggered animation
export const EnhancedChatHistorySkeleton: React.FC = () => (
  <div className="space-y-2 p-4">
    {Array.from({ length: 8 }).map((_, index) => (
      <div
        key={index}
        className="p-3 rounded-xl border border-gray-100"
        style={{
          animation: `fadeInUp 0.6s ease-out ${index * 0.1}s both`
        }}
      >
        <div className="flex items-center justify-between mb-2">
          <div className="animate-pulse bg-gray-200 h-4 rounded" style={{ width: `${60 + Math.random() * 20}%` }} />
          <div className="animate-pulse bg-gray-200 h-3 w-12 rounded" />
        </div>
        <div className="animate-pulse bg-gray-200 h-3 rounded mb-1" style={{ width: `${70 + Math.random() * 20}%` }} />
        <div className="flex justify-between items-center">
          <div className="animate-pulse bg-gray-200 h-3 w-16 rounded" />
          <div className="animate-pulse bg-gray-200 h-4 w-4 rounded-full" />
        </div>
      </div>
    ))}
    <style jsx>{`
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    `}</style>
  </div>
);

export const MessageSkeleton: React.FC = () => (
  <div className="space-y-6 py-8">
    {Array.from({ length: 3 }).map((_, index) => (
      <div key={index} className={`flex ${index % 2 === 0 ? 'justify-end' : 'justify-start'}`}>
        <div className={`max-w-3xl p-4 rounded-2xl ${
          index % 2 === 0 ? 'bg-orange-50' : 'bg-white border border-gray-200'
        }`}>
          <LoadingSkeleton lines={3} height="1rem" />
        </div>
      </div>
    ))}
  </div>
);

export const ConfigSelectorSkeleton: React.FC = () => (
  <div className="flex items-center space-x-3">
    <LoadingSkeleton variant="circular" width={32} height={32} />
    <LoadingSkeleton width="8rem" height="1.5rem" />
  </div>
);

// Enhanced skeleton components for better performance perception
export const DashboardSkeleton: React.FC = () => (
  <div className="space-y-8 animate-fade-in">
    {/* Header */}
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <LoadingSkeleton height="2.5rem" width="12rem" className="mb-2" />
        <LoadingSkeleton height="1.25rem" width="20rem" />
      </div>
      <LoadingSkeleton variant="rectangular" height="2.5rem" width="8rem" />
    </div>

    {/* Stats Grid */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {Array.from({ length: 4 }).map((_, i) => (
        <div key={i} className="card p-6">
          <div className="flex items-center justify-between mb-4">
            <LoadingSkeleton variant="circular" width={40} height={40} />
            <LoadingSkeleton height="1rem" width="3rem" />
          </div>
          <LoadingSkeleton height="2rem" width="4rem" className="mb-2" />
          <LoadingSkeleton height="0.875rem" width="6rem" />
        </div>
      ))}
    </div>

    {/* Charts */}
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div className="card p-6">
        <LoadingSkeleton height="1.5rem" width="8rem" className="mb-4" />
        <LoadingSkeleton variant="rectangular" height="20rem" />
      </div>
      <div className="card p-6">
        <LoadingSkeleton height="1.5rem" width="10rem" className="mb-4" />
        <LoadingSkeleton variant="rectangular" height="20rem" />
      </div>
    </div>
  </div>
);

export const MyModelsSkeleton: React.FC = () => (
  <div className="space-y-6 animate-fade-in">
    {/* Header */}
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <LoadingSkeleton height="2.5rem" width="10rem" className="mb-2" />
        <LoadingSkeleton height="1.25rem" width="18rem" />
      </div>
      <LoadingSkeleton variant="rectangular" height="2.5rem" width="10rem" />
    </div>

    {/* Config Cards */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: 6 }).map((_, i) => (
        <div key={i} className="card p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <LoadingSkeleton height="1.5rem" width="8rem" className="mb-2" />
              <LoadingSkeleton height="1rem" width="12rem" />
            </div>
            <LoadingSkeleton variant="circular" width={32} height={32} />
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <LoadingSkeleton height="0.875rem" width="4rem" />
              <LoadingSkeleton height="0.875rem" width="2rem" />
            </div>
            <div className="flex justify-between">
              <LoadingSkeleton height="0.875rem" width="5rem" />
              <LoadingSkeleton height="0.875rem" width="3rem" />
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

export const RoutingSetupSkeleton: React.FC = () => (
  <div className="space-y-8 animate-fade-in">
    {/* Header */}
    <div className="text-center">
      <LoadingSkeleton height="3rem" width="16rem" className="mx-auto mb-4" />
      <LoadingSkeleton height="1.25rem" width="24rem" className="mx-auto" />
    </div>

    {/* Strategy Cards */}
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {Array.from({ length: 4 }).map((_, i) => (
        <div key={i} className="card p-6">
          <div className="flex items-center mb-4">
            <LoadingSkeleton variant="circular" width={48} height={48} className="mr-4" />
            <div className="flex-1">
              <LoadingSkeleton height="1.5rem" width="10rem" className="mb-2" />
              <LoadingSkeleton height="1rem" width="8rem" />
            </div>
          </div>
          <LoadingSkeleton lines={3} height="0.875rem" />
        </div>
      ))}
    </div>
  </div>
);

export const TrainingSkeleton: React.FC = () => (
  <div className="space-y-8 animate-fade-in">
    {/* Header */}
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <LoadingSkeleton height="2.5rem" width="8rem" className="mb-2" />
        <LoadingSkeleton height="1.25rem" width="16rem" />
      </div>
      <LoadingSkeleton variant="rectangular" height="2.5rem" width="12rem" />
    </div>

    {/* Upload Area */}
    <div className="card p-8">
      <div className="text-center">
        <LoadingSkeleton variant="circular" width={64} height={64} className="mx-auto mb-4" />
        <LoadingSkeleton height="1.5rem" width="12rem" className="mx-auto mb-2" />
        <LoadingSkeleton height="1rem" width="20rem" className="mx-auto" />
      </div>
    </div>

    {/* Training Jobs */}
    <div className="card">
      <div className="p-6 border-b border-gray-200">
        <LoadingSkeleton height="1.5rem" width="10rem" />
      </div>
      <div className="divide-y divide-gray-200">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <LoadingSkeleton height="1.25rem" width="12rem" className="mb-2" />
                <LoadingSkeleton height="1rem" width="8rem" />
              </div>
              <div className="flex items-center space-x-4">
                <LoadingSkeleton height="1.5rem" width="4rem" />
                <LoadingSkeleton variant="circular" width={32} height={32} />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

export const AnalyticsSkeleton: React.FC = () => (
  <div className="space-y-8 animate-fade-in">
    {/* Header */}
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <LoadingSkeleton height="2.5rem" width="9rem" className="mb-2" />
        <LoadingSkeleton height="1.25rem" width="18rem" />
      </div>
      <div className="flex space-x-3">
        <LoadingSkeleton variant="rectangular" height="2.5rem" width="8rem" />
        <LoadingSkeleton variant="rectangular" height="2.5rem" width="6rem" />
      </div>
    </div>

    {/* Key Metrics */}
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {Array.from({ length: 3 }).map((_, i) => (
        <div key={i} className="card p-6">
          <LoadingSkeleton height="1.25rem" width="6rem" className="mb-4" />
          <LoadingSkeleton height="2.5rem" width="5rem" className="mb-2" />
          <LoadingSkeleton height="1rem" width="8rem" />
        </div>
      ))}
    </div>

    {/* Charts */}
    <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
      <div className="card p-6">
        <LoadingSkeleton height="1.5rem" width="12rem" className="mb-6" />
        <LoadingSkeleton variant="rectangular" height="24rem" />
      </div>
      <div className="card p-6">
        <LoadingSkeleton height="1.5rem" width="10rem" className="mb-6" />
        <LoadingSkeleton variant="rectangular" height="24rem" />
      </div>
    </div>
  </div>
);

export const PlaygroundSkeleton: React.FC = () => (
  <div className="space-y-6 animate-fade-in">
    {/* Header */}
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <LoadingSkeleton height="2.5rem" width="10rem" className="mb-2" />
        <LoadingSkeleton height="1.25rem" width="16rem" />
      </div>
      <LoadingSkeleton variant="rectangular" height="2.5rem" width="8rem" />
    </div>

    {/* Main Chat Interface */}
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* Chat History Sidebar */}
      <div className="lg:col-span-1">
        <div className="card p-4">
          <LoadingSkeleton height="1.5rem" width="8rem" className="mb-4" />
          <div className="space-y-3">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="p-3 rounded-lg border border-gray-100">
                <LoadingSkeleton height="1rem" width="90%" className="mb-2" />
                <LoadingSkeleton height="0.75rem" width="60%" />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="lg:col-span-3">
        <div className="card p-6">
          {/* Config Selector */}
          <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
            <div className="flex items-center space-x-3">
              <LoadingSkeleton variant="circular" width={32} height={32} />
              <LoadingSkeleton height="1.5rem" width="8rem" />
            </div>
            <LoadingSkeleton variant="rectangular" height="2rem" width="6rem" />
          </div>

          {/* Chat Messages */}
          <div className="space-y-6 mb-6">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className={`flex ${i % 2 === 0 ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-3xl p-4 rounded-2xl ${
                  i % 2 === 0 ? 'bg-orange-50' : 'bg-white border border-gray-200'
                }`}>
                  <LoadingSkeleton lines={2} height="1rem" />
                </div>
              </div>
            ))}
          </div>

          {/* Input Area */}
          <div className="border-t border-gray-200 pt-4">
            <LoadingSkeleton variant="rectangular" height="3rem" className="mb-3" />
            <div className="flex justify-between items-center">
              <LoadingSkeleton height="1rem" width="8rem" />
              <LoadingSkeleton variant="rectangular" height="2rem" width="5rem" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export const LogsSkeleton: React.FC = () => (
  <div className="space-y-6 animate-fade-in">
    {/* Header */}
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <LoadingSkeleton height="2.5rem" width="6rem" className="mb-2" />
        <LoadingSkeleton height="1.25rem" width="14rem" />
      </div>
      <div className="flex space-x-3">
        <LoadingSkeleton variant="rectangular" height="2.5rem" width="8rem" />
        <LoadingSkeleton variant="rectangular" height="2.5rem" width="6rem" />
      </div>
    </div>

    {/* Filters */}
    <div className="card p-4">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <LoadingSkeleton variant="rectangular" height="2.5rem" />
        <LoadingSkeleton variant="rectangular" height="2.5rem" />
        <LoadingSkeleton variant="rectangular" height="2.5rem" />
        <LoadingSkeleton variant="rectangular" height="2.5rem" />
      </div>
    </div>

    {/* Logs Table */}
    <div className="card">
      {/* Table Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="grid grid-cols-6 gap-4">
          <LoadingSkeleton height="1rem" width="4rem" />
          <LoadingSkeleton height="1rem" width="6rem" />
          <LoadingSkeleton height="1rem" width="5rem" />
          <LoadingSkeleton height="1rem" width="4rem" />
          <LoadingSkeleton height="1rem" width="3rem" />
          <LoadingSkeleton height="1rem" width="5rem" />
        </div>
      </div>

      {/* Table Rows */}
      <div className="divide-y divide-gray-200">
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="p-4">
            <div className="grid grid-cols-6 gap-4 items-center">
              <LoadingSkeleton height="1rem" width="80%" />
              <LoadingSkeleton height="1rem" width="90%" />
              <LoadingSkeleton height="1rem" width="70%" />
              <LoadingSkeleton height="1rem" width="60%" />
              <LoadingSkeleton height="1rem" width="50%" />
              <LoadingSkeleton height="1rem" width="85%" />
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

export default LoadingSkeleton;
