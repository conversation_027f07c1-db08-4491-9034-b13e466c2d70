const API_KEY = 'rk_live_c5ae5a36_9Nl8iikIL2SDSNt6R4TujRKEg8xzUkK6';
const BASE_URL = 'https://roukey.online';

// Test cases for async processing
const asyncTestCases = [
  {
    name: 'Complex multi-role task',
    request: {
      messages: [
        { 
          role: 'user', 
          content: 'I need you to research and analyze the current state of quantum computing, then brainstorm innovative applications for quantum algorithms in machine learning, and finally write a comprehensive technical report with code examples and implementation strategies. This should be a detailed, multi-step analysis.' 
        }
      ],
      role: 'research_analyst',
      max_tokens: 4000,
      temperature: 0.8,
      webhook_url: 'https://webhook.site/your-webhook-id' // Replace with your webhook URL
    }
  },
  {
    name: 'Long content generation',
    request: {
      messages: [
        { 
          role: 'user', 
          content: 'Write a comprehensive guide to building a full-stack web application with React, Node.js, and PostgreSQL. Include detailed explanations, code examples, best practices, security considerations, deployment strategies, and testing approaches. Make it suitable for intermediate developers.' 
        }
      ],
      max_tokens: 5000,
      temperature: 0.7
    }
  },
  {
    name: 'Creative brainstorming task',
    request: {
      messages: [
        { 
          role: 'user', 
          content: 'Brainstorm 20 innovative startup ideas for 2024, analyze market potential for each, identify target audiences, estimate funding requirements, and create detailed business model canvases. Focus on AI, sustainability, and remote work trends.' 
        }
      ],
      role: 'business_strategist',
      max_tokens: 3500,
      temperature: 1.2
    }
  }
];

async function submitAsyncJob(testCase) {
  console.log(`\n🚀 Submitting async job: ${testCase.name}`);
  console.log('=' .repeat(60));
  
  try {
    const response = await fetch(`${BASE_URL}/api/external/v1/async/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
        'User-Agent': 'RouKey-Async-Test/1.0'
      },
      body: JSON.stringify(testCase.request)
    });

    const data = await response.json();
    
    console.log(`📊 Status: ${response.status}`);
    console.log(`📥 Response:`, JSON.stringify(data, null, 2));
    
    if (response.ok) {
      return data.job_id;
    } else {
      console.log('❌ Failed to submit job');
      return null;
    }
    
  } catch (error) {
    console.error('❌ Error submitting job:', error.message);
    return null;
  }
}

async function checkJobStatus(jobId) {
  try {
    const response = await fetch(`${BASE_URL}/api/external/v1/async/status/${jobId}`, {
      method: 'GET',
      headers: {
        'X-API-Key': API_KEY,
        'User-Agent': 'RouKey-Async-Test/1.0'
      }
    });

    const data = await response.json();
    return { success: response.ok, data };
    
  } catch (error) {
    console.error('❌ Error checking status:', error.message);
    return { success: false, error: error.message };
  }
}

async function getJobResult(jobId) {
  try {
    const response = await fetch(`${BASE_URL}/api/external/v1/async/result/${jobId}`, {
      method: 'GET',
      headers: {
        'X-API-Key': API_KEY,
        'User-Agent': 'RouKey-Async-Test/1.0'
      }
    });

    const data = await response.json();
    return { success: response.ok, data };
    
  } catch (error) {
    console.error('❌ Error getting result:', error.message);
    return { success: false, error: error.message };
  }
}

async function pollJobUntilComplete(jobId, maxWaitMinutes = 15) {
  console.log(`\n🔍 Polling job ${jobId} for completion...`);
  
  const startTime = Date.now();
  const maxWaitMs = maxWaitMinutes * 60 * 1000;
  
  while (Date.now() - startTime < maxWaitMs) {
    const statusResult = await checkJobStatus(jobId);
    
    if (!statusResult.success) {
      console.log('❌ Failed to check status:', statusResult.error);
      return false;
    }
    
    const { status, progress_percentage, estimated_remaining_minutes, roles_detected } = statusResult.data;
    
    console.log(`📊 Status: ${status} | Progress: ${progress_percentage}% | ETA: ${estimated_remaining_minutes || 'N/A'} min | Roles: ${(roles_detected || []).join(', ') || 'none'}`);
    
    if (status === 'completed') {
      console.log('✅ Job completed!');
      return true;
    } else if (status === 'failed' || status === 'timeout') {
      console.log(`❌ Job ${status}: ${statusResult.data.error_message || 'Unknown error'}`);
      return false;
    }
    
    // Wait before next poll
    await new Promise(resolve => setTimeout(resolve, 10000)); // 10 seconds
  }
  
  console.log('⏰ Polling timeout reached');
  return false;
}

async function runAsyncTests() {
  console.log('🚀 Starting RouKey Async API Tests');
  console.log(`🔑 API Key: ${API_KEY.substring(0, 20)}...`);
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log(`📅 Started at: ${new Date().toISOString()}`);
  
  const jobIds = [];
  
  // Submit all jobs
  for (const testCase of asyncTestCases) {
    const jobId = await submitAsyncJob(testCase);
    if (jobId) {
      jobIds.push({ jobId, name: testCase.name });
    }
    
    // Wait between submissions
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log(`\n📋 Submitted ${jobIds.length} jobs successfully`);
  
  // Poll first job for completion and get result
  if (jobIds.length > 0) {
    const firstJob = jobIds[0];
    console.log(`\n🎯 Focusing on first job: ${firstJob.name}`);
    
    const completed = await pollJobUntilComplete(firstJob.jobId);
    
    if (completed) {
      console.log('\n📥 Getting job result...');
      const result = await getJobResult(firstJob.jobId);
      
      if (result.success) {
        console.log('✅ Result retrieved successfully!');
        console.log(`📏 Response length: ${JSON.stringify(result.data).length} characters`);
        console.log(`🏷️  Roles used: ${(result.data.rokey_metadata?.roles_used || []).join(', ') || 'none'}`);
        console.log(`⏱️  Processing time: ${result.data.rokey_metadata?.processing_time_minutes || 'N/A'} minutes`);
        
        // Show first 500 characters of response
        const content = result.data.choices?.[0]?.message?.content || 'No content';
        console.log(`📝 Content preview: ${content.substring(0, 500)}${content.length > 500 ? '...' : ''}`);
      } else {
        console.log('❌ Failed to get result:', result.error);
      }
    }
  }
  
  // Show status of all jobs
  console.log('\n📊 Final status of all jobs:');
  for (const job of jobIds) {
    const statusResult = await checkJobStatus(job.jobId);
    if (statusResult.success) {
      const { status, progress_percentage } = statusResult.data;
      console.log(`  ${job.name}: ${status} (${progress_percentage}%)`);
    } else {
      console.log(`  ${job.name}: Error checking status`);
    }
  }
  
  console.log('\n🎉 Async API tests completed!');
}

// Run the tests
runAsyncTests().catch(console.error);
