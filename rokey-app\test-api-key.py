#!/usr/bin/env python3

"""
RouKey API Key Test Script (Python)
Tests the user-generated API key functionality with various scenarios
"""

import requests
import json
import time
import sys
from datetime import datetime

# Configuration
API_KEY = 'rk_live_c5ae5a36_9Nl8iikIL2SDSNt6R4TujRKEg8xzUkK6'
BASE_URL = 'http://localhost:3000'  # Change to your deployed URL if testing production
ENDPOINT = f'{BASE_URL}/api/external/v1/chat/completions'

# Test scenarios
test_cases = [
    {
        'name': 'Simple greeting',
        'messages': [{'role': 'user', 'content': 'hi'}],
        'stream': False
    },
    {
        'name': 'Code generation request',
        'messages': [{'role': 'user', 'content': 'code a snake game in python'}],
        'stream': False
    },
    {
        'name': 'Complex creative task',
        'messages': [{'role': 'user', 'content': 'brainstorm an idea for a book and write a python code about it'}],
        'stream': <PERSON>alse
    },
    {
        'name': 'Simple greeting (streaming)',
        'messages': [{'role': 'user', 'content': 'hi'}],
        'stream': True
    },
    {
        'name': 'Code generation (streaming)',
        'messages': [{'role': 'user', 'content': 'code a simple calculator in python'}],
        'stream': True
    },
    {
        'name': 'Role-based routing test',
        'messages': [{'role': 'user', 'content': 'Explain quantum computing in simple terms'}],
        'role': 'science_expert',
        'stream': False
    },
    {
        'name': 'Temperature test (creative)',
        'messages': [{'role': 'user', 'content': 'Write a creative story about a robot'}],
        'temperature': 1.5,
        'stream': False
    },
    {
        'name': 'Temperature test (factual)',
        'messages': [{'role': 'user', 'content': 'What is the capital of France?'}],
        'temperature': 0.1,
        'stream': False
    }
]

def make_request(test_case, test_number):
    print(f"\n🧪 Test {test_number}: {test_case['name']}")
    print('=' * 50)
    
    request_body = {
        # No model specified - RouKey will use your configured models
        'messages': test_case['messages'],
        'stream': test_case.get('stream', False),
        'max_tokens': 500
    }
    
    # Add optional parameters
    if 'temperature' in test_case:
        request_body['temperature'] = test_case['temperature']
    if 'role' in test_case:
        request_body['role'] = test_case['role']

    print(f'📤 Request: {json.dumps(request_body, indent=2)}')
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {API_KEY}',
        'User-Agent': 'RouKey-Test-Script-Python/1.0'
    }
    
    try:
        start_time = time.time()
        
        if test_case.get('stream', False):
            # Streaming request
            response = requests.post(
                ENDPOINT,
                headers=headers,
                json=request_body,
                stream=True,
                timeout=60
            )
            
            end_time = time.time()
            duration = (end_time - start_time) * 1000  # Convert to ms
            
            print(f'📊 Status: {response.status_code} {response.reason}')
            print(f'⏱️  Duration: {duration:.0f}ms')
            print(f'📋 Headers: {dict(response.headers)}')
            
            if response.ok:
                print('📡 Streaming response:')
                print('─' * 30)
                
                full_response = ''
                for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                    if chunk:
                        print(chunk, end='', flush=True)
                        full_response += chunk
                
                print('\n' + '─' * 30)
                print(f'📏 Total response length: {len(full_response)} characters')
            else:
                print(f'❌ Request failed: {response.text}')
                return False
                
        else:
            # Non-streaming request
            response = requests.post(
                ENDPOINT,
                headers=headers,
                json=request_body,
                timeout=60
            )
            
            end_time = time.time()
            duration = (end_time - start_time) * 1000  # Convert to ms
            
            print(f'📊 Status: {response.status_code} {response.reason}')
            print(f'⏱️  Duration: {duration:.0f}ms')
            print(f'📋 Headers: {dict(response.headers)}')
            
            if response.ok:
                response_data = response.json()
                print(f'📥 Response: {json.dumps(response_data, indent=2)}')
                
                if 'choices' in response_data and response_data['choices']:
                    content = response_data['choices'][0].get('message', {}).get('content', '')
                    print(f'📏 Response length: {len(content)} characters')
            else:
                print(f'❌ Request failed: {response.text}')
                return False

        print('✅ Request successful')
        return True

    except requests.exceptions.RequestException as e:
        print(f'❌ Error: {str(e)}')
        return False
    except Exception as e:
        print(f'❌ Unexpected error: {str(e)}')
        return False

def run_tests():
    print('🚀 Starting RouKey API Key Tests')
    print(f'🔑 API Key: {API_KEY[:20]}...')
    print(f'🌐 Endpoint: {ENDPOINT}')
    print(f'📅 Started at: {datetime.now().isoformat()}')
    
    success_count = 0
    total_tests = len(test_cases)

    for i, test_case in enumerate(test_cases):
        success = make_request(test_case, i + 1)
        if success:
            success_count += 1
        
        # Wait between requests to avoid rate limiting
        if i < len(test_cases) - 1:
            print('\n⏳ Waiting 2 seconds before next test...')
            time.sleep(2)

    print('\n' + '=' * 60)
    print('📊 TEST SUMMARY')
    print('=' * 60)
    print(f'✅ Successful: {success_count}/{total_tests}')
    print(f'❌ Failed: {total_tests - success_count}/{total_tests}')
    print(f'📈 Success Rate: {(success_count / total_tests * 100):.1f}%')
    print(f'📅 Completed at: {datetime.now().isoformat()}')
    
    if success_count == total_tests:
        print('🎉 All tests passed! Your API key is working perfectly.')
        return 0
    else:
        print('⚠️  Some tests failed. Check the logs above for details.')
        return 1

if __name__ == '__main__':
    try:
        exit_code = run_tests()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print('\n❌ Tests interrupted by user')
        sys.exit(1)
    except Exception as e:
        print(f'❌ Fatal error: {str(e)}')
        sys.exit(1)
