'use client';

import { useState } from 'react';
import { useSubscription } from '@/hooks/useSubscription';

interface DebugData {
  userId: string;
  timestamp: string;
  profile: {
    data: any;
    error?: string;
    subscription_tier: string;
    subscription_status: string;
    user_status: string;
    updated_at: string;
  };
  subscriptions: {
    total_count: number;
    error?: string;
    all_subscriptions: any[];
  };
  latest_subscription: any;
  active_subscription: {
    data: any;
    error?: string;
  };
  tier_determination: {
    profile_tier: string;
    latest_subscription_tier: string;
    latest_subscription_status: string;
    active_subscription_tier: string;
    active_subscription_status: string;
    final_tier_logic: string;
  };
}

export default function DebugSubscriptionStatus() {
  const { user } = useSubscription();
  const [debugData, setDebugData] = useState<DebugData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchDebugData = async () => {
    if (!user) {
      setError('No user logged in');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/debug/subscription-data?userId=${user.id}&_t=${Date.now()}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setDebugData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      console.error('Debug fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-yellow-800">Please log in to view subscription debug data.</p>
      </div>
    );
  }

  return (
    <div className="p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">🔍 Subscription Debug Tool</h3>
        <button
          onClick={fetchDebugData}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Loading...' : 'Fetch Debug Data'}
        </button>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800 text-sm">Error: {error}</p>
        </div>
      )}

      {debugData && (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Profile Info */}
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-2">👤 User Profile</h4>
              <div className="text-sm space-y-1">
                <p><span className="font-medium">Tier:</span> <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded">{debugData.profile.subscription_tier}</span></p>
                <p><span className="font-medium">Status:</span> <span className="px-2 py-1 bg-green-100 text-green-800 rounded">{debugData.profile.subscription_status}</span></p>
                <p><span className="font-medium">User Status:</span> <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded">{debugData.profile.user_status}</span></p>
                <p><span className="font-medium">Updated:</span> {new Date(debugData.profile.updated_at).toLocaleString()}</p>
              </div>
            </div>

            {/* Active Subscription */}
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-2">🎯 Active Subscription</h4>
              {debugData.active_subscription.data ? (
                <div className="text-sm space-y-1">
                  <p><span className="font-medium">Tier:</span> <span className="px-2 py-1 bg-green-100 text-green-800 rounded">{debugData.active_subscription.data.tier}</span></p>
                  <p><span className="font-medium">Status:</span> <span className="px-2 py-1 bg-green-100 text-green-800 rounded">{debugData.active_subscription.data.status}</span></p>
                  <p><span className="font-medium">Updated:</span> {new Date(debugData.active_subscription.data.updated_at).toLocaleString()}</p>
                  <p><span className="font-medium">Stripe ID:</span> <span className="text-xs font-mono bg-gray-200 px-1 rounded">{debugData.active_subscription.data.stripe_subscription_id}</span></p>
                </div>
              ) : (
                <p className="text-sm text-gray-600">No active subscription found</p>
              )}
              {debugData.active_subscription.error && (
                <p className="text-sm text-red-600 mt-2">Error: {debugData.active_subscription.error}</p>
              )}
            </div>
          </div>

          {/* Tier Determination Logic */}
          <div className="p-4 bg-blue-50 rounded-lg">
            <h4 className="font-semibold text-gray-900 mb-2">🧠 Tier Determination Logic</h4>
            <p className="text-sm text-gray-700 mb-2">{debugData.tier_determination.final_tier_logic}</p>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-2 text-xs">
              <div>
                <span className="font-medium">Profile:</span>
                <br />
                <span className="px-1 py-0.5 bg-gray-200 rounded">{debugData.tier_determination.profile_tier}</span>
              </div>
              <div>
                <span className="font-medium">Latest Sub:</span>
                <br />
                <span className="px-1 py-0.5 bg-gray-200 rounded">{debugData.tier_determination.latest_subscription_tier || 'N/A'}</span>
              </div>
              <div>
                <span className="font-medium">Latest Status:</span>
                <br />
                <span className="px-1 py-0.5 bg-gray-200 rounded">{debugData.tier_determination.latest_subscription_status || 'N/A'}</span>
              </div>
              <div>
                <span className="font-medium">Active Sub:</span>
                <br />
                <span className="px-1 py-0.5 bg-gray-200 rounded">{debugData.tier_determination.active_subscription_tier || 'N/A'}</span>
              </div>
              <div>
                <span className="font-medium">Active Status:</span>
                <br />
                <span className="px-1 py-0.5 bg-gray-200 rounded">{debugData.tier_determination.active_subscription_status || 'N/A'}</span>
              </div>
            </div>
          </div>

          {/* All Subscriptions */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h4 className="font-semibold text-gray-900 mb-2">📋 All Subscriptions ({debugData.subscriptions.total_count})</h4>
            {debugData.subscriptions.all_subscriptions.length > 0 ? (
              <div className="space-y-2">
                {debugData.subscriptions.all_subscriptions.map((sub, index) => (
                  <div key={sub.id} className="p-2 bg-white border rounded text-sm">
                    <div className="flex justify-between items-start">
                      <div>
                        <span className="font-medium">#{index + 1}</span> - 
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded ml-1">{sub.tier}</span>
                        <span className={`px-2 py-1 rounded ml-1 ${
                          sub.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>{sub.status}</span>
                      </div>
                      <div className="text-xs text-gray-500">
                        Updated: {new Date(sub.updated_at).toLocaleString()}
                      </div>
                    </div>
                    <div className="text-xs text-gray-600 mt-1 font-mono">
                      {sub.stripe_subscription_id}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-600">No subscriptions found</p>
            )}
          </div>

          {/* Timestamp */}
          <div className="text-xs text-gray-500 text-center">
            Last fetched: {new Date(debugData.timestamp).toLocaleString()}
          </div>
        </div>
      )}
    </div>
  );
}
