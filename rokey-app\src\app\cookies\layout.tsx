import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Cookie Policy - RouKey',
  description: 'Learn how <PERSON><PERSON><PERSON><PERSON> uses cookies and similar technologies to enhance your experience and improve our services.',
  keywords: ['cookie policy', 'cookies', 'tracking', 'privacy', 'RouKey', 'data collection'],
  openGraph: {
    title: 'Cookie Policy - RouKey',
    description: 'Learn how <PERSON><PERSON><PERSON><PERSON> uses cookies and similar technologies to enhance your experience and improve our services.',
    type: 'website',
    url: 'https://roukey.online/cookies',
    images: [
      {
        url: 'https://roukey.online/og-cookies.jpg',
        width: 1200,
        height: 630,
        alt: 'RouKey Cookie Policy',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Cookie Policy - RouKey',
    description: 'Learn how <PERSON><PERSON><PERSON><PERSON> uses cookies and similar technologies to enhance your experience.',
    images: ['https://roukey.online/og-cookies.jpg'],
  },
  alternates: {
    canonical: 'https://roukey.online/cookies',
  },
};

export default function CookiesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
