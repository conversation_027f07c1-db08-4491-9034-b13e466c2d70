# RouKey API Key Testing

This directory contains comprehensive test scripts to validate your RouKey user-generated API key functionality.

## 🔑 Your API Key
```
rk_live_c5ae5a36_9Nl8iikIL2SDSNt6R4TujRKEg8xzUkK6
```

## 🌐 API Endpoint
```
http://localhost:3000/api/external/v1/chat/completions
```

## 📋 Test Scenarios

The test scripts cover the following scenarios:

1. **Simple greeting** - Basic "hi" message
2. **Code generation** - "code a snake game in python"
3. **Complex creative task** - "brainstorm an idea for a book and write a python code about it"
4. **Streaming responses** - Same requests with streaming enabled
5. **Role-based routing** - Using <PERSON><PERSON><PERSON><PERSON>'s role parameter
6. **Temperature variations** - Testing creative vs factual responses
7. **OpenAI compatibility** - Ensuring standard OpenAI SDK compatibility

## 🚀 Running the Tests

### Option 1: Node.js (Recommended)
```bash
node test-api-key.js
```

**Features:**
- ✅ Comprehensive testing
- ✅ Streaming support
- ✅ Detailed logging
- ✅ Performance metrics
- ✅ Error handling

### Option 2: Python
```bash
python3 test-api-key.py
```

**Features:**
- ✅ Comprehensive testing
- ✅ Streaming support
- ✅ Detailed logging
- ✅ Performance metrics
- ✅ Error handling

### Option 3: Curl (Quick Tests)
```bash
# On Windows (PowerShell)
bash test-api-key.sh

# On Linux/Mac
./test-api-key.sh
```

**Features:**
- ✅ Quick validation
- ✅ Basic streaming test
- ✅ Simple to run
- ⚠️ Limited error handling

## 🔍 What to Look For

### ✅ Success Indicators
- **Status Code**: 200 for successful requests
- **Response Format**: OpenAI-compatible JSON structure
- **Streaming**: Proper SSE format for streaming responses
- **Role Routing**: Different responses based on role parameter
- **Temperature**: Creative vs factual responses based on temperature
- **Performance**: Reasonable response times (< 30 seconds)

### 📊 Expected Response Format
```json
{
  "id": "chatcmpl-...",
  "object": "chat.completion",
  "created": **********,
  "model": "gpt-3.5-turbo",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! How can I help you today?"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 10,
    "completion_tokens": 20,
    "total_tokens": 30
  }
}
```

### ❌ Error Indicators
- **401 Unauthorized**: API key issues
- **403 Forbidden**: Permission or rate limit issues
- **500 Internal Server Error**: Server-side problems
- **Timeout**: Network or processing issues

## 🔧 Configuration

### Changing the Base URL
Edit the scripts to test against production:

**Node.js/Python:**
```javascript
const BASE_URL = 'https://your-domain.com';
```

**Curl:**
```bash
BASE_URL="https://your-domain.com"
```

### Testing Different API Keys
Replace the `API_KEY` variable in any script with your test key.

## 📈 Monitoring

### Server Logs
Check your RouKey application logs for:
- API key authentication
- Request routing decisions
- Provider API calls
- Error messages
- Performance metrics

### Database Logs
Monitor the `user_generated_api_key_usage_logs` table for:
- Request counts
- Usage patterns
- Error tracking
- Performance data

## 🐛 Troubleshooting

### Common Issues

1. **Connection Refused**
   - Ensure your RouKey app is running on localhost:3000
   - Check firewall settings

2. **Invalid API Key**
   - Verify the API key is correct
   - Check if the key is active in your dashboard

3. **Permission Denied**
   - Verify API key has chat and streaming permissions
   - Check IP restrictions if configured

4. **Rate Limiting**
   - Wait between requests
   - Check your subscription tier limits

5. **Streaming Issues**
   - Ensure your client supports Server-Sent Events (SSE)
   - Check network proxy settings

## 📞 Support

If tests fail consistently:
1. Check the server logs
2. Verify your API configuration
3. Test with a fresh API key
4. Check your subscription status

## 🎯 Next Steps

After successful testing:
1. ✅ Integrate with your applications
2. ✅ Set up monitoring and alerting
3. ✅ Configure rate limits appropriately
4. ✅ Test in production environment
5. ✅ Document your API usage patterns
