// Performance monitoring utility for Phase 1 optimizations
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // Track API response times
  trackApiResponse(endpoint: string, duration: number) {
    if (!this.metrics.has(endpoint)) {
      this.metrics.set(endpoint, []);
    }
    this.metrics.get(endpoint)!.push(duration);
    
    // Keep only last 100 measurements
    const measurements = this.metrics.get(endpoint)!;
    if (measurements.length > 100) {
      measurements.shift();
    }
  }

  // Get performance statistics
  getStats(endpoint: string) {
    const measurements = this.metrics.get(endpoint);
    if (!measurements || measurements.length === 0) {
      return null;
    }

    const sorted = [...measurements].sort((a, b) => a - b);
    const avg = measurements.reduce((sum, val) => sum + val, 0) / measurements.length;
    const median = sorted[Math.floor(sorted.length / 2)];
    const p95 = sorted[Math.floor(sorted.length * 0.95)];
    const min = sorted[0];
    const max = sorted[sorted.length - 1];

    return {
      count: measurements.length,
      average: Math.round(avg),
      median: Math.round(median),
      p95: Math.round(p95),
      min: Math.round(min),
      max: Math.round(max),
      recent: measurements.slice(-10).map(m => Math.round(m))
    };
  }

  // Log performance summary
  logSummary() {
    console.log('\n=== RoKey Performance Summary ===');
    for (const [endpoint, measurements] of this.metrics.entries()) {
      const stats = this.getStats(endpoint);
      if (stats) {
        console.log(`${endpoint}:`, stats);
      }
    }
    console.log('================================\n');
  }

  // Clear metrics
  clear() {
    this.metrics.clear();
  }
}

// Helper function to measure async operations
export async function measureAsync<T>(
  operation: () => Promise<T>,
  label: string
): Promise<T> {
  const start = performance.now();
  try {
    const result = await operation();
    const duration = performance.now() - start;
    PerformanceMonitor.getInstance().trackApiResponse(label, duration);
    return result;
  } catch (error) {
    const duration = performance.now() - start;
    PerformanceMonitor.getInstance().trackApiResponse(`${label}_error`, duration);
    throw error;
  }
}

// Helper function to measure sync operations
export function measureSync<T>(
  operation: () => T,
  label: string
): T {
  const start = performance.now();
  try {
    const result = operation();
    const duration = performance.now() - start;
    PerformanceMonitor.getInstance().trackApiResponse(label, duration);
    return result;
  } catch (error) {
    const duration = performance.now() - start;
    PerformanceMonitor.getInstance().trackApiResponse(`${label}_error`, duration);
    throw error;
  }
}
