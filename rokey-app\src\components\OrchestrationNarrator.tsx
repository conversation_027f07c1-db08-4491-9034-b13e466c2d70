'use client';

import React, { useState, useEffect } from 'react';
import { 
  SpeakerWaveIcon,
  ChatBubbleLeftRightIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

interface OrchestrationNarratorProps {
  currentNarration: string;
  phase: 'planning' | 'executing' | 'synthesizing' | 'complete';
}

export const OrchestrationNarrator: React.FC<OrchestrationNarratorProps> = ({
  currentNarration,
  phase
}) => {
  const [displayedText, setDisplayedText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [narrationHistory, setNarrationHistory] = useState<string[]>([]);

  // Typewriter effect for new narrations
  useEffect(() => {
    if (!currentNarration || currentNarration === displayedText) return;

    setIsTyping(true);
    setDisplayedText('');
    
    // Add to history if it's a new narration
    if (currentNarration && !narrationHistory.includes(currentNarration)) {
      setNarrationHistory(prev => [...prev.slice(-4), currentNarration]); // Keep last 5
    }

    let currentIndex = 0;
    const typingSpeed = 30; // milliseconds per character

    const typeInterval = setInterval(() => {
      if (currentIndex < currentNarration.length) {
        setDisplayedText(currentNarration.slice(0, currentIndex + 1));
        currentIndex++;
      } else {
        setIsTyping(false);
        clearInterval(typeInterval);
      }
    }, typingSpeed);

    return () => clearInterval(typeInterval);
  }, [currentNarration, displayedText, narrationHistory]);

  const getPhaseColor = () => {
    switch (phase) {
      case 'planning':
        return 'from-blue-500 to-blue-600';
      case 'executing':
        return 'from-orange-500 to-orange-600';
      case 'synthesizing':
        return 'from-purple-500 to-purple-600';
      case 'complete':
        return 'from-green-500 to-green-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const getPhaseIcon = () => {
    switch (phase) {
      case 'planning':
        return <ChatBubbleLeftRightIcon className="w-5 h-5" />;
      case 'executing':
        return <SpeakerWaveIcon className="w-5 h-5" />;
      case 'synthesizing':
        return <SparklesIcon className="w-5 h-5 animate-spin" />;
      case 'complete':
        return <SparklesIcon className="w-5 h-5" />;
      default:
        return <SpeakerWaveIcon className="w-5 h-5" />;
    }
  };

  const getPhaseName = () => {
    switch (phase) {
      case 'planning':
        return 'AI Moderator';
      case 'executing':
        return 'Team Update';
      case 'synthesizing':
        return 'Synthesis';
      case 'complete':
        return 'Complete';
      default:
        return 'Moderator';
    }
  };

  return (
    <div className="mb-6">
      {/* Current Narration */}
      <div className={`relative p-4 rounded-xl bg-gradient-to-r ${getPhaseColor()} text-white shadow-lg`}>
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 mt-1">
            {getPhaseIcon()}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-semibold opacity-90">
                {getPhaseName()}
              </h4>
              {isTyping && (
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                  <div className="w-2 h-2 bg-white rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                </div>
              )}
            </div>
            <p className="text-white text-sm leading-relaxed">
              {displayedText}
              {isTyping && (
                <span className="inline-block w-0.5 h-4 bg-white ml-1 animate-pulse" />
              )}
            </p>
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-2 right-2 opacity-20">
          <div className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center">
            <SparklesIcon className="w-4 h-4" />
          </div>
        </div>
      </div>

      {/* Narration History */}
      {narrationHistory.length > 1 && (
        <div className="mt-3 space-y-2">
          <button
            className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
            onClick={() => {
              // Toggle history visibility
              const historyElement = document.getElementById('narration-history');
              if (historyElement) {
                historyElement.classList.toggle('hidden');
              }
            }}
          >
            View recent updates ({narrationHistory.length - 1} previous)
          </button>
          
          <div id="narration-history" className="hidden space-y-2 max-h-40 overflow-y-auto">
            {narrationHistory.slice(0, -1).reverse().map((narration, index) => (
              <div
                key={index}
                className="p-3 bg-gray-50 rounded-lg border border-gray-200 text-sm text-gray-700"
              >
                <div className="flex items-start space-x-2">
                  <div className="w-4 h-4 rounded-full bg-gray-300 flex-shrink-0 mt-0.5" />
                  <p className="leading-relaxed">{narration}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Live indicator */}
      {isTyping && (
        <div className="mt-2 flex items-center space-x-2 text-xs text-gray-500">
          <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
          <span>Live commentary</span>
        </div>
      )}
    </div>
  );
};
