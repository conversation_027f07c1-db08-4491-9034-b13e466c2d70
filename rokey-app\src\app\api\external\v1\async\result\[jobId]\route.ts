import { type NextRequest, NextResponse } from 'next/server';
import { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';
import { AsyncJobManager } from '@/lib/async/jobManager';

const authMiddleware = new ApiKeyAuthMiddleware();
const jobManager = new AsyncJobManager();

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig } = authResult;

    // 2. Await params and get job
    const { jobId } = await params;
    const job = await jobManager.getJob(jobId);
    
    if (!job) {
      return NextResponse.json(
        {
          error: {
            message: 'Job not found',
            type: 'not_found_error',
            code: 'job_not_found'
          }
        },
        { status: 404 }
      );
    }

    // 3. Check if user owns this job
    if (job.user_id !== userConfig!.user_id) {
      return NextResponse.json(
        {
          error: {
            message: 'Access denied to this job',
            type: 'permission_error',
            code: 'access_denied'
          }
        },
        { status: 403 }
      );
    }

    // 4. Check if job is completed
    if (job.status !== 'completed') {
      return NextResponse.json(
        {
          error: {
            message: `Job is not completed. Current status: ${job.status}`,
            type: 'job_not_ready_error',
            code: 'job_not_completed',
            current_status: job.status,
            status_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/external/v1/async/status/${jobId}`
          }
        },
        { status: 409 }
      );
    }

    // 5. Return the result with enhanced metadata
    const enhancedResponse = {
      ...job.response_data,
      // Add RouKey-specific metadata
      rokey_metadata: {
        job_id: job.id,
        roles_used: job.roles_detected || [],
        processing_time_minutes: job.started_at && job.completed_at 
          ? Math.floor((new Date(job.completed_at).getTime() - new Date(job.started_at).getTime()) / 60000)
          : null,
        created_at: job.created_at,
        completed_at: job.completed_at,
        config_name: userConfig!.name,
        api_key_name: userApiKey!.key_name,
        ...(job.response_data?.rokey_metadata || {})
      }
    };

    return NextResponse.json(enhancedResponse, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
        'X-RouKey-Job-ID': jobId,
        'X-RouKey-Roles-Used': (job.roles_detected || []).join(', ') || 'none',
        'X-RouKey-Config': userConfig!.name,
        'Cache-Control': 'public, max-age=3600', // Cache results for 1 hour
      }
    });

  } catch (error) {
    console.error('Error in async result API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// Handle CORS preflight requests
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      'Access-Control-Max-Age': '86400',
    },
  });
}
