# 🚀 RouKey Training System Test Guide

## **What We've Implemented: Option A - Prompt Engineering**

Your RouKey application now has a **complete training system** that enhances your meta-model configurations with custom behavior, examples, and knowledge base content. This works with **ALL your configured models** (OpenAI, Anthropic, Google, DeepSeek, XAI, etc.) through intelligent prompt engineering.

## **How It Works**

### **1. Training Data Processing**
- **System Instructions**: Core personality and role definitions
- **Behavior Guidelines**: How the AI should behave in different situations  
- **Training Examples**: Input → Output patterns for the AI to follow
- **Knowledge Base**: Content from uploaded files (.txt, .pdf, .docx, .md)

### **2. Automatic Enhancement**
When users chat in the Playground:
1. System loads training data for the selected configuration
2. Enhances the conversation with training context
3. Your router selects the best underlying model
4. **All models receive the same enhanced training context**
5. Responses follow your training patterns consistently

## **🧪 Testing Instructions**

### **Step 1: Create Training Data**

1. **Go to Training Page**
2. **Select an API Configuration** (any of your existing configs)
3. **Enter Training Prompts** using this format:

```
SYSTEM: You are a helpful customer service agent for TechCorp
BEHAVIOR: Always be polite, empathetic, and solution-focused

User asks about returns → I'd be happy to help with your return! Let me check our 30-day policy for you.
Customer is frustrated → I completely understand your frustration. Let me see how I can resolve this for you right away.
User wants technical support → I'll connect you with our technical team who can provide expert assistance.

Always end responses with "Is there anything else I can help you with today?"
```

4. **Upload Knowledge Base Files** (optional):
   - Create a .txt file with company policies
   - Upload product documentation
   - Add FAQ content

5. **Click "Start Training"**

### **Step 2: Test Enhanced Behavior**

1. **Go to Playground**
2. **Select the SAME configuration** you just trained
3. **Test the training examples**:

```
Test Message 1: "I want to return this product"
Expected: Should mention 30-day policy and be helpful

Test Message 2: "This is so frustrating!"
Expected: Should acknowledge frustration and offer solutions

Test Message 3: "I need technical help"
Expected: Should mention connecting with technical team
```

4. **Verify consistency**: All responses should end with "Is there anything else I can help you with today?"

### **Step 3: Test Knowledge Base**

If you uploaded files:
1. **Ask questions about the content** in your uploaded files
2. **Verify the AI references** the knowledge base information
3. **Test with different underlying models** (your router will select different providers)

### **Step 4: Test Cross-Model Consistency**

1. **Send the same message multiple times**
2. **Your router will select different models** (GPT-4, Claude, Gemini, etc.)
3. **All responses should follow the same training patterns**
4. **Behavior should be consistent** regardless of which underlying model is used

## **🔍 What to Look For**

### **✅ Success Indicators**
- Responses follow your training examples
- AI uses your specified behavior guidelines
- Knowledge from uploaded files is referenced
- Consistent personality across all model selections
- System instructions are followed

### **🚨 Troubleshooting**

**If training doesn't seem to work:**
1. Check browser console for errors
2. Verify the configuration ID matches between training and playground
3. Ensure training job shows "completed" status
4. Try refreshing the playground page

**If responses are inconsistent:**
- Your router is working correctly - different models interpret prompts differently
- The training context is being applied to all models
- Some variation is normal and expected

## **🎯 Advanced Testing**

### **Test Multiple Configurations**
1. Create different training for different configs
2. Switch between configs in playground
3. Verify each has its own distinct behavior

### **Test File Processing**
1. Upload different file types (.txt, .pdf, .docx, .md)
2. Verify content is extracted and used
3. Check that file knowledge appears in responses

### **Test Complex Examples**
```
User complains about billing → Acknowledge concern, explain billing process from knowledge base, offer to escalate to billing team
User asks about product features → Reference uploaded product documentation, provide detailed feature explanation
User needs urgent help → Show empathy, prioritize urgency, offer immediate assistance options
```

## **🔧 Technical Details**

### **Database Tables Created**
- `training_jobs`: Stores training configurations
- `training_files`: Stores uploaded file content
- Enhanced `api_keys` with temperature control

### **API Endpoints**
- `POST /api/training/jobs`: Create training jobs
- `GET /api/training/jobs?active_only=true`: Load training data
- `POST /api/training/files`: Upload and process files

### **Chat Enhancement**
- Training data is loaded automatically in `/api/v1/chat/completions`
- System prompts are enhanced with training context
- Works with all providers in your routing system

## **🚀 Next Steps**

After successful testing:
1. **Create training for all your configurations**
2. **Upload comprehensive knowledge bases**
3. **Train different personalities** for different use cases
4. **Monitor chat logs** to see training effectiveness
5. **Iterate and improve** training examples based on results

## **💡 Pro Tips**

- **Be specific in examples**: The more detailed your training examples, the better the results
- **Use consistent language**: Keep your behavior guidelines clear and consistent
- **Test edge cases**: Try unusual requests to see how training handles them
- **Update regularly**: Refine your training based on real usage patterns

Your RoKey application now has **production-ready training capabilities** that work with your entire model ecosystem! 🎉
