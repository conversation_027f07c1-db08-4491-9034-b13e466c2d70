'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { 
  KeyIcon, 
  CogIcon, 
  DocumentTextIcon, 
  ChartBarIcon,
  BeakerIcon,
  MapIcon
} from '@heroicons/react/24/outline';

interface SearchResult {
  id: string;
  title: string;
  subtitle: string;
  type: 'config' | 'api-key' | 'log' | 'page' | 'user-api-key';
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  metadata?: string;
}

interface GlobalSearchProps {
  isOpen: boolean;
  onClose: () => void;
}

export function GlobalSearch({ isOpen, onClose }: GlobalSearchProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const router = useRouter();
  const inputRef = useRef<HTMLInputElement>(null);

  // Static page results
  const staticPages: SearchResult[] = [
    {
      id: 'dashboard',
      title: 'Dashboard',
      subtitle: 'Overview & analytics',
      type: 'page',
      href: '/dashboard',
      icon: ChartBarIcon
    },
    {
      id: 'my-models',
      title: 'My Models',
      subtitle: 'API key management',
      type: 'page',
      href: '/my-models',
      icon: KeyIcon
    },
    {
      id: 'playground',
      title: 'Playground',
      subtitle: 'Test your models',
      type: 'page',
      href: '/playground',
      icon: BeakerIcon
    },
    {
      id: 'routing-setup',
      title: 'Routing Setup',
      subtitle: 'Configure routing',
      type: 'page',
      href: '/routing-setup',
      icon: MapIcon
    },
    {
      id: 'logs',
      title: 'Logs',
      subtitle: 'Request history',
      type: 'page',
      href: '/logs',
      icon: DocumentTextIcon
    }
  ];

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Search function
  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    setLoading(true);
    const allResults: SearchResult[] = [];

    try {
      // Search static pages
      const pageResults = staticPages.filter(page =>
        page.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        page.subtitle.toLowerCase().includes(searchQuery.toLowerCase())
      );
      allResults.push(...pageResults);

      // Search API configurations
      let configResults: SearchResult[] = [];
      try {
        const configResponse = await fetch('/api/custom-configs');
        if (configResponse.ok) {
          const configData = await configResponse.json();
          // The API returns data directly as an array, not wrapped in a configs property
          const configs = Array.isArray(configData) ? configData : [];
          configResults = configs
            .filter((config: any) =>
              config?.name?.toLowerCase().includes(searchQuery.toLowerCase())
            )
            .map((config: any) => ({
              id: `config-${config.id}`,
              title: config.name,
              subtitle: `Configuration • ${config.routing_strategy || 'Default'}`,
              type: 'config' as const,
              href: `/my-models/${config.id}`,
              icon: CogIcon,
              metadata: new Date(config.created_at).toLocaleDateString()
            }));
        }
      } catch (error) {
        console.error('Error searching configurations:', error);
      }

      // Search user-generated API keys
      let userKeyResults: SearchResult[] = [];
      try {
        const userKeysResponse = await fetch('/api/user-api-keys');
        if (userKeysResponse.ok) {
          const userKeysData = await userKeysResponse.json();
          const apiKeys = userKeysData.api_keys || userKeysData || [];
          if (Array.isArray(apiKeys)) {
            userKeyResults = apiKeys
              .filter((key: any) =>
                key?.key_name?.toLowerCase().includes(searchQuery.toLowerCase())
              )
              .map((key: any) => ({
                id: `user-key-${key.id}`,
                title: key.key_name,
                subtitle: `User API Key • ${key.status || 'Unknown'}`,
                type: 'user-api-key' as const,
                href: `/my-models/${key.custom_api_config_id}?tab=user-api-keys`,
                icon: KeyIcon,
                metadata: `${key.total_requests || 0} requests`
              }));
          }
        }
      } catch (error) {
        console.error('Error searching user API keys:', error);
      }

      // Add configuration and user key results to the main results array
      allResults.push(...configResults, ...userKeyResults);

      // Sort results by relevance (exact matches first, then partial matches)
      const sortedResults = allResults.sort((a, b) => {
        const aExact = a.title.toLowerCase() === searchQuery.toLowerCase();
        const bExact = b.title.toLowerCase() === searchQuery.toLowerCase();
        if (aExact && !bExact) return -1;
        if (!aExact && bExact) return 1;
        return 0;
      });

      setResults(sortedResults.slice(0, 10)); // Limit to 10 results
      setSelectedIndex(0);
    } catch (error) {
      console.error('Search error:', error);
      setResults([]);
    } finally {
      setLoading(false);
    }
  };

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      performSearch(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => Math.min(prev + 1, results.length - 1));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => Math.max(prev - 1, 0));
        break;
      case 'Enter':
        e.preventDefault();
        if (results[selectedIndex]) {
          handleResultClick(results[selectedIndex]);
        }
        break;
      case 'Escape':
        e.preventDefault();
        onClose();
        break;
    }
  };

  const handleResultClick = (result: SearchResult) => {
    router.push(result.href);
    onClose();
    setQuery('');
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50"
        onClick={onClose}
      />

      {/* Search Modal */}
      <div className="fixed top-20 left-1/2 transform -translate-x-1/2 w-full max-w-2xl mx-4 z-50">
        <div className="bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden">
          {/* Search Input */}
          <div className="flex items-center px-4 py-3 border-b border-gray-200">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 mr-3" />
            <input
              ref={inputRef}
              type="text"
              placeholder="Search configurations, API keys, pages..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              className="flex-1 text-gray-900 placeholder-gray-500 bg-transparent border-none outline-none text-sm"
            />
            <button
              onClick={onClose}
              className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <XMarkIcon className="h-4 w-4 text-gray-400" />
            </button>
          </div>

          {/* Results */}
          <div className="max-h-96 overflow-y-auto">
            {loading ? (
              <div className="px-4 py-8 text-center text-gray-500">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500 mx-auto"></div>
                <p className="mt-2 text-sm">Searching...</p>
              </div>
            ) : results.length > 0 ? (
              <div className="py-2">
                {results.map((result, index) => {
                  const Icon = result.icon;
                  return (
                    <button
                      key={result.id}
                      onClick={() => handleResultClick(result)}
                      className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors flex items-center space-x-3 ${
                        index === selectedIndex ? 'bg-orange-50 border-r-2 border-orange-500' : ''
                      }`}
                    >
                      <div className="flex-shrink-0">
                        <Icon className="h-5 w-5 text-gray-400" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {result.title}
                        </p>
                        <p className="text-xs text-gray-500 truncate">
                          {result.subtitle}
                        </p>
                      </div>
                      {result.metadata && (
                        <div className="flex-shrink-0">
                          <span className="text-xs text-gray-400">
                            {result.metadata}
                          </span>
                        </div>
                      )}
                    </button>
                  );
                })}
              </div>
            ) : query.trim() ? (
              <div className="px-4 py-8 text-center text-gray-500">
                <MagnifyingGlassIcon className="h-8 w-8 mx-auto text-gray-300 mb-2" />
                <p className="text-sm">No results found for "{query}"</p>
                <p className="text-xs text-gray-400 mt-1">
                  Try searching for pages like "dashboard", "playground", or "settings"
                </p>
              </div>
            ) : (
              <div className="px-4 py-8 text-center text-gray-500">
                <MagnifyingGlassIcon className="h-8 w-8 mx-auto text-gray-300 mb-2" />
                <p className="text-sm">Start typing to search...</p>
                <p className="text-xs text-gray-400 mt-1">
                  Search across configurations, API keys, and pages
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="px-4 py-2 bg-gray-50 border-t border-gray-200 text-xs text-gray-500 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span>↑↓ Navigate</span>
              <span>↵ Select</span>
              <span>Esc Close</span>
            </div>
            <span>{results.length} results</span>
          </div>
        </div>
      </div>
    </>
  );
}
