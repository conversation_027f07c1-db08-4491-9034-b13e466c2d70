'use client';

import { useState, useEffect, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  CreditCardIcon,
  CalendarIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EnvelopeIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { useSubscription } from '@/hooks/useSubscription';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import DebugSubscriptionStatus from '@/components/DebugSubscriptionStatus';
import DebugWebhookLogs from '@/components/DebugWebhookLogs';
import confetti from 'canvas-confetti';
import { useConfirmation } from '@/hooks/useConfirmation';
import emailjs from '@emailjs/browser';

interface PlanFeature {
  name: string;
  included: boolean;
  limit?: string;
}

interface Plan {
  id: string;
  name: string;
  price: number;
  interval: string;
  features: PlanFeature[];
  popular?: boolean;
}

const plans: Plan[] = [
  {
    id: 'free',
    name: 'Free',
    price: 0,
    interval: 'forever',
    features: [
      { name: 'Strict fallback routing only', included: true },
      { name: 'Basic analytics', included: true },
      { name: 'No custom roles', included: false },
      { name: 'Configurations', included: true, limit: '1 max' },
      { name: 'API keys per config', included: true, limit: '3 max' },
      { name: 'User-generated API keys', included: true, limit: '3 max' }
    ]
  },
  {
    id: 'starter',
    name: 'Starter',
    price: 19,
    interval: 'month',
    popular: true,
    features: [
      { name: 'All routing strategies', included: true },
      { name: 'Advanced analytics', included: true },
      { name: 'Custom roles', included: true, limit: 'Up to 3 roles' },
      { name: 'Configurations', included: true, limit: '5 max' },
      { name: 'API keys per config', included: true, limit: '15 max' },
      { name: 'User-generated API keys', included: true, limit: '50 max' },
      { name: 'Browsing tasks', included: true, limit: '15/month' }
    ]
  },
  {
    id: 'professional',
    name: 'Professional',
    price: 49,
    interval: 'month',
    features: [
      { name: 'Everything in Starter', included: true },
      { name: 'Unlimited configurations', included: true },
      { name: 'Unlimited API keys per config', included: true },
      { name: 'Unlimited user-generated API keys', included: true },
      { name: 'Knowledge base documents', included: true, limit: '5 documents' },
      { name: 'Priority support', included: true }
    ]
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: 149,
    interval: 'month',
    features: [
      { name: 'Everything in Professional', included: true },
      { name: 'Unlimited knowledge base documents', included: true },
      { name: 'Advanced semantic caching', included: true },
      { name: 'Custom integrations', included: true },
      { name: 'Dedicated support + phone', included: true },
      { name: 'SLA guarantee', included: true }
    ]
  }
];

export default function BillingPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, subscriptionStatus, refreshStatus: refreshSubscription, createCheckoutSession, openCustomerPortal } = useSubscription();
  const confirmation = useConfirmation();
  
  const [loading, setLoading] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [cancelFeedback, setCancelFeedback] = useState('');
  const [tierBeforePortal, setTierBeforePortal] = useState<string | null>(null);

  const currentPlan = plans.find(plan => plan.id === subscriptionStatus?.tier) || plans[0];

  // Calculate days until renewal based on actual subscription data
  const daysUntilRenewal = useMemo(() => {
    if (subscriptionStatus?.tier === 'free' || !subscriptionStatus?.currentPeriodEnd) {
      return null;
    }

    const renewalDate = new Date(subscriptionStatus.currentPeriodEnd);
    const today = new Date();
    const diffTime = renewalDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Return null if subscription has already expired
    return diffDays > 0 ? diffDays : null;
  }, [subscriptionStatus?.tier, subscriptionStatus?.currentPeriodEnd]);

  // Helper function to determine if a tier change is an upgrade
  const isUpgrade = (fromTier: string, toTier: string) => {
    const tierOrder = { 'free': 0, 'starter': 1, 'professional': 2, 'enterprise': 3 };
    return tierOrder[toTier as keyof typeof tierOrder] > tierOrder[fromTier as keyof typeof tierOrder];
  };

  // Handle return from Customer Portal
  useEffect(() => {
    const portalReturn = searchParams.get('portal_return');
    const previousTier = searchParams.get('prev_tier');

    if (portalReturn === 'true') {
      console.log('Returned from Customer Portal, previous tier:', previousTier);

      // Remove the parameters from URL
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('portal_return');
      newUrl.searchParams.delete('prev_tier');
      window.history.replaceState({}, '', newUrl.toString());

      // Wait for webhook to process, then refresh subscription status
      const refreshWithRetry = async () => {
        let attempts = 0;
        const maxAttempts = 8; // Increased attempts
        const initialDelay = 3000; // Longer initial delay for webhook processing
        const retryDelay = 2000; // 2 seconds between retry attempts

        console.log('Starting subscription refresh with retry logic...');

        // Initial delay to allow webhook processing
        await new Promise(resolve => setTimeout(resolve, initialDelay));

        while (attempts < maxAttempts) {
          try {
            console.log(`Refresh attempt ${attempts + 1}/${maxAttempts}`);

            // Force a complete refresh
            await refreshSubscription();

            // Wait a moment for the state to update
            await new Promise(resolve => setTimeout(resolve, 500));

            // Get fresh subscription status after refresh
            const response = await fetch(`/api/stripe/subscription-status?userId=${user?.id}&_t=${Date.now()}`, {
              cache: 'no-store',
              headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
              }
            });

            if (response.ok) {
              const freshStatus = await response.json();
              const currentTier = freshStatus.tier || 'free';

              console.log('Fresh tier from API:', currentTier, 'Previous tier:', previousTier);

              // Check if tier actually changed
              if (currentTier !== previousTier) {
                const wasUpgrade = isUpgrade(previousTier || 'free', currentTier);

                if (wasUpgrade) {
                  // Show upgrade success message with confetti
                  toast.success('Plan upgraded successfully!');

                  // Trigger confetti for upgrades only
                  const triggerConfetti = () => {
                    confetti({
                      particleCount: 100,
                      spread: 70,
                      origin: { y: 0.6 }
                    });
                  };

                  triggerConfetti();
                  setTimeout(triggerConfetti, 500);
                  setTimeout(triggerConfetti, 1000);
                } else {
                  // Show generic success message for downgrades/cancellations
                  toast.success('Billing settings updated successfully!');
                }

                console.log('Plan change detected and processed successfully');
                break;
              } else if (attempts >= 3) {
                // After a few attempts, show success message even if no change detected
                // (user might have just viewed the portal without making changes)
                toast.success('Billing settings updated successfully!');
                console.log('No plan change detected, but showing success message');
                break;
              }
            }

            attempts++;

            if (attempts < maxAttempts) {
              console.log(`No change detected yet, waiting ${retryDelay}ms before next attempt...`);
              await new Promise(resolve => setTimeout(resolve, retryDelay));
            }

          } catch (error) {
            console.error(`Refresh attempt ${attempts + 1} failed:`, error);
            attempts++;

            if (attempts >= maxAttempts) {
              console.log('Max refresh attempts reached, forcing page reload...');
              toast.error('Refreshing page to update subscription status...');
              // Force a page reload as last resort
              setTimeout(() => {
                window.location.reload();
              }, 2000);
            } else {
              await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
          }
        }
      };

      refreshWithRetry();
    }
  }, [searchParams, refreshSubscription, user?.id]); // Removed subscriptionStatus?.tier from dependencies

  const handleChangePlan = async () => {
    // For existing users (anyone who can access the billing page), always use Customer Portal
    // The billing page is only accessible to authenticated users with accounts
    try {
      setLoading(true);
      // Create return URL with current domain, portal return parameter, and current tier
      const currentTier = subscriptionStatus?.tier || 'free';
      const returnUrl = `${window.location.origin}/billing?portal_return=true&prev_tier=${currentTier}`;
      await openCustomerPortal(returnUrl);
    } catch (error: any) {
      console.error('Customer portal error:', error);

      // If portal is not configured, fall back to showing a helpful message
      if (error.message.includes('No configuration provided') || error.message.includes('default configuration has not been created')) {
        toast.error('Billing portal is being set up. Please contact support for plan changes.');
      } else {
        toast.error('Failed to open billing portal. Please try again.');
      }
      setLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!cancelReason.trim()) {
      toast.error('Please select a reason for cancellation');
      return;
    }

    setLoading(true);
    try {
      // Send cancellation feedback via EmailJS
      const templateParams = {
        user_email: user?.email || 'Unknown',
        user_name: user?.user_metadata?.first_name || 'User',
        current_plan: currentPlan.name,
        cancel_reason: cancelReason,
        additional_feedback: cancelFeedback,
        cancel_date: new Date().toLocaleDateString()
      };

      await emailjs.send(
        process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID!,
        process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID!,
        templateParams,
        process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY!
      );

      // Here you would also cancel the subscription in your payment processor
      // For now, we'll simulate the process
      await new Promise(resolve => setTimeout(resolve, 1500));

      toast.success('Subscription cancelled successfully. We\'ve sent your feedback to our team.');
      setShowCancelModal(false);
      setCancelReason('');
      setCancelFeedback('');
      await refreshSubscription();
    } catch (error: any) {
      console.error('Cancellation error:', error);
      toast.error('Failed to cancel subscription. Please contact support.');
    } finally {
      setLoading(false);
    }
  };

  const cancelReasons = [
    'Too expensive',
    'Not using enough features',
    'Found a better alternative',
    'Technical issues',
    'Poor customer support',
    'Missing features I need',
    'Temporary financial constraints',
    'Other'
  ];

  return (
    <div className="space-y-8">
      {/* Header with enhanced styling */}
      <div className="animate-slide-in">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              Billing & Plans 💳
            </h1>
            <p className="text-gray-600 text-lg">
              Manage your subscription, billing information, and plan features.
            </p>
          </div>
          <Button
            variant="outline"
            onClick={refreshSubscription}
            className="text-gray-600 border-gray-300 hover:bg-gray-50"
            size="sm"
          >
            🔄 Refresh Status
          </Button>
        </div>
      </div>

      {/* Current Plan Status - Enhanced Card */}
      <div className="card p-8 hover:shadow-lg transition-all duration-200 animate-slide-in">
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-orange-100 rounded-lg">
            <CreditCardIcon className="h-6 w-6 text-orange-600" />
          </div>
          <h2 className="text-2xl font-semibold text-gray-900">Current Plan</h2>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-4 mb-3">
              <h3 className="text-3xl font-bold text-gray-900">{currentPlan.name}</h3>
              {currentPlan.popular && (
                <Badge className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-3 py-1 text-sm font-semibold">
                  ⭐ Popular
                </Badge>
              )}
            </div>
            <div className="space-y-2">
              <p className="text-xl font-semibold text-gray-700">
                {currentPlan.price === 0 ? (
                  <span className="text-green-600">Free forever</span>
                ) : (
                  <span>${currentPlan.price}<span className="text-gray-500 font-normal">/{currentPlan.interval}</span></span>
                )}
              </p>
              {daysUntilRenewal && (
                <div className="flex items-center gap-2 text-sm text-gray-500 bg-gray-50 px-3 py-2 rounded-lg inline-flex">
                  <CalendarIcon className="h-4 w-4" />
                  <span>Renews in {daysUntilRenewal} days</span>
                </div>
              )}
            </div>
          </div>
          <div className="text-right">
            {subscriptionStatus?.tier !== 'free' && (
              <Button
                variant="outline"
                onClick={() => setShowCancelModal(true)}
                className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 transition-all duration-200"
              >
                Cancel Subscription
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Plan Management */}
      <div className="animate-slide-in" style={{ animationDelay: '200ms' }}>
        <div className="flex items-center gap-3 mb-8">
          <h2 className="text-3xl font-bold text-gray-900">Plan Management</h2>
          <div className="h-px bg-gradient-to-r from-orange-200 to-transparent flex-1 ml-4"></div>
        </div>

        {/* Single Change Plan Card */}
        <div className="max-w-2xl mx-auto">
          <div className="card relative overflow-hidden transition-all duration-300 hover:shadow-xl">
            <div className="p-8">
              {/* Current Plan Display */}
              <div className="text-center mb-8">
                <div className="inline-flex items-center gap-2 bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-semibold mb-4">
                  <CheckCircleIcon className="h-4 w-4" />
                  Current Plan
                </div>
                <h3 className="text-3xl font-bold text-gray-900 mb-2">{currentPlan.name}</h3>
                <div className="mb-4">
                  <span className="text-5xl font-bold text-gray-900">${currentPlan.price}</span>
                  {currentPlan.price > 0 && (
                    <span className="text-gray-500 text-xl">/{currentPlan.interval}</span>
                  )}
                </div>
                {currentPlan.price === 0 && (
                  <p className="text-green-600 font-semibold text-lg">Forever free</p>
                )}
              </div>

              {/* Plan Features */}
              <div className="mb-8">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Your Current Features:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {currentPlan.features.slice(0, 6).map((feature, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div className="flex-shrink-0">
                        {feature.included ? (
                          <div className="p-1 bg-green-100 rounded-full">
                            <CheckCircleIcon className="h-4 w-4 text-green-600" />
                          </div>
                        ) : (
                          <div className="p-1 bg-gray-100 rounded-full">
                            <XCircleIcon className="h-4 w-4 text-gray-400" />
                          </div>
                        )}
                      </div>
                      <span className={`text-sm ${
                        feature.included ? 'text-gray-900' : 'text-gray-400'
                      }`}>
                        {feature.name}
                        {feature.limit && (
                          <span className="text-gray-500 font-medium"> ({feature.limit})</span>
                        )}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-4">
                <Button
                  onClick={handleChangePlan}
                  disabled={loading}
                  className="w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                  size="lg"
                >
                  {subscriptionStatus?.tier === 'free' ? (
                    <>
                      <ArrowUpIcon className="h-5 w-5 mr-2" />
                      Choose a Paid Plan
                    </>
                  ) : (
                    <>
                      <CreditCardIcon className="h-5 w-5 mr-2" />
                      Change Plan or Cancel
                    </>
                  )}
                </Button>

                <p className="text-center text-sm text-gray-500">
                  {subscriptionStatus?.tier === 'free'
                    ? 'Upgrade to unlock more features and higher limits'
                    : 'Manage your subscription, change plans, or cancel anytime'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Information Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 animate-slide-in" style={{ animationDelay: '400ms' }}>
        {/* Billing Information */}
        <div className="card p-8">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-blue-100 rounded-lg">
              <CreditCardIcon className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900">Billing Information</h3>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center py-3 border-b border-gray-100">
              <span className="text-gray-600">Current Plan</span>
              <span className="font-semibold text-gray-900">{currentPlan.name}</span>
            </div>
            <div className="flex justify-between items-center py-3 border-b border-gray-100">
              <span className="text-gray-600">Billing Cycle</span>
              <span className="font-semibold text-gray-900">
                {currentPlan.price === 0 ? 'N/A' : `Monthly (${currentPlan.interval})`}
              </span>
            </div>
            {daysUntilRenewal && (
              <div className="flex justify-between items-center py-3 border-b border-gray-100">
                <span className="text-gray-600">Next Billing Date</span>
                <span className="font-semibold text-gray-900">
                  {new Date(Date.now() + daysUntilRenewal * 24 * 60 * 60 * 1000).toLocaleDateString()}
                </span>
              </div>
            )}
            <div className="flex justify-between items-center py-3">
              <span className="text-gray-600">Status</span>
              <Badge className={`${
                subscriptionStatus?.tier === 'free'
                  ? 'bg-gray-100 text-gray-800'
                  : 'bg-green-100 text-green-800'
              }`}>
                {subscriptionStatus?.tier === 'free' ? 'Free Plan' : 'Active'}
              </Badge>
            </div>
          </div>
        </div>

        {/* Support & Help */}
        <div className="card p-8">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-purple-100 rounded-lg">
              <ExclamationTriangleIcon className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900">Need Help?</h3>
          </div>
          <div className="space-y-4">
            <p className="text-gray-600">
              Have questions about your subscription or need assistance with billing?
            </p>
            <div className="space-y-3">
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() => window.open('/contact', '_blank')}
              >
                <EnvelopeIcon className="h-4 w-4 mr-2" />
                Contact Support
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Cancellation Modal */}
      {showCancelModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-2xl max-w-md w-full p-6">
            <div className="flex items-center gap-3 mb-4">
              <ExclamationTriangleIcon className="h-6 w-6 text-red-500" />
              <h3 className="text-lg font-semibold text-gray-900">Cancel Subscription</h3>
            </div>

            <p className="text-gray-600 mb-4">
              We're sorry to see you go! Please help us improve by telling us why you're cancelling.
            </p>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reason for cancellation *
                </label>
                <select
                  value={cancelReason}
                  onChange={(e) => setCancelReason(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                >
                  <option value="">Select a reason...</option>
                  {cancelReasons.map((reason) => (
                    <option key={reason} value={reason}>{reason}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional feedback (optional)
                </label>
                <textarea
                  value={cancelFeedback}
                  onChange={(e) => setCancelFeedback(e.target.value)}
                  placeholder="Tell us more about your experience or what we could do better..."
                  rows={3}
                  className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                />
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setShowCancelModal(false)}
                className="flex-1"
              >
                Keep Subscription
              </Button>
              <Button
                onClick={handleCancelSubscription}
                disabled={loading || !cancelReason.trim()}
                className="flex-1 bg-red-600 hover:bg-red-700"
              >
                {loading ? 'Cancelling...' : 'Cancel Subscription'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Debug Section - Only show in development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-8 space-y-6">
          <DebugSubscriptionStatus />
          <DebugWebhookLogs />
        </div>
      )}

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmation.isOpen}
        onClose={confirmation.hideConfirmation}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        type={confirmation.type}
        isLoading={confirmation.isLoading}
      />
    </div>
  );
}
