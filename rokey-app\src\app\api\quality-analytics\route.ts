import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';

// GET endpoint to retrieve quality analytics for a user
export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClientFromRequest(request);
    
    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const configId = searchParams.get('config_id');
    const days = parseInt(searchParams.get('days') || '30');

    if (!configId) {
      return NextResponse.json({ error: 'config_id parameter required' }, { status: 400 });
    }

    // Calculate date range
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get quality metrics for the specified period
    const { data: qualityMetrics, error: metricsError } = await supabase
      .from('routing_quality_metrics')
      .select('*')
      .eq('user_id', user.id)
      .eq('custom_api_config_id', configId)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: false });

    if (metricsError) {
      console.error('[Quality Analytics] Error fetching metrics:', metricsError);
      return NextResponse.json({ error: 'Failed to fetch analytics' }, { status: 500 });
    }

    // Get cost optimization profile
    const { data: costProfile } = await supabase
      .from('cost_optimization_profiles')
      .select('*')
      .eq('user_id', user.id)
      .eq('custom_api_config_id', configId)
      .single();

    // Get A/B test data
    const { data: abTest } = await supabase
      .from('ab_test_assignments')
      .select('*')
      .eq('user_id', user.id)
      .eq('custom_api_config_id', configId)
      .single();

    // Calculate analytics
    const analytics = calculateAnalytics(qualityMetrics || [], costProfile, abTest);

    return NextResponse.json({
      success: true,
      period: `${days} days`,
      totalRequests: qualityMetrics?.length || 0,
      analytics
    });

  } catch (error) {
    console.error('[Quality Analytics] Unexpected error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

function calculateAnalytics(metrics: any[], costProfile: any, abTest: any) {
  if (metrics.length === 0) {
    return {
      overview: {
        averageQuality: 0,
        totalCost: 0,
        costSavings: 0,
        learningPhase: costProfile?.learning_phase_completed === false
      },
      modelPerformance: [],
      routingStrategies: [],
      abTestResults: null,
      recommendations: []
    };
  }

  // Overall metrics
  const totalQuality = metrics.reduce((sum, m) => sum + (m.quality_score || 0), 0);
  const averageQuality = totalQuality / metrics.length;
  const totalCost = metrics.reduce((sum, m) => sum + (m.cost_usd || 0), 0);

  // Model performance analysis
  const modelStats = new Map();
  metrics.forEach(metric => {
    const key = `${metric.model_used}-${metric.provider}`;
    if (!modelStats.has(key)) {
      modelStats.set(key, {
        model: metric.model_used,
        provider: metric.provider,
        requests: 0,
        totalQuality: 0,
        totalCost: 0,
        userRatings: []
      });
    }
    const stats = modelStats.get(key);
    stats.requests += 1;
    stats.totalQuality += metric.quality_score || 0;
    stats.totalCost += metric.cost_usd || 0;
    if (metric.user_rating) stats.userRatings.push(metric.user_rating);
  });

  const modelPerformance = Array.from(modelStats.values()).map(stats => ({
    model: stats.model,
    provider: stats.provider,
    requests: stats.requests,
    averageQuality: stats.totalQuality / stats.requests,
    totalCost: stats.totalCost,
    costPerRequest: stats.totalCost / stats.requests,
    averageUserRating: stats.userRatings.length > 0
      ? stats.userRatings.reduce((a: number, b: number) => a + b, 0) / stats.userRatings.length
      : null,
    costEfficiency: stats.totalCost > 0 ? (stats.totalQuality / stats.requests) / (stats.totalCost / stats.requests) : 0
  })).sort((a, b) => b.costEfficiency - a.costEfficiency);

  // Routing strategy analysis
  const strategyStats = new Map();
  metrics.forEach(metric => {
    const strategy = metric.routing_strategy || 'unknown';
    if (!strategyStats.has(strategy)) {
      strategyStats.set(strategy, {
        strategy,
        requests: 0,
        totalQuality: 0,
        totalCost: 0
      });
    }
    const stats = strategyStats.get(strategy);
    stats.requests += 1;
    stats.totalQuality += metric.quality_score || 0;
    stats.totalCost += metric.cost_usd || 0;
  });

  const routingStrategies = Array.from(strategyStats.values()).map(stats => ({
    strategy: stats.strategy,
    requests: stats.requests,
    averageQuality: stats.totalQuality / stats.requests,
    totalCost: stats.totalCost,
    costPerRequest: stats.totalCost / stats.requests
  }));

  // A/B test results
  let abTestResults = null;
  if (abTest && abTest.test_requests > 0 && abTest.control_requests > 0) {
    abTestResults = {
      testRequests: abTest.test_requests,
      controlRequests: abTest.control_requests,
      testAverageQuality: abTest.test_avg_quality,
      controlAverageQuality: abTest.control_avg_quality,
      testAverageCost: abTest.test_avg_cost,
      controlAverageCost: abTest.control_avg_cost,
      qualityImprovement: abTest.test_avg_quality && abTest.control_avg_quality 
        ? ((abTest.test_avg_quality - abTest.control_avg_quality) / abTest.control_avg_quality) * 100
        : null,
      costChange: abTest.test_avg_cost && abTest.control_avg_cost
        ? ((abTest.test_avg_cost - abTest.control_avg_cost) / abTest.control_avg_cost) * 100
        : null
    };
  }

  // Generate recommendations
  const recommendations = generateRecommendations(modelPerformance, routingStrategies, abTestResults, costProfile);

  // Estimate cost savings (compared to always using most expensive model)
  const mostExpensiveModel = modelPerformance.reduce((max, model) => 
    model.costPerRequest > max.costPerRequest ? model : max, 
    modelPerformance[0] || { costPerRequest: 0 }
  );
  const costSavings = mostExpensiveModel.costPerRequest > 0 
    ? (mostExpensiveModel.costPerRequest * metrics.length) - totalCost
    : 0;

  return {
    overview: {
      averageQuality: Math.round(averageQuality * 100) / 100,
      totalCost: Math.round(totalCost * 100000) / 100000, // Round to 5 decimal places
      costSavings: Math.round(costSavings * 100000) / 100000,
      learningPhase: costProfile?.learning_phase_completed === false,
      learningProgress: costProfile ? `${costProfile.learning_phase_requests}/50` : '0/50'
    },
    modelPerformance,
    routingStrategies,
    abTestResults,
    recommendations
  };
}

function generateRecommendations(
  modelPerformance: any[], 
  routingStrategies: any[], 
  abTestResults: any, 
  costProfile: any
): string[] {
  const recommendations = [];

  // Learning phase recommendations
  if (costProfile?.learning_phase_completed === false) {
    recommendations.push(
      `You're in the learning phase (${costProfile.learning_phase_requests}/50 requests). Continue using the system to unlock smart cost optimization.`
    );
  }

  // Model performance recommendations
  if (modelPerformance.length > 1) {
    const bestModel = modelPerformance[0];
    const worstModel = modelPerformance[modelPerformance.length - 1];
    
    if (bestModel.costEfficiency > worstModel.costEfficiency * 2) {
      recommendations.push(
        `Consider using ${bestModel.model} more often - it has ${Math.round((bestModel.costEfficiency / worstModel.costEfficiency - 1) * 100)}% better cost efficiency than ${worstModel.model}.`
      );
    }
  }

  // A/B test recommendations
  if (abTestResults) {
    if (abTestResults.qualityImprovement > 10) {
      recommendations.push(
        `A/B testing shows ${Math.round(abTestResults.qualityImprovement)}% quality improvement. Consider adjusting your routing strategy.`
      );
    }
    if (abTestResults.costChange < -20) {
      recommendations.push(
        `A/B testing found models that are ${Math.round(Math.abs(abTestResults.costChange))}% cheaper with similar quality.`
      );
    }
  }

  // Strategy recommendations
  const costOptimizedStrategy = routingStrategies.find(s => s.strategy === 'cost_optimized');
  const defaultStrategy = routingStrategies.find(s => s.strategy === 'none' || s.strategy === 'unknown');
  
  if (costOptimizedStrategy && defaultStrategy && costOptimizedStrategy.costPerRequest < defaultStrategy.costPerRequest * 0.8) {
    recommendations.push(
      `Smart cost-optimized routing saves ${Math.round((1 - costOptimizedStrategy.costPerRequest / defaultStrategy.costPerRequest) * 100)}% on costs compared to default routing.`
    );
  }

  if (recommendations.length === 0) {
    recommendations.push('Your routing is performing well! Continue monitoring for optimization opportunities.');
  }

  return recommendations;
}
