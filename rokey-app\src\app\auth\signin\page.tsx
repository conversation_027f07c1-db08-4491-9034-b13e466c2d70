'use client';

import { useState, Suspense, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { EyeIcon, EyeSlashIcon, XMarkIcon, EnvelopeIcon } from '@heroicons/react/24/outline';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { useRouter, useSearchParams } from 'next/navigation';
import { useToast } from '@/components/ui/Toast';

function SignInPageContent() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Password reset modal state
  const [showResetModal, setShowResetModal] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [isResetLoading, setIsResetLoading] = useState(false);
  const [resetError, setResetError] = useState('');

  const router = useRouter();
  const searchParams = useSearchParams();
  const supabase = createSupabaseBrowserClient();
  const { success, error: toastError } = useToast();

  useEffect(() => {
    // TEMPORARILY DISABLED: Check if user is already signed in
    // This is causing redirect loops, so we'll let users manually fill out the form
    console.log('Signin page loaded, automatic session check disabled to prevent redirect loops');

    /*
    const checkUser = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        const redirectTo = searchParams.get('redirectTo');
        const plan = searchParams.get('plan');

        if (plan && ['starter', 'professional', 'enterprise'].includes(plan)) {
          router.push(`/pricing?plan=${plan}&checkout=true`);
        } else if (redirectTo) {
          router.push(redirectTo);
        } else {
          router.push('/dashboard');
        }
      }
    };
    checkUser();
    */
  }, [router, searchParams, supabase.auth]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      if (data.user) {
        console.log('Sign in successful, user:', data.user.id);
        console.log('Current URL:', window.location.href);
        console.log('Environment:', process.env.NODE_ENV);
        console.log('Site URL:', process.env.NEXT_PUBLIC_SITE_URL);

        // Wait a moment for session to be established
        await new Promise(resolve => setTimeout(resolve, 500));

        // Handle redirect logic - users should be able to access dashboard regardless of subscription
        const redirectTo = searchParams.get('redirectTo');
        const plan = searchParams.get('plan');
        const email = searchParams.get('email');
        const checkoutUserId = searchParams.get('checkout_user_id');

        console.log('Redirect params:', { redirectTo, plan, email, checkoutUserId });

        // Check actual subscription status in database first, then metadata
        try {
          const { data: profile } = await supabase
            .from('user_profiles')
            .select('subscription_tier, subscription_status')
            .eq('id', data.user.id)
            .single();

          // If user has active subscription in database, redirect to dashboard regardless of metadata
          if (profile && profile.subscription_status === 'active') {
            console.log('User has active subscription in database, redirecting to dashboard:', {
              userId: data.user.id,
              tier: profile.subscription_tier,
              status: profile.subscription_status
            });

            // Handle redirect logic for active users
            if (redirectTo) {
              router.push(redirectTo);
            } else {
              router.push('/dashboard');
            }
            return; // Exit early to prevent other redirects
          }

          // Only check metadata if no active subscription in database
          const userMetadata = data.user.user_metadata;
          const paymentStatus = userMetadata?.payment_status;
          const userPlan = userMetadata?.plan;

          if (paymentStatus === 'pending' && userPlan && ['starter', 'professional', 'enterprise'].includes(userPlan)) {
            console.log('User has pending payment status and no active subscription, redirecting to pricing for fresh start:', {
              userId: data.user.id,
              plan: userPlan,
              paymentStatus,
              dbProfile: profile
            });

            // Redirect to pricing page for fresh signup process
            console.log('Redirecting pending payment user to pricing page');
            router.push('/pricing');
            return; // Exit early to prevent other redirects
          }
        } catch (error) {
          console.error('Error checking user profile during sign-in:', error);
          // On error, continue with normal flow
        }

        // If this is specifically a checkout flow, redirect to checkout
        if (checkoutUserId && plan && ['starter', 'professional', 'enterprise'].includes(plan)) {
          const checkoutUrl = `/checkout?plan=${plan}&user_id=${data.user.id}${email ? `&email=${encodeURIComponent(email)}` : ''}`;
          console.log('Redirecting to checkout:', checkoutUrl);
          router.push(checkoutUrl);
        } else if (plan && ['starter', 'professional', 'enterprise'].includes(plan)) {
          // Check user's current subscription tier before redirecting to checkout
          // Free users should go to dashboard, not checkout, even if there's a plan parameter
          try {
            const { data: profile } = await supabase
              .from('user_profiles')
              .select('subscription_tier, subscription_status')
              .eq('id', data.user.id)
              .single();

            if (profile && profile.subscription_tier === 'free' && profile.subscription_status === 'active') {
              // Free user with plan parameter - redirect to dashboard instead of checkout
              console.log('Free user with plan parameter, redirecting to dashboard instead of checkout');
              router.push('/dashboard');
            } else {
              // Paid user or user without profile - redirect to checkout
              const checkoutUrl = `/checkout?plan=${plan}&user_id=${data.user.id}${email ? `&email=${encodeURIComponent(email)}` : ''}`;
              console.log('Redirecting to checkout for plan:', checkoutUrl);
              router.push(checkoutUrl);
            }
          } catch (error) {
            console.error('Error checking user profile for redirect:', error);
            // On error, default to dashboard to be safe
            router.push('/dashboard');
          }
        } else if (redirectTo) {
          // Redirect to specified location
          console.log('Redirecting to specified location:', redirectTo);
          router.push(redirectTo);
        } else {
          // Default redirect to dashboard - all users should be able to access dashboard
          console.log('Redirecting to dashboard');
          router.push('/dashboard');
        }
      }
    } catch (err: any) {
      console.error('Sign in error:', err);
      setError(err.message || 'Invalid email or password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsResetLoading(true);
    setResetError('');

    if (!resetEmail.trim()) {
      setResetError('Please enter your email address');
      setIsResetLoading(false);
      return;
    }

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(resetEmail, {
        redirectTo: `${window.location.origin}/auth/reset-password`
      });

      if (error) {
        throw error;
      }

      success('Password reset email sent!', 'Check your inbox for instructions to reset your password.');
      setShowResetModal(false);
      setResetEmail('');
    } catch (err: any) {
      console.error('Password reset error:', err);
      setResetError(err.message || 'Failed to send reset email. Please try again.');
      toastError('Failed to send reset email', err.message || 'Please try again.');
    } finally {
      setIsResetLoading(false);
    }
  };

  const openResetModal = () => {
    setResetEmail(email);
    setResetError('');
    setShowResetModal(true);
  };

  return (
    <div className="min-h-screen flex bg-gradient-to-br from-[#1C051C] to-[#040716]">
      {/* Left Side - Dark Welcome Section */}
      <div className="hidden lg:flex lg:w-1/2 items-center justify-center p-12 relative">
        {/* Subtle grid background with fade */}
        <div
          className="absolute inset-0 opacity-[0.15]"
          style={{
            backgroundImage: 'linear-gradient(rgba(255, 255, 255, 0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.3) 1px, transparent 1px)',
            backgroundSize: '40px 40px',
            maskImage: 'radial-gradient(ellipse at center, black 30%, transparent 80%)',
            WebkitMaskImage: 'radial-gradient(ellipse at center, black 30%, transparent 80%)'
          }}
        />
        <div className="max-w-lg relative z-10">
          {/* Logo */}
          <div className="mb-8">
            <Link href="/" className="inline-flex items-center space-x-3">
              <Image
                src="/RouKey_Logo_GLOW.png"
                alt="RouKey"
                width={48}
                height={48}
                className="w-12 h-12"
              />
              <span className="text-2xl font-bold text-white">RouKey</span>
            </Link>
          </div>

          {/* Welcome Content */}
          <div className="space-y-6">
            <h1 className="text-4xl font-bold text-white leading-tight">
              Welcome back!
            </h1>
            <p className="text-xl text-white/80 leading-relaxed">
              Sign in to your RouKey account and continue building with our powerful routing platform.
            </p>
            <div className="pt-4">
              <p className="text-white/60 text-sm">
                Don't have an account?{' '}
                <Link href="/auth/signup" className="text-white font-medium hover:text-white/80 transition-colors">
                  Sign up here
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Sign In Form */}
      <div className="flex-1 flex items-center justify-center p-8 bg-white">
        <div className="w-full max-w-md space-y-8">
          {/* Mobile Logo */}
          <div className="lg:hidden text-center">
            <Link href="/" className="inline-flex items-center space-x-2">
              <Image
                src="/RouKey_Logo_NOGLOW.png"
                alt="RouKey"
                width={32}
                height={32}
                className="w-8 h-8"
              />
              <span className="text-2xl font-bold text-gray-900">RouKey</span>
            </Link>
          </div>

          {/* Form Header */}
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900">Sign in</h2>
            <p className="mt-2 text-gray-600">Welcome back to RouKey</p>
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                placeholder="Enter your email"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-5 w-5" />
                  ) : (
                    <EyeIcon className="h-5 w-5" />
                  )}
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <button
                type="button"
                onClick={openResetModal}
                className="text-sm text-blue-600 hover:text-blue-500 font-medium transition-colors"
              >
                Forgot your password?
              </button>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Signing in...
                </div>
              ) : (
                'Sign in'
              )}
            </button>
          </form>

          {/* Mobile Sign Up Link */}
          <div className="lg:hidden text-center">
            <p className="text-gray-600 text-sm">
              Don't have an account?{' '}
              <Link href="/auth/signup" className="text-blue-600 hover:text-blue-500 font-medium transition-colors">
                Sign up
              </Link>
            </p>
          </div>
        </div>
      </div>

      {/* Password Reset Modal */}
      {showResetModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Reset Password</h3>
              <button
                onClick={() => setShowResetModal(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <form onSubmit={handlePasswordReset} className="space-y-4">
              <div>
                <label htmlFor="resetEmail" className="block text-sm font-medium text-gray-700 mb-2">
                  Email address
                </label>
                <input
                  id="resetEmail"
                  type="email"
                  required
                  value={resetEmail}
                  onChange={(e) => setResetEmail(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  placeholder="Enter your email"
                />
              </div>

              {resetError && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-600 text-sm">{resetError}</p>
                </div>
              )}

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setShowResetModal(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isResetLoading}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isResetLoading ? 'Sending...' : 'Send Reset Email'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default function SignInPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <SignInPageContent />
    </Suspense>
  );
}
