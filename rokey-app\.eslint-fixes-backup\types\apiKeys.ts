export type ApiKeyProvider = string;

// For creating a new API key (client to server)
export interface NewApiKey {
  custom_api_config_id: string; // New: Link to the parent custom configuration
  provider: ApiKeyProvider;
  predefined_model_id: string; // Renamed from model_id, refers to ID in `models` table
  api_key_raw: string; // The raw API key, will be encrypted on server
  label: string;
  temperature?: number; // Temperature setting (0.0 to 2.0), defaults to 1.0
  // user_id will be added on the server based on authenticated user (Milestone 13)
}

// Represents an API key as stored in the database (with encrypted key)
export interface ApiKey {
  id: string; // UUID
  custom_api_config_id: string; // FK to custom_api_configs
  user_id?: string; // UUID, FK to auth.users, will be enforced in M13
  provider: ApiKeyProvider;
  predefined_model_id: string; // FK to models table id
  encrypted_api_key: string; // Stores the combined iv:authTag:encryptedData string
  api_key_hash: string; // SHA-256 hash of the raw API key for quick checks
  label: string;
  status: 'active' | 'inactive' | 'revoked';
  is_default_general_chat_model: boolean;
  temperature: number; // Temperature setting (0.0 to 2.0)
  created_at: string; // ISO 8601 timestamp
  updated_at: string; // ISO 8601 timestamp
  last_used_at?: string | null; // ISO 8601 timestamp
}

// For displaying API keys on the client (omits sensitive data)
export interface DisplayApiKey {
  id: string;
  custom_api_config_id: string;
  provider: ApiKeyProvider;
  predefined_model_id: string; // Matches the field from DB
  label: string;
  status: 'active' | 'inactive' | 'revoked';
  temperature: number; // Temperature setting (0.0 to 2.0)
  created_at: string;
  last_used_at?: string | null;
  // user_id is not typically displayed directly but might be used for filtering if an admin view is ever created
}