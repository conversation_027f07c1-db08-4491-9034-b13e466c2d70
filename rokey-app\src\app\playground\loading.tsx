import { MessageSkeleton, ConfigSelectorSkeleton } from '@/components/LoadingSkeleton';

export default function PlaygroundLoading() {
  return (
    <div className="h-screen flex bg-[#040716]">
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-[#040716]/95 backdrop-blur-sm border-b border-gray-800/50 p-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="animate-pulse bg-gray-700 h-8 w-32 rounded"></div>
            <ConfigSelectorSkeleton />
          </div>
          <div className="flex items-center space-x-3">
            <div className="animate-pulse bg-gray-700 h-8 w-20 rounded"></div>
            <div className="animate-pulse bg-gray-700 h-8 w-8 rounded-full"></div>
          </div>
        </div>

        {/* Chat Area */}
        <div className="flex-1 flex">
          {/* Messages */}
          <div className="flex-1 overflow-hidden">
            <div className="h-full flex justify-center">
              <div className="w-full max-w-4xl px-6">
                <MessageSkeleton />
              </div>
            </div>
          </div>

          {/* History Sidebar */}
          <div className="w-80 bg-gray-900/50 border-l border-gray-800/50 flex flex-col">
            <div className="p-4 border-b border-gray-800/50">
              <div className="animate-pulse bg-gray-700 h-6 w-24 rounded mb-2"></div>
              <div className="animate-pulse bg-gray-700 h-8 w-full rounded"></div>
            </div>
            <div className="flex-1 p-4">
              <div className="space-y-3">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="p-3 rounded-xl border border-gray-700/50">
                    <div className="animate-pulse space-y-2">
                      <div className="bg-gray-700 h-4 w-3/4 rounded"></div>
                      <div className="bg-gray-700 h-3 w-1/2 rounded"></div>
                      <div className="flex justify-between">
                        <div className="bg-gray-700 h-3 w-16 rounded"></div>
                        <div className="bg-gray-700 h-3 w-12 rounded"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Input Area */}
        <div className="bg-white/95 backdrop-blur-sm border-t border-gray-200 p-4">
          <div className="max-w-4xl mx-auto">
            <div className="animate-pulse bg-gray-200 h-12 w-full rounded-xl"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
