import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Test basic connectivity to Supabase
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json({
        success: false,
        error: 'Missing Supabase environment variables',
        details: {
          hasUrl: !!supabaseUrl,
          hasKey: !!supabaseKey
        }
      });
    }

    // Test basic HTTP connectivity to Supabase
    const testUrl = `${supabaseUrl}/rest/v1/`;
    
    console.log('Testing Supabase connectivity to:', testUrl);
    
    const response = await fetch(testUrl, {
      method: 'GET',
      headers: {
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`,
        'Content-Type': 'application/json'
      }
    });

    const responseText = await response.text();
    
    return NextResponse.json({
      success: true,
      connectivity: {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        body: responseText.substring(0, 500) // Limit response size
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        supabaseUrl: supabaseUrl,
        hasKey: !!supabaseKey
      }
    });

  } catch (error: any) {
    console.error('Supabase connectivity test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Connectivity test failed',
      details: {
        message: error.message,
        code: error.code,
        cause: error.cause?.toString(),
        stack: error.stack?.split('\n').slice(0, 5) // First 5 lines of stack
      }
    });
  }
}
