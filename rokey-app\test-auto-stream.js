const API_KEY = 'rk_live_c5ae5a36_9Nl8iikIL2SDSNt6R4TujRKEg8xzUkK6';
const BASE_URL = 'https://roukey.online/api/external/v1/chat/completions';

console.log('🧪 Testing Auto-Stream for Multi-Role Tasks');
console.log('='.repeat(50));

async function testAutoStream() {
  // Test 1: Complex multi-role task that should trigger auto-streaming
  console.log('\n🧪 Test 1: Complex multi-role task (should auto-stream)');
  console.log('📤 Request: stream=false (should be forced to true)');
  
  try {
    const response = await fetch(BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: 'brainstorm an idea for a book and write a python code about it'
          }
        ],
        stream: false, // This should be forced to true
        max_tokens: 500
      })
    });

    console.log(`📊 Status: ${response.status}`);
    console.log(`📋 Content-Type: ${response.headers.get('content-type')}`);
    
    // Check for auto-stream headers
    const streamForced = response.headers.get('X-RouKey-Stream-Forced');
    const streamReason = response.headers.get('X-RouKey-Stream-Reason');
    const multiRoleDetected = response.headers.get('X-RouKey-Multi-Role-Detected');
    
    console.log(`🔄 Stream Forced: ${streamForced || 'No'}`);
    console.log(`💡 Stream Reason: ${streamReason || 'None'}`);
    console.log(`🎭 Multi-Role Detected: ${multiRoleDetected || 'No'}`);
    
    if (response.headers.get('content-type')?.includes('text/event-stream')) {
      console.log('✅ Successfully converted to streaming!');
      
      // Read the streaming response
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let fullResponse = '';
      
      console.log('📡 Streaming response:');
      console.log('-'.repeat(30));
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        fullResponse += chunk;
        process.stdout.write(chunk);
      }
      
      console.log('\n' + '-'.repeat(30));
      console.log(`📏 Total response length: ${fullResponse.length} characters`);
      
    } else {
      console.log('❌ Not converted to streaming');
      const responseText = await response.text();
      console.log('📥 Response:', responseText.substring(0, 500) + '...');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Test 2: Simple task that should NOT trigger auto-streaming
async function testNoAutoStream() {
  console.log('\n🧪 Test 2: Simple task (should NOT auto-stream)');
  console.log('📤 Request: stream=false (should remain false)');
  
  try {
    const response = await fetch(BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
      },
      body: JSON.stringify({
        messages: [
          {
            role: 'user',
            content: 'hi'
          }
        ],
        stream: false,
        max_tokens: 500
      })
    });

    console.log(`📊 Status: ${response.status}`);
    console.log(`📋 Content-Type: ${response.headers.get('content-type')}`);
    
    const streamForced = response.headers.get('X-RouKey-Stream-Forced');
    const multiRoleDetected = response.headers.get('X-RouKey-Multi-Role-Detected');
    
    console.log(`🔄 Stream Forced: ${streamForced || 'No'}`);
    console.log(`🎭 Multi-Role Detected: ${multiRoleDetected || 'No'}`);
    
    if (response.headers.get('content-type')?.includes('application/json')) {
      console.log('✅ Correctly remained as non-streaming');
      const responseData = await response.json();
      console.log(`📏 Response length: ${responseData.choices?.[0]?.message?.content?.length || 0} characters`);
      console.log(`🎭 Roles used: ${responseData.rokey_metadata?.roles_used?.join(', ') || 'none'}`);
    } else {
      console.log('⚠️ Unexpectedly converted to streaming');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

async function runTests() {
  await testAutoStream();
  await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
  await testNoAutoStream();
  
  console.log('\n' + '='.repeat(50));
  console.log('🏁 Auto-Stream Tests Complete');
}

runTests().catch(console.error);
