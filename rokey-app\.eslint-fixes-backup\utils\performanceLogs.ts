// Comprehensive performance logging utilities for terminal monitoring

import { messagingPerformanceTracker, firstTokenTracker } from './messagingPerformance';
import { PERFORMANCE_THRESHOLDS, evaluatePerformance } from './streamingUtils';

// Enhanced performance report with first token insights
export function logComprehensivePerformanceReport() {
  console.log('\n🚀 ===== COMPREHENSIVE PERFORMANCE REPORT =====');
  console.log('📊 Real-time messaging and streaming performance analysis\n');
  
  // Get messaging performance summary
  const messagingSummary = messagingPerformanceTracker.getSummary();
  
  console.log('📈 MESSAGING PERFORMANCE OVERVIEW:');
  console.log(`   Total Providers: ${messagingSummary.totalProviders}`);
  console.log(`   Total Models: ${messagingSummary.totalModels}`);
  console.log(`   Overall Average Time: ${messagingSummary.overallAverageTime}ms`);
  
  if (messagingSummary.fastestProvider) {
    console.log(`   🏆 Fastest Provider: ${messagingSummary.fastestProvider.provider} (${messagingSummary.fastestProvider.averageTotal}ms avg)`);
  }
  
  if (messagingSummary.slowestProvider) {
    console.log(`   🐌 Slowest Provider: ${messagingSummary.slowestProvider.provider} (${messagingSummary.slowestProvider.averageTotal}ms avg)`);
  }
  
  console.log('\n⚡ FIRST TOKEN PERFORMANCE TARGETS:');
  console.log(`   🎯 Excellent: < ${PERFORMANCE_THRESHOLDS.EXCELLENT_FIRST_TOKEN}ms`);
  console.log(`   ✅ Good: < ${PERFORMANCE_THRESHOLDS.GOOD_FIRST_TOKEN}ms`);
  console.log(`   ⚠️ Slow: < ${PERFORMANCE_THRESHOLDS.SLOW_FIRST_TOKEN}ms`);
  console.log(`   🚨 Very Slow: > ${PERFORMANCE_THRESHOLDS.SLOW_FIRST_TOKEN}ms`);
  
  console.log('\n💡 RECOMMENDATIONS:');
  messagingSummary.recommendations.forEach(rec => console.log(`   • ${rec}`));
  
  console.log('\n📋 HOW TO MONITOR FIRST TOKEN PERFORMANCE:');
  console.log('   1. Send a message in the playground with streaming enabled');
  console.log('   2. Watch the terminal for "🚀 FIRST TOKEN" logs');
  console.log('   3. Look for "📊 STREAMING COMPLETE" summaries');
  console.log('   4. Use logFirstTokenReport() for detailed analysis');
  
  console.log('\n🔧 PERFORMANCE MONITORING COMMANDS:');
  console.log('   • logComprehensivePerformanceReport() - This report');
  console.log('   • logFirstTokenReport() - First token specific analysis');
  console.log('   • logMessagingReport() - General messaging performance');
  console.log('   • firstTokenTracker.clear() - Clear tracking data');
  
  console.log('\n===============================================\n');
}

// First token specific performance analysis
export function logFirstTokenReport() {
  console.log('\n⚡ ===== FIRST TOKEN PERFORMANCE ANALYSIS =====');

  // Check if we have any active tracking
  const tracker = firstTokenTracker;
  const activeTrackings = (tracker as any).timingData?.size || 0;

  console.log(`📊 Active Trackings: ${activeTrackings}`);

  if (activeTrackings === 0) {
    console.log('\n💡 No active first token tracking sessions.');
    console.log('   Start a streaming conversation to see real-time metrics!');
  } else {
    console.log('\n🔄 Currently tracking streaming sessions...');
    console.log('   Watch for "🚀 FIRST TOKEN RECEIVED" logs in real-time');
  }

  console.log('\n📈 PERFORMANCE CATEGORIES:');
  console.log(`   ⚡ EXCELLENT: < ${PERFORMANCE_THRESHOLDS.EXCELLENT_FIRST_TOKEN}ms`);
  console.log(`   ✅ GOOD: ${PERFORMANCE_THRESHOLDS.EXCELLENT_FIRST_TOKEN}-${PERFORMANCE_THRESHOLDS.GOOD_FIRST_TOKEN}ms`);
  console.log(`   ⚠️ SLOW: ${PERFORMANCE_THRESHOLDS.GOOD_FIRST_TOKEN}-${PERFORMANCE_THRESHOLDS.SLOW_FIRST_TOKEN}ms`);
  console.log(`   🐌 VERY SLOW: > ${PERFORMANCE_THRESHOLDS.SLOW_FIRST_TOKEN}ms`);

  console.log('\n🎯 WHAT TO LOOK FOR:');
  console.log('   • Backend logs: "🚀 [PROVIDER] FIRST TOKEN: XXXms"');
  console.log('   • Frontend logs: "🚀 FRONTEND FIRST TOKEN: XXXms"');
  console.log('   • Completion logs: "📊 STREAMING COMPLETE"');
  console.log('   • Stream quality: "✅ STREAMING QUALITY" vs "⚠️ STREAMING ISSUE"');

  console.log('\n🔍 DEBUGGING TIPS:');
  console.log('   • Compare backend vs frontend first token times');
  console.log('   • Look for provider-specific performance patterns');
  console.log('   • Check chunk count: 1 chunk = buffering issue, many chunks = good streaming');
  console.log('   • Monitor "Raw chunk" logs to see if data arrives gradually');
  console.log('   • Watch for "STREAMING ISSUE" warnings indicating buffering problems');

  console.log('\n🚨 GOOGLE STREAMING TROUBLESHOOTING:');
  console.log('   • Google API does NOT truly stream - we implement artificial streaming');
  console.log('   • Look for "[Google Stream] Artificial chunk" logs in terminal');
  console.log('   • First token should show "(Artificial)" in the log');
  console.log('   • Compare first token timing: backend vs frontend');
  console.log('   • Expected behavior: Multiple artificial chunks with delays');
  console.log('   • If still seeing issues: Check chunk size and delay settings');

  console.log('\n===============================================\n');
}

// New function specifically for debugging Google streaming issues
export function logGoogleStreamingDebug() {
  console.log('\n🔍 ===== GOOGLE STREAMING DEBUG GUIDE =====');
  console.log('Use this guide to diagnose Google streaming issues\n');

  console.log('🎯 EXPECTED BEHAVIOR (ARTIFICIAL STREAMING):');
  console.log('   • Backend: "Starting artificial streaming transformation"');
  console.log('   • Backend: "🚀 GOOGLE FIRST TOKEN (Artificial): XXXms" with content preview');
  console.log('   • Backend: Multiple "[Google Stream] Artificial chunk X" logs with delays');
  console.log('   • Frontend: "🚀 FRONTEND FIRST TOKEN: XXXms" shortly after backend');
  console.log('   • Frontend: Multiple "[Frontend Stream] Chunk X" logs');
  console.log('   • Frontend: "✅ STREAMING QUALITY: Good chunk distribution"');

  console.log('\n🚨 PROBLEM INDICATORS:');
  console.log('   • Missing "artificial streaming transformation" log');
  console.log('   • No "Artificial chunk" logs in backend');
  console.log('   • Frontend: "⚠️ STREAMING ISSUE: Only 1 chunk received"');
  console.log('   • Frontend: "⚠️ STREAMING ISSUE: Very few chunks for long content"');
  console.log('   • First token NOT marked as "(Artificial)"');

  console.log('\n🔧 DEBUGGING STEPS:');
  console.log('   1. Send a message and watch terminal logs');
  console.log('   2. Look for "artificial streaming transformation" message');
  console.log('   3. Check for multiple "Artificial chunk" logs with delays');
  console.log('   4. Verify first token shows "(Artificial)" marker');
  console.log('   5. Compare chunk counts between backend and frontend');
  console.log('   6. Check for streaming quality assessment in frontend');

  console.log('\n💡 SOLUTIONS TO TRY:');
  console.log('   • Adjust CHUNK_SIZE (currently 15 chars) for different streaming speed');
  console.log('   • Modify CHUNK_DELAY (currently 50ms) for faster/slower streaming');
  console.log('   • Check if artificial streaming transformation is being called');
  console.log('   • Verify frontend is receiving multiple chunks progressively');

  console.log('\n===============================================\n');
}

// Real-time performance monitoring for development
export function startPerformanceMonitoring() {
  console.log('🔄 Starting real-time performance monitoring...');
  console.log('📊 This will log performance insights as they happen.\n');
  
  // Set up interval to show periodic updates
  const monitoringInterval = setInterval(() => {
    const summary = messagingPerformanceTracker.getSummary();
    if (summary.totalProviders > 0) {
      console.log(`📈 Performance Update: ${summary.totalProviders} providers, avg ${summary.overallAverageTime}ms`);
    }
  }, 30000); // Every 30 seconds
  
  console.log('✅ Performance monitoring active. Use stopPerformanceMonitoring() to stop.');
  
  // Store interval ID for cleanup
  (globalThis as any).__performanceMonitoringInterval = monitoringInterval;
}

export function stopPerformanceMonitoring() {
  const interval = (globalThis as any).__performanceMonitoringInterval;
  if (interval) {
    clearInterval(interval);
    delete (globalThis as any).__performanceMonitoringInterval;
    console.log('⏹️ Performance monitoring stopped.');
  } else {
    console.log('ℹ️ No active performance monitoring to stop.');
  }
}

// Quick performance check
export function quickPerformanceCheck() {
  console.log('\n⚡ QUICK PERFORMANCE CHECK');
  console.log('==========================');
  
  const summary = messagingPerformanceTracker.getSummary();
  
  if (summary.totalProviders === 0) {
    console.log('📊 No performance data available yet.');
    console.log('💡 Send some messages to generate performance metrics!');
    return;
  }
  
  console.log(`📈 Overall Average: ${summary.overallAverageTime}ms`);
  
  // Performance assessment
  if (summary.overallAverageTime < 2000) {
    console.log('✅ EXCELLENT overall performance!');
  } else if (summary.overallAverageTime < 5000) {
    console.log('👍 GOOD overall performance');
  } else if (summary.overallAverageTime < 10000) {
    console.log('⚠️ SLOW performance - needs optimization');
  } else {
    console.log('🚨 VERY SLOW performance - urgent optimization needed');
  }
  
  if (summary.fastestProvider) {
    console.log(`🏆 Best: ${summary.fastestProvider.provider} (${summary.fastestProvider.averageTotal}ms)`);
  }
  
  if (summary.slowestProvider) {
    console.log(`🐌 Worst: ${summary.slowestProvider.provider} (${summary.slowestProvider.averageTotal}ms)`);
  }
  
  console.log('==========================\n');
}

// Export all logging functions for easy terminal access
export const performanceLogs = {
  comprehensive: logComprehensivePerformanceReport,
  firstToken: logFirstTokenReport,
  googleDebug: logGoogleStreamingDebug,
  quick: quickPerformanceCheck,
  startMonitoring: startPerformanceMonitoring,
  stopMonitoring: stopPerformanceMonitoring,
};

// Make functions available globally for terminal access
if (typeof globalThis !== 'undefined') {
  (globalThis as any).logComprehensivePerformanceReport = logComprehensivePerformanceReport;
  (globalThis as any).logFirstTokenReport = logFirstTokenReport;
  (globalThis as any).logGoogleStreamingDebug = logGoogleStreamingDebug;
  (globalThis as any).quickPerformanceCheck = quickPerformanceCheck;
  (globalThis as any).startPerformanceMonitoring = startPerformanceMonitoring;
  (globalThis as any).stopPerformanceMonitoring = stopPerformanceMonitoring;
  (globalThis as any).performanceLogs = performanceLogs;
}
