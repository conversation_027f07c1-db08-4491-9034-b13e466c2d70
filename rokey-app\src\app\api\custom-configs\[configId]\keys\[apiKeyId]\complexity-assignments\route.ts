import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { z } from 'zod';

interface RouteParams {
  params: Promise<{
    configId: string;
    apiKeyId: string;
  }>;
}

const complexityLevelsSchema = z.object({
  complexity_levels: z.array(z.number().int().min(1).max(5)).min(0).max(5), // Allow empty array to remove all assignments
});

// Authentication helper
async function getAuthenticatedUser(supabase: any) {
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  if (userError || !user) {
    return null;
  }
  return user;
}

// Helper to verify config ownership (using placeholder user)
async function verifyConfigOwnership(supabase: any, configId: string, userId: string): Promise<boolean> {
  const { data: config, error } = await supabase
    .from('custom_api_configs')
    .select('user_id')
    .eq('id', configId)
    .single();

  if (error || !config) {
    return false;
  }
  return config.user_id === userId;
}


// GET /api/custom-configs/:configId/keys/:apiKeyId/complexity-assignments
// Fetches complexity levels assigned to a specific API key within a specific config.
export async function GET(request: NextRequest, { params }: RouteParams) {
  const supabase = await createSupabaseServerClientOnRequest();
  const { configId, apiKeyId } = await params;

  const user = await getAuthenticatedUser(supabase);
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized. Please log in.' }, { status: 401 });
  }

  const isOwner = await verifyConfigOwnership(supabase, configId, user.id);
  if (!isOwner) {
    return NextResponse.json({ error: 'Unauthorized or Configuration not found for this user.' }, { status: 403 });
  }

  if (!configId || !apiKeyId) {
    return NextResponse.json({ error: 'Config ID and API Key ID are required.' }, { status: 400 });
  }

  try {
    const { data, error } = await supabase
      .from('config_api_key_complexity_assignments')
      .select('complexity_level')
      .eq('custom_api_config_id', configId)
      .eq('api_key_id', apiKeyId);

    if (error) {
      console.error('Error fetching complexity assignments:', error);
      throw error;
    }

    const complexityLevels = data.map((item: { complexity_level: number }) => item.complexity_level);
    return NextResponse.json(complexityLevels, { status: 200 });

  } catch (error: any) {
    return NextResponse.json({ error: 'Failed to fetch complexity assignments.', details: error.message }, { status: 500 });
  }
}


// PUT /api/custom-configs/:configId/keys/:apiKeyId/complexity-assignments
// Updates/sets the list of complexity levels for an API key within a config.
export async function PUT(request: NextRequest, { params }: RouteParams) {
  const supabase = await createSupabaseServerClientOnRequest();
  const { configId, apiKeyId } = await params;

  const user = await getAuthenticatedUser(supabase);
  if (!user) {
    return NextResponse.json({ error: 'Unauthorized. Please log in.' }, { status: 401 });
  }

  const isOwner = await verifyConfigOwnership(supabase, configId, user.id);
  if (!isOwner) {
    return NextResponse.json({ error: 'Unauthorized or Configuration not found for this user.' }, { status: 403 });
  }

  if (!configId || !apiKeyId) {
    return NextResponse.json({ error: 'Config ID and API Key ID are required.' }, { status: 400 });
  }

  let requestBody;
  try {
    requestBody = await request.json();
  } catch (e) {
    return NextResponse.json({ error: 'Invalid JSON body.' }, { status: 400 });
  }

  const parsedBody = complexityLevelsSchema.safeParse(requestBody);
  if (!parsedBody.success) {
    return NextResponse.json({ error: 'Invalid request body.', issues: parsedBody.error.flatten() }, { status: 400 });
  }

  const { complexity_levels } = parsedBody.data;

  try {
    // Perform operations in a transaction
    const { error: transactionError } = await supabase.rpc('update_api_key_complexity_assignments', {
        p_config_id: configId,
        p_api_key_id: apiKeyId,
        p_complexity_levels: complexity_levels
    });
    
    if (transactionError) {
        console.error('Error in transaction for updating complexity assignments:', transactionError);
        throw transactionError;
    }

    return NextResponse.json({ message: 'Complexity assignments updated successfully.', assigned_levels: complexity_levels }, { status: 200 });

  } catch (error: any) {
    let errorMessage = 'Failed to update complexity assignments.';
    if (error.code === '23503') { // Foreign key violation
        errorMessage = 'Failed to update complexity assignments. Ensure the API key and Configuration ID are valid.';
    } else if (error.code === '23505') { // Unique constraint violation (should be handled by delete-then-insert logic in RPC)
        errorMessage = 'Failed to update complexity assignments due to a conflict. This might indicate a concurrent update.';
    }
    return NextResponse.json({ error: errorMessage, details: error.message }, { status: 500 });
  }
} 