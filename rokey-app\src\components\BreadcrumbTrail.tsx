'use client';

import React from 'react';
import Link from 'next/link';
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';
import { useBreadcrumb } from '@/hooks/useBreadcrumb';

interface BreadcrumbTrailProps {
  className?: string;
  showHome?: boolean;
  separator?: 'chevron' | 'slash';
}

export default function BreadcrumbTrail({ 
  className = '', 
  showHome = true,
  separator = 'chevron'
}: BreadcrumbTrailProps) {
  const { breadcrumbTrail } = useBreadcrumb();

  if (breadcrumbTrail.length === 0) {
    return null;
  }

  const SeparatorIcon = separator === 'chevron' ? ChevronRightIcon : null;

  return (
    <nav className={`flex items-center space-x-1 text-sm ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        {/* Home link */}
        {showHome && (
          <li>
            <Link 
              href="/dashboard" 
              className="text-gray-500 hover:text-gray-700 transition-colors duration-200"
            >
              <HomeIcon className="h-4 w-4" />
              <span className="sr-only">Home</span>
            </Link>
          </li>
        )}

        {/* Breadcrumb items */}
        {breadcrumbTrail.map((item, index) => (
          <li key={item.href || index} className="flex items-center">
            {/* Separator */}
            {(showHome || index > 0) && (
              <div className="mx-2 text-gray-400">
                {separator === 'chevron' ? (
                  <ChevronRightIcon className="h-4 w-4" />
                ) : (
                  <span>/</span>
                )}
              </div>
            )}

            {/* Breadcrumb link or text */}
            {item.isActive ? (
              <span className="text-gray-900 font-medium" aria-current="page">
                {item.title}
              </span>
            ) : (
              <Link
                href={item.href || '#'}
                className="text-gray-500 hover:text-gray-700 transition-colors duration-200"
              >
                {item.title}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}

// Compact breadcrumb for mobile
export function CompactBreadcrumb({ className = '' }: { className?: string }) {
  const { currentPage } = useBreadcrumb();

  return (
    <div className={`flex items-center ${className}`}>
      <div className="text-sm">
        <div className="font-medium text-gray-900">{currentPage.title}</div>
        <div className="text-xs text-gray-500">{currentPage.subtitle}</div>
      </div>
    </div>
  );
}

// Page header with breadcrumb and actions
export function PageHeader({ 
  children,
  className = '',
  showBreadcrumb = true 
}: { 
  children?: React.ReactNode;
  className?: string;
  showBreadcrumb?: boolean;
}) {
  const { currentPage } = useBreadcrumb();

  return (
    <div className={`mb-6 ${className}`}>
      {showBreadcrumb && (
        <BreadcrumbTrail className="mb-2" />
      )}
      
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{currentPage.title}</h1>
          <p className="text-gray-600 mt-1">{currentPage.subtitle}</p>
        </div>
        
        {children && (
          <div className="flex items-center space-x-3">
            {children}
          </div>
        )}
      </div>
    </div>
  );
}
