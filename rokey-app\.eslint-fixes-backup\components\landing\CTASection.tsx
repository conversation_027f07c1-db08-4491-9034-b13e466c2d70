'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRightIcon, CheckIcon } from '@heroicons/react/24/outline';
import InstantLink from '@/components/ui/InstantLink';

export default function CTASection() {
  return (
    <section className="py-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))]" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center">
          {/* Main CTA */}
          <motion.h2
            initial={{ opacity: 0, y: 15 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.3 }}
            className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6"
          >
            Ready to Transform
            <br />Your AI Infrastructure?
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 15 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.3, delay: 0.05 }}
            className="text-xl text-white/90 mb-8 max-w-3xl mx-auto"
          >
            Join thousands of developers enjoying unlimited access to 300+ AI models.
            No request limits, no overage fees - just intelligent routing.
          </motion.p>

          {/* Benefits List */}
          <motion.div
            initial={{ opacity: 0, y: 15 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="flex flex-wrap justify-center gap-6 mb-10"
          >
            {[
              "Unlimited API requests",
              "300+ AI models",
              "Setup in 5 minutes",
              "Cancel anytime"
            ].map((benefit, index) => (
              <div key={index} className="flex items-center text-white/90">
                <CheckIcon className="h-5 w-5 mr-2" />
                <span>{benefit}</span>
              </div>
            ))}
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 15 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.3, delay: 0.15 }}
            className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
          >
            <InstantLink
              href="/auth/signup"
              className="inline-flex items-center px-8 py-4 bg-white text-[#ff6b35] font-semibold rounded-xl hover:bg-gray-50 transition-all duration-100 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              Start Building Now
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </InstantLink>

            <InstantLink
              href="/docs"
              className="inline-flex items-center px-8 py-4 bg-transparent text-white font-semibold rounded-xl border-2 border-white/30 hover:border-white/50 hover:bg-white/10 transition-all duration-100"
            >
              View Documentation
            </InstantLink>
          </motion.div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 15 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.3, delay: 0.2 }}
            className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto"
          >
            <div className="text-center">
              <div className="text-3xl font-bold text-white mb-2">∞</div>
              <div className="text-white/80 text-sm">API Requests</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-white mb-2">300+</div>
              <div className="text-white/80 text-sm">AI Models</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-white mb-2">$0</div>
              <div className="text-white/80 text-sm">Overage Fees</div>
            </div>
          </motion.div>


        </div>
      </div>
    </section>
  );
}
