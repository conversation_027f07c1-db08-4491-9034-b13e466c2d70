'use client';

import { motion } from 'framer-motion';
import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';

const testimonials = [
  {
    name: "<PERSON>",
    role: "Lead Developer",
    company: "TechFlow Inc",
    username: "@sarahdev",
    avatar: "<PERSON>",
    image: "/sarah.jpg",
    content: "Honestly, <PERSON><PERSON><PERSON><PERSON> changed everything for us. We went from spending hours configuring AI requests to just... not worrying about it anymore. Our AI costs dropped 40% and I actually have time for coffee breaks now."
  },
  {
    name: "<PERSON>",
    role: "<PERSON><PERSON>",
    company: "DataVision Labs",
    username: "@marcustech",
    avatar: "<PERSON>",
    image: "/marcus.jpg",
    content: "I'll be real - I was skeptical at first. But <PERSON><PERSON><PERSON><PERSON>'s failover is like having a safety net you never knew you needed. No more 3AM calls about API limits. My team loves me again."
  },
  {
    name: "<PERSON>",
    role: "AI Engineer",
    company: "InnovateCorp",
    username: "@em<PERSON><PERSON>",
    avatar: "E<PERSON>",
    image: "/Emily.jpg",
    content: "300+ models through one API? I thought it was too good to be true. Turns out it's just really good. The analytics help me pick the right model every time. It's like having a crystal ball."
  },
  {
    name: "<PERSON>",
    role: "Product Manager",
    company: "StartupXYZ",
    username: "@davidpm",
    avatar: "DK",
    image: "/David.jpg",
    content: "RouKey feels like having an AI whisperer on the team. It just knows which model to use. Our output quality went through the roof and I look like a genius to my boss."
  },
  {
    name: "Lisa Thompson",
    role: "Engineering Manager",
    company: "ScaleUp Solutions",
    username: "@lisaeng",
    avatar: "LT",
    image: "/Lisa.jpg",
    content: "The analytics dashboard is my new best friend. Finally, data-driven decisions about AI spending that actually make sense. My CFO stopped asking uncomfortable questions."
  },
  {
    name: "Alex Johnson",
    role: "Senior Developer",
    company: "CloudTech Pro",
    username: "@alexcodes",
    avatar: "AJ",
    image: "/Alex.jpg",
    content: "Production-ready from day one. The security features give me peace of mind, and the team management is exactly what we needed. It just works, which is rare these days."
  },
  {
    name: "Maya Patel",
    role: "Startup Founder",
    company: "AI Innovations",
    username: "@mayabuilds",
    avatar: "MP",
    image: "https://images.unsplash.com/photo-1573497620166-aef748c8c792?fm=jpg&q=80&w=400&h=400&fit=crop&crop=face",
    content: "As a solo founder, RouKey is like having a whole AI infrastructure team. I can focus on building my product instead of wrestling with API configurations. Game changer."
  },
  {
    name: "James Wilson",
    role: "Tech Lead",
    company: "DevCorp",
    username: "@jameswilson",
    avatar: "JW",
    image: "https://images.unsplash.com/photo-*************-0a1dd7228f2d?fm=jpg&q=80&w=400&h=400&fit=crop&crop=face",
    content: "RouKey's intelligent routing saved our project. We were burning through our AI budget in weeks, now it lasts months. The automatic optimization is pure magic."
  }
];

// Testimonial Card Component with mouse tracking
function TestimonialCard({ testimonial, index }: { testimonial: any; index: number }) {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const cardRef = useRef<HTMLDivElement>(null);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) return;

    const rect = cardRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    setMousePosition({ x, y });
  };

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      className="relative flex-shrink-0 w-[400px] h-[280px] bg-gradient-to-br from-gray-900/90 to-gray-800/90 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50 group cursor-pointer overflow-hidden"
      onMouseMove={handleMouseMove}
      style={{
        background: `radial-gradient(600px circle at ${mousePosition.x}px ${mousePosition.y}px, rgba(255, 107, 53, 0.1), transparent 40%)`
      }}
    >
      {/* Hover glow outline */}
      <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-r from-pink-500/20 via-purple-500/20 to-orange-500/20 blur-sm -z-10" />
      <div className="absolute inset-[1px] rounded-2xl bg-gradient-to-br from-gray-900/95 to-gray-800/95 group-hover:from-gray-900/90 group-hover:to-gray-800/90 transition-all duration-300" />

      {/* Content */}
      <div className="relative z-10 h-full flex flex-col">
        {/* Quote */}
        <p className="text-gray-300 text-base leading-relaxed mb-6 flex-grow">
          {testimonial.content}
        </p>

        {/* Author */}
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full overflow-hidden mr-3 border border-gray-600">
            <Image
              src={testimonial.image}
              alt={`${testimonial.name} profile picture`}
              width={40}
              height={40}
              className="w-full h-full object-cover"
            />
          </div>
          <div>
            <div className="font-semibold text-white text-sm">{testimonial.name}</div>
            <div className="text-gray-400 text-xs">{testimonial.username}</div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

export default function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll every 4 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  // Smooth scroll to current testimonial
  useEffect(() => {
    if (containerRef.current) {
      const cardWidth = 400 + 24; // card width + gap
      containerRef.current.scrollTo({
        left: currentIndex * cardWidth,
        behavior: 'smooth'
      });
    }
  }, [currentIndex]);

  return (
    <section className="relative py-20 bg-[#040716] overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-[#1C051C] via-[#040716] to-[#1C051C]" />

      {/* Side fade effects */}
      <div className="absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-[#040716] to-transparent z-20 pointer-events-none" />
      <div className="absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-[#040716] to-transparent z-20 pointer-events-none" />

      {/* Scrolling container */}
      <div className="relative z-10">
        <div
          ref={containerRef}
          className="flex gap-6 px-6 overflow-x-hidden"
          style={{
            width: 'calc(100vw + 800px)',
            marginLeft: '-400px'
          }}
        >
          {/* Duplicate testimonials for seamless loop */}
          {[...testimonials, ...testimonials].map((testimonial, index) => (
            <TestimonialCard
              key={`${testimonial.name}-${index}`}
              testimonial={testimonial}
              index={index}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
