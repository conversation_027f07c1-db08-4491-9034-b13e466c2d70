'use client';

import React, { createContext, useContext, useState, useEffect, useRef, useCallback } from 'react';
import { usePathname, useRouter } from 'next/navigation';

interface NavigationContextType {
  isNavigating: boolean;
  targetRoute: string | null;
  navigateOptimistically: (href: string) => void;
  clearNavigation: () => void;
  isPageCached: (route: string) => boolean;
  navigationHistory: string[];
}

interface NavigationQueueItem {
  route: string;
  timestamp: number;
  id: string;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

export function NavigationProvider({ children }: { children: React.ReactNode }) {
  const [isNavigating, setIsNavigating] = useState(false);
  const [targetRoute, setTargetRoute] = useState<string | null>(null);
  const [navigationHistory, setNavigationHistory] = useState<string[]>([]);
  const [cachedPages, setCachedPages] = useState<Set<string>>(new Set());
  const [isClient, setIsClient] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const navigationQueueRef = useRef<NavigationQueueItem[]>([]);
  const currentNavigationIdRef = useRef<string | null>(null);
  const lastNavigationTimeRef = useRef<number>(0);
  const clickCountRef = useRef<{ [key: string]: number }>({});
  const clickTimeoutRef = useRef<{ [key: string]: NodeJS.Timeout }>({});

  // Handle client-side hydration
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Safe console logging that only runs on client
  const safeLog = useCallback((message: string) => {
    if (isClient && typeof window !== 'undefined') {
      console.log(message);
    }
  }, [isClient]);

  // Track navigation history and cache pages
  useEffect(() => {
    if (pathname && !navigationHistory.includes(pathname)) {
      setNavigationHistory(prev => [...prev, pathname]);
      setCachedPages(prev => new Set([...prev, pathname]));
    }
  }, [pathname, navigationHistory]);

  // Clear navigation state when route actually changes
  useEffect(() => {
    safeLog(`🔍 [OPTIMISTIC NAV] Route check: target=${targetRoute}, current=${pathname}, navigationId=${currentNavigationIdRef.current}`);

    if (targetRoute && currentNavigationIdRef.current) {
      // Exact route matching only - be more precise
      const isRouteMatch = pathname === targetRoute;

      if (isRouteMatch) {
        safeLog(`✅ [OPTIMISTIC NAV] Navigation completed: ${targetRoute} -> ${pathname}`);

        // Clear timeout if navigation completed successfully
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
          timeoutRef.current = null;
        }

        // Clear navigation state immediately
        setIsNavigating(false);
        setTargetRoute(null);
        currentNavigationIdRef.current = null;

        // Clear any remaining queued navigations for this route
        navigationQueueRef.current = navigationQueueRef.current.filter(
          item => item.route !== targetRoute
        );
      }
    }
  }, [pathname, targetRoute, safeLog]);

  // Additional immediate route change detection
  useEffect(() => {
    // If we're navigating but the pathname has already changed to match target
    if (isNavigating && targetRoute && pathname === targetRoute) {
      safeLog(`🚀 [OPTIMISTIC NAV] Immediate route match detected, clearing navigation state`);
      setIsNavigating(false);
      setTargetRoute(null);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    }
  }, [pathname, targetRoute, isNavigating, safeLog]);

  // Check if page is cached (visited before)
  const isPageCached = useCallback((route: string) => {
    return cachedPages.has(route);
  }, [cachedPages]);

  // Process navigation queue
  const processNavigationQueue = useCallback(() => {
    if (navigationQueueRef.current.length === 0) return;

    // Get the most recent navigation request
    const latestNavigation = navigationQueueRef.current[navigationQueueRef.current.length - 1];

    // Clear all older navigation requests
    navigationQueueRef.current = [latestNavigation];

    const { route, id } = latestNavigation;

    safeLog(`🚀 [OPTIMISTIC NAV] Processing navigation to: ${route} (id: ${id})`);

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    // Update navigation ID and confirm target route
    currentNavigationIdRef.current = id;
    // Target route should already be set by navigateOptimistically

    // For cached pages, show minimal loading
    const isCached = isPageCached(route);
    if (isCached) {
      safeLog(`⚡ [OPTIMISTIC NAV] Using cached navigation for: ${route}`);
      // Clear quickly for cached pages
      setTimeout(() => {
        if (currentNavigationIdRef.current === id) {
          setIsNavigating(false);
        }
      }, 100);
    }

    // Start actual navigation immediately
    try {
      router.push(route);
    } catch (error) {
      safeLog(`❌ [OPTIMISTIC NAV] Router.push failed for: ${route}, using fallback`);
      // Fallback to window.location if router fails
      window.location.href = route;
      return;
    }

    // Set timeout for fallback (shorter for cached pages, but more generous)
    const timeoutDuration = isCached ? 800 : 3000;
    timeoutRef.current = setTimeout(() => {
      safeLog(`⚠️ [OPTIMISTIC NAV] Timeout reached for: ${route} (id: ${id}), current path: ${pathname}`);
      if (currentNavigationIdRef.current === id) {
        // Try fallback navigation
        safeLog(`🔄 [OPTIMISTIC NAV] Attempting fallback navigation to: ${route}`);
        try {
          window.location.href = route;
        } catch (fallbackError) {
          safeLog(`❌ [OPTIMISTIC NAV] Fallback navigation failed: ${fallbackError}`);
        }
        setIsNavigating(false);
        setTargetRoute(null);
        currentNavigationIdRef.current = null;
      }
      timeoutRef.current = null;
    }, timeoutDuration);
  }, [router, pathname, isPageCached, safeLog]);

  const navigateOptimistically = useCallback((href: string) => {
    // Don't navigate if we're already on this exact route
    if (pathname === href || !isClient) {
      return;
    }

    const now = Date.now();

    // Simple debounce to prevent rapid clicks
    if (now - lastNavigationTimeRef.current < 100 && targetRoute === href) {
      safeLog(`🔄 [OPTIMISTIC NAV] Debouncing duplicate navigation to: ${href}`);
      return;
    }

    lastNavigationTimeRef.current = now;

    // Track click count for escape hatch
    if (!clickCountRef.current[href]) {
      clickCountRef.current[href] = 0;
    }
    clickCountRef.current[href]++;

    // Clear click count after 2 seconds
    if (clickTimeoutRef.current[href]) {
      clearTimeout(clickTimeoutRef.current[href]);
    }
    clickTimeoutRef.current[href] = setTimeout(() => {
      clickCountRef.current[href] = 0;
    }, 2000);

    // If user clicks same route 3+ times quickly, force regular navigation
    if (clickCountRef.current[href] >= 3) {
      safeLog(`🚨 [OPTIMISTIC NAV] Force navigation escape hatch for: ${href}`);
      clickCountRef.current[href] = 0;
      if (typeof window !== 'undefined') {
        window.location.href = href;
      }
      return;
    }

    // Clear any existing navigation state first
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    // IMMEDIATE visual feedback - set loading state right away
    setIsNavigating(true);
    setTargetRoute(href);

    // Generate unique ID for this navigation
    const navigationId = `nav_${now}_${Math.random().toString(36).substr(2, 9)}`;

    // Clear queue and add new navigation
    navigationQueueRef.current = [{
      route: href,
      timestamp: now,
      id: navigationId
    }];

    // Process queue immediately for instant feedback
    processNavigationQueue();
  }, [pathname, targetRoute, processNavigationQueue, safeLog, isClient]);

  const clearNavigation = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsNavigating(false);
    setTargetRoute(null);
    currentNavigationIdRef.current = null;
    navigationQueueRef.current = [];
  }, []);

  // Additional safety: clear navigation state when document becomes visible
  // This handles cases where the route change detection might miss
  useEffect(() => {
    // Only add event listener on client side
    if (!isClient || typeof window === 'undefined') return;

    const handleVisibilityChange = () => {
      if (!document.hidden && isNavigating) {
        safeLog(`👁️ [OPTIMISTIC NAV] Document visible, checking if navigation should clear`);
        // Small delay to allow route to update
        setTimeout(() => {
          if (targetRoute) {
            const isRouteMatch = pathname === targetRoute;

            if (isRouteMatch) {
              safeLog(`🔧 [OPTIMISTIC NAV] Force clearing navigation state`);
              setIsNavigating(false);
              setTargetRoute(null);
              if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
                timeoutRef.current = null;
              }
            }
          }
        }, 100);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [isNavigating, targetRoute, pathname, safeLog, isClient]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <NavigationContext.Provider value={{
      isNavigating,
      targetRoute,
      navigateOptimistically,
      clearNavigation,
      isPageCached,
      navigationHistory
    }}>
      {children}
    </NavigationContext.Provider>
  );
}

export function useNavigation() {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
}

// Safe version that returns null instead of throwing
export function useNavigationSafe() {
  const context = useContext(NavigationContext);
  return context || null;
}
