-- Migration: Add temperature control and chat history support
-- Run this in Supabase SQL Editor

-- 1. Add temperature column to api_keys table
ALTER TABLE public.api_keys 
ADD COLUMN IF NOT EXISTS temperature NUMERIC(3,2) DEFAULT 1.0 CHECK (temperature >= 0.0 AND temperature <= 2.0);

COMMENT ON COLUMN public.api_keys.temperature IS 'Temperature setting for this API key (0.0 to 2.0). Controls randomness in model responses.';

-- 2. Create chat_conversations table for persistent chat history
CREATE TABLE IF NOT EXISTS public.chat_conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID, -- FK to auth.users.id (will be enforced later)
    custom_api_config_id UUID NOT NULL,
    title TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    CONSTRAINT fk_custom_api_config FOREIGN KEY (custom_api_config_id) REFERENCES public.custom_api_configs(id) ON DELETE CASCADE
    -- CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE -- Add when auth is integrated
);

CREATE INDEX idx_chat_conversations_user_id ON public.chat_conversations(user_id);
CREATE INDEX idx_chat_conversations_custom_api_config_id ON public.chat_conversations(custom_api_config_id);
CREATE INDEX idx_chat_conversations_updated_at ON public.chat_conversations(updated_at DESC);

COMMENT ON TABLE public.chat_conversations IS 'Stores chat conversation metadata for the playground';

-- 3. Create chat_messages table for storing individual messages
CREATE TABLE IF NOT EXISTS public.chat_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system', 'error')),
    content JSONB NOT NULL, -- Stores message content parts (text, images, etc.)
    api_key_id UUID, -- Which API key was used for this message (for assistant messages)
    model_used TEXT, -- Which model was actually used
    temperature_used NUMERIC(3,2), -- Temperature setting used for this message
    tokens_prompt INTEGER,
    tokens_completion INTEGER,
    cost NUMERIC,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    CONSTRAINT fk_conversation FOREIGN KEY (conversation_id) REFERENCES public.chat_conversations(id) ON DELETE CASCADE,
    CONSTRAINT fk_api_key FOREIGN KEY (api_key_id) REFERENCES public.api_keys(id) ON DELETE SET NULL
);

CREATE INDEX idx_chat_messages_conversation_id ON public.chat_messages(conversation_id);
CREATE INDEX idx_chat_messages_created_at ON public.chat_messages(created_at);

COMMENT ON TABLE public.chat_messages IS 'Stores individual messages within chat conversations';
COMMENT ON COLUMN public.chat_messages.content IS 'JSONB array of message content parts (text, images, etc.)';

-- 4. Create training_jobs table for fine-tuning functionality
CREATE TABLE IF NOT EXISTS public.training_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID, -- FK to auth.users.id
    custom_api_config_id UUID NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    training_data JSONB, -- Stores training prompts and responses
    knowledge_base_files JSONB, -- Metadata about uploaded files
    parameters JSONB, -- Training parameters (learning rate, epochs, etc.)
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    error_message TEXT,
    fine_tuned_model_id TEXT, -- Reference to the resulting fine-tuned model
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    CONSTRAINT fk_custom_api_config FOREIGN KEY (custom_api_config_id) REFERENCES public.custom_api_configs(id) ON DELETE CASCADE
    -- CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE -- Add when auth is integrated
);

CREATE INDEX idx_training_jobs_user_id ON public.training_jobs(user_id);
CREATE INDEX idx_training_jobs_custom_api_config_id ON public.training_jobs(custom_api_config_id);
CREATE INDEX idx_training_jobs_status ON public.training_jobs(status);
CREATE INDEX idx_training_jobs_created_at ON public.training_jobs(created_at DESC);

COMMENT ON TABLE public.training_jobs IS 'Stores fine-tuning/training job information';

-- 5. Create training_files table for uploaded knowledge base files
CREATE TABLE IF NOT EXISTS public.training_files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    training_job_id UUID NOT NULL,
    original_filename TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    file_type TEXT NOT NULL,
    storage_path TEXT NOT NULL, -- Path in storage system
    processing_status TEXT DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
    extracted_content TEXT, -- Processed text content
    metadata JSONB, -- File metadata (page count, word count, etc.)
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    CONSTRAINT fk_training_job FOREIGN KEY (training_job_id) REFERENCES public.training_jobs(id) ON DELETE CASCADE
);

CREATE INDEX idx_training_files_training_job_id ON public.training_files(training_job_id);
CREATE INDEX idx_training_files_processing_status ON public.training_files(processing_status);

COMMENT ON TABLE public.training_files IS 'Stores uploaded files for training jobs';

-- 6. Add triggers for updated_at timestamps
CREATE TRIGGER handle_updated_at_chat_conversations 
BEFORE UPDATE ON public.chat_conversations 
FOR EACH ROW EXECUTE FUNCTION public.moddatetime();

CREATE TRIGGER handle_updated_at_training_jobs 
BEFORE UPDATE ON public.training_jobs 
FOR EACH ROW EXECUTE FUNCTION public.moddatetime();

CREATE TRIGGER handle_updated_at_training_files 
BEFORE UPDATE ON public.training_files 
FOR EACH ROW EXECUTE FUNCTION public.moddatetime();

-- 7. Add RLS policies (placeholder for when auth is implemented)
-- ALTER TABLE public.chat_conversations ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE public.chat_messages ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE public.training_jobs ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE public.training_files ENABLE ROW LEVEL SECURITY;

SELECT 'Temperature control and chat history schema created successfully. Please review and execute.' as result;
