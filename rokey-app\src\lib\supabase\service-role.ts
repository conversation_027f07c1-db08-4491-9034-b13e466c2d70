import { createClient } from '@supabase/supabase-js';

/**
 * Create service role client for server-side operations that need to bypass RLS
 * This client has full database access and should only be used for:
 * - Internal API operations
 * - Admin operations
 * - System-level operations
 * - External API key authentication
 */
export function createServiceRoleClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}
