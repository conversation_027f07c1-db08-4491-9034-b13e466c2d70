'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useState, useEffect } from 'react';
import {
  BoltIcon,
  CodeBracketIcon,
  PencilIcon,
  ChartBarIcon,
  CpuChipIcon,
  ArrowRightIcon,
  ShieldCheckIcon,
  EyeIcon,
  CogIcon,
  CloudIcon,
  CheckIcon,
  SparklesIcon,
  BeakerIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import EnhancedGridBackground from './EnhancedGridBackground';

const routingExamples = [
  {
    id: 1,
    prompt: "Solve this complex math problem: 2x + 5 = 15",
    role: "logic_reasoning",
    roleName: "Logic & Reasoning",
    model: "GPT-4o",
    provider: "OpenAI",
    icon: BoltIcon,
    color: "text-[#ff6b35]",
    bgColor: "bg-[#ff6b35]/10",
    borderColor: "border-[#ff6b35]/20",
    glowColor: "shadow-[#ff6b35]/50"
  },
  {
    id: 2,
    prompt: "Write a blog post about AI trends",
    role: "writing",
    roleName: "Writing & Content Creation",
    model: "GPT-4o",
    provider: "OpenAI",
    icon: PencilIcon,
    color: "text-[#ff6b35]",
    bgColor: "bg-[#ff6b35]/10",
    borderColor: "border-[#ff6b35]/20",
    glowColor: "shadow-[#ff6b35]/50"
  },
  {
    id: 3,
    prompt: "Build a React component with TypeScript",
    role: "coding_frontend",
    roleName: "Frontend Development",
    model: "Claude 4 Opus",
    provider: "Anthropic",
    icon: CodeBracketIcon,
    color: "text-[#ff6b35]",
    bgColor: "bg-[#ff6b35]/10",
    borderColor: "border-[#ff6b35]/20",
    glowColor: "shadow-[#ff6b35]/50"
  },
  {
    id: 4,
    prompt: "Summarize this research paper",
    role: "research_synthesis",
    roleName: "Research & Analysis",
    model: "DeepSeek R1 0528",
    provider: "DeepSeek",
    icon: ChartBarIcon,
    color: "text-[#ff6b35]",
    bgColor: "bg-[#ff6b35]/10",
    borderColor: "border-[#ff6b35]/20",
    glowColor: "shadow-[#ff6b35]/50"
  }
];

export default function RoutingVisualization() {
  const [activeExample, setActiveExample] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setIsAnimating(true);
      setTimeout(() => {
        setActiveExample((prev) => (prev + 1) % routingExamples.length);
        setIsAnimating(false);
      }, 200);
    }, 4000); // Slightly slower for better readability

    return () => clearInterval(interval);
  }, []);

  const currentExample = routingExamples[activeExample];

  return (
    <section className="relative py-24 overflow-hidden">
      {/* Background with same gradient as main page */}
      <div
        className="absolute inset-0"
        style={{
          background: `radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)`
        }}
      />

      {/* Custom CSS for enhanced animations */}
      <style jsx>{`
        @keyframes flowCurrent {
          0% {
            transform: translateX(-100%);
            opacity: 0;
          }
          20% {
            opacity: 1;
          }
          80% {
            opacity: 1;
          }
          100% {
            transform: translateX(200%);
            opacity: 0;
          }
        }
        @keyframes pulse-glow {
          0%, 100% {
            box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
          }
          50% {
            box-shadow: 0 0 40px rgba(255, 107, 53, 0.6);
          }
        }
        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }
        @keyframes glossy-highlight {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }
        .current-flow {
          animation: flowCurrent 3s ease-in-out infinite;
        }
        .current-flow-delayed {
          animation: flowCurrent 3s ease-in-out infinite;
          animation-delay: 1.5s;
        }
        .pulse-glow {
          animation: pulse-glow 2s ease-in-out infinite;
        }
        .float-animation {
          animation: float 6s ease-in-out infinite;
        }
        .glossy-card {
          position: relative;
          overflow: hidden;
        }
        .glossy-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2) 50%,
            transparent
          );
          background-size: 200% 100%;
          opacity: 0;
          transition: opacity 0.3s ease;
          pointer-events: none;
          z-index: 10;
        }
        .glossy-card:hover::before {
          opacity: 1;
          animation: glossy-highlight 1.5s ease-in-out;
        }
      `}</style>

      {/* AI Gateway Circuit Board Pattern */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 opacity-20">
          <div
            style={{
              backgroundImage: `
                linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)
              `,
              backgroundSize: '50px 50px'
            }}
          />
        </div>

        {/* Circuit Board Grid Pattern */}
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 107, 53, 0.25) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 107, 53, 0.25) 1px, transparent 1px),
              linear-gradient(rgba(0, 0, 0, 0.15) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 0, 0, 0.15) 1px, transparent 1px),
              radial-gradient(circle at 25% 25%, rgba(255, 107, 53, 0.4) 2px, transparent 2px),
              radial-gradient(circle at 75% 75%, rgba(0, 0, 0, 0.3) 1px, transparent 1px)
            `,
            backgroundSize: '80px 80px, 80px 80px, 40px 40px, 40px 40px, 160px 160px, 120px 120px',
            backgroundPosition: '0 0, 0 0, 20px 20px, 20px 20px, 0 0, 60px 60px',
            mask: `
              radial-gradient(ellipse 90% 90% at center, black 20%, transparent 85%),
              linear-gradient(to right, transparent 5%, black 10%, black 90%, transparent 95%),
              linear-gradient(to bottom, transparent 5%, black 10%, black 90%, transparent 95%)
            `,
            maskComposite: 'intersect',
            WebkitMask: `
              radial-gradient(ellipse 90% 90% at center, black 20%, transparent 85%),
              linear-gradient(to right, transparent 5%, black 10%, black 90%, transparent 95%),
              linear-gradient(to bottom, transparent 5%, black 10%, black 90%, transparent 95%)
            `,
            WebkitMaskComposite: 'source-in'
          }}
        ></div>

        {/* Floating Gateway Nodes */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-[#ff6b35]/8 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-[#f7931e]/8 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 right-1/3 w-64 h-64 bg-[#ff6b35]/6 rounded-full blur-2xl"></div>

        {/* Circuit Connection Lines */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <div
            className="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-[#ff6b35]/30 to-transparent"
            style={{ animation: 'circuit-pulse 4s ease-in-out infinite' }}
          ></div>
          <div
            className="absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-[#ff6b35]/30 to-transparent"
            style={{ animation: 'circuit-pulse 4s ease-in-out infinite', animationDelay: '2s' }}
          ></div>
          <div
            className="absolute left-1/4 top-0 w-px h-full bg-gradient-to-b from-transparent via-[#ff6b35]/30 to-transparent"
            style={{ animation: 'circuit-pulse 4s ease-in-out infinite', animationDelay: '1s' }}
          ></div>
          <div
            className="absolute right-1/4 top-0 w-px h-full bg-gradient-to-b from-transparent via-[#ff6b35]/30 to-transparent"
            style={{ animation: 'circuit-pulse 4s ease-in-out infinite', animationDelay: '3s' }}
          ></div>
        </div>
      </div>

      {/* Circuit Animation Styles */}
      <style jsx>{`
        @keyframes circuit-pulse {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 0.8; }
        }
      `}</style>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-20">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6"
          >
            <span className="text-gray-300">The fast way to actually</span>
            <br />
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]">
              get AI working in your business
            </span>
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-lg text-gray-400 max-w-2xl mx-auto"
          >
            RouKey intelligently routes your requests through our unified gateway to the perfect AI model,
            eliminating the complexity of managing multiple providers.
          </motion.p>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-start">

          {/* Left Side - Feature Cards */}
          <div className="space-y-8">

            {/* Smart AI Routing Card */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="relative"
            >
              <div className="glossy-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 hover:border-[#ff6b35]/30 transition-all duration-300"
              >
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-xl flex items-center justify-center pulse-glow">
                    <SparklesIcon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">Smart AI Routing</h3>
                    <p className="text-sm text-gray-400">with intelligent role detection</p>
                  </div>
                </div>

                <p className="text-gray-300 mb-6 leading-relaxed">
                  RouKey automatically detects your request type and routes it to the optimal AI model.
                  No manual switching between providers.
                </p>

                <div className="space-y-3">
                  <div className="flex items-center gap-3 text-sm">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-gray-300">Request Analyzed</span>
                  </div>
                  <div className="flex items-center gap-3 text-sm">
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                    <span className="text-gray-300">Role Classified: Frontend Coding</span>
                  </div>
                  <div className="flex items-center gap-3 text-sm">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                    <span className="text-gray-300">Routed to Claude 4 Opus</span>
                  </div>
                </div>

                <button className="mt-6 px-6 py-2 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white text-sm font-medium rounded-lg hover:from-[#ff7043] hover:to-[#ff6b35] transition-all duration-300 flex items-center gap-2">
                  <span>Try RouKey</span>
                  <ArrowRightIcon className="w-4 h-4" />
                </button>
              </div>
            </motion.div>

            {/* Multi-Provider Management Card */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="relative"
            >
              <div className="glossy-card bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 hover:border-[#ff6b35]/30 transition-all duration-300"
              >
                <div className="flex items-center gap-4 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-purple-700 rounded-xl flex items-center justify-center">
                    <ShieldCheckIcon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">Unified API Gateway</h3>
                    <p className="text-sm text-gray-400">for all AI providers</p>
                  </div>
                </div>

                <p className="text-gray-300 mb-6 leading-relaxed">
                  Connect OpenAI, Anthropic, Google, and more through one simple API.
                  Switch providers without changing your code.
                </p>

                <div className="space-y-3">
                  <div className="flex items-center gap-3 text-sm">
                    <CheckIcon className="w-4 h-4 text-green-400" />
                    <span className="text-gray-300">15+ AI Providers Supported</span>
                  </div>
                  <div className="flex items-center gap-3 text-sm">
                    <CheckIcon className="w-4 h-4 text-green-400" />
                    <span className="text-gray-300">Automatic Failover & Load Balancing</span>
                  </div>
                  <div className="flex items-center gap-3 text-sm">
                    <CheckIcon className="w-4 h-4 text-green-400" />
                    <span className="text-gray-300">Cost Optimization Built-in</span>
                  </div>
                </div>
              </div>
            </motion.div>

          </div>

          {/* Right Side - RouKey Playground */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="relative"
          >
            <div className="glossy-card bg-gradient-to-br from-gray-900/60 to-gray-800/40 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 hover:border-[#ff6b35]/30 transition-all duration-300"
            >
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-green-600 to-green-700 rounded-xl flex items-center justify-center">
                  <DocumentTextIcon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">RouKey Playground</h3>
                </div>
              </div>

              <p className="text-gray-300 mb-6 leading-relaxed">
                Test different AI models, compare responses, and configure your routing rules
                all in one interactive playground interface.
              </p>

              {/* Chat Messages */}
              <div className="space-y-4 mb-6">
                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
                  <div className="text-sm text-gray-400 mb-2">Write a React component for a login form</div>
                </div>

                <div className="bg-gray-700/30 rounded-lg p-4 border border-gray-600/30">
                  <div className="text-sm text-gray-300 mb-3">
                    <span className="text-[#ff6b35] font-medium">Claude 4 Opus:</span> Here's a modern React login component with TypeScript...
                  </div>
                  <div className="text-xs text-gray-500 flex items-center gap-2">
                    <span>✓ Routed via Frontend Coding role</span>
                    <span className="w-1 h-1 bg-gray-500 rounded-full"></span>
                    <span>Response time: 1.2s</span>
                  </div>
                </div>

                <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
                  <div className="text-sm text-gray-400">Now optimize this for mobile...</div>
                  <div className="flex items-center gap-2 mt-2">
                    <div className="w-2 h-2 bg-[#ff6b35] rounded-full animate-pulse"></div>
                    <span className="text-xs text-gray-500">RouKey is thinking...</span>
                  </div>
                </div>
              </div>

              {/* Input Field */}
              <div className="relative">
                <input
                  type="text"
                  placeholder="Try RouKey's intelligent routing..."
                  className="w-full bg-gray-800/50 border border-gray-700/50 rounded-lg px-4 py-3 text-white placeholder-gray-500 focus:outline-none focus:border-[#ff6b35]/50 transition-colors"
                  readOnly
                />
                <button className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-lg flex items-center justify-center">
                  <ArrowRightIcon className="w-4 h-4 text-white" />
                </button>
              </div>
            </div>
          </motion.div>

        </div>
      </div>
    </section>
  );
}
