-- Enable RLS and create policies for training_jobs table
-- This migration enables authentication-based access control for training jobs

-- 1. Enable RLS on training_jobs table
ALTER TABLE public.training_jobs ENABLE ROW LEVEL SECURITY;

-- 2. Create RLS policies for training_jobs
-- Users can view their own training jobs
CREATE POLICY "Users can view their own training jobs" ON public.training_jobs
    FOR SELECT USING (auth.uid() = user_id);

-- Users can create their own training jobs
CREATE POLICY "Users can create their own training jobs" ON public.training_jobs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own training jobs
CREATE POLICY "Users can update their own training jobs" ON public.training_jobs
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own training jobs
CREATE POLICY "Users can delete their own training jobs" ON public.training_jobs
    FOR DELETE USING (auth.uid() = user_id);

-- Service role can manage all training jobs (for system operations)
CREATE POLICY "Service role can manage all training jobs" ON public.training_jobs
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- 3. Add foreign key constraint for user_id if not already present
DO $$
BEGIN
    -- Check if the foreign key constraint already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_training_jobs_user' 
        AND table_name = 'training_jobs'
    ) THEN
        -- Add the foreign key constraint
        ALTER TABLE public.training_jobs 
        ADD CONSTRAINT fk_training_jobs_user 
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
    END IF;
END $$;

SELECT 'Training jobs RLS policies enabled successfully' as status;
