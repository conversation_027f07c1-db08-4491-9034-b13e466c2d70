// Utility to clear all user-related cache and storage
export async function clearAllUserCache() {
  console.log('Clearing all user cache and storage...');
  
  try {
    // Clear localStorage
    if (typeof window !== 'undefined') {
      localStorage.clear();
      console.log('localStorage cleared');
    }
  } catch (error) {
    console.warn('Failed to clear localStorage:', error);
  }

  try {
    // Clear sessionStorage
    if (typeof window !== 'undefined') {
      sessionStorage.clear();
      console.log('sessionStorage cleared');
    }
  } catch (error) {
    console.warn('Failed to clear sessionStorage:', error);
  }

  try {
    // Clear advanced cache
    const { globalCache } = await import('@/utils/advancedCache');
    globalCache.clear();
    console.log('Advanced cache cleared');
  } catch (error) {
    console.warn('Failed to clear advanced cache:', error);
  }

  try {
    // Clear any browser cache for API requests
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
      console.log('Browser caches cleared');
    }
  } catch (error) {
    console.warn('Failed to clear browser caches:', error);
  }

  try {
    // Clear any IndexedDB data (if used)
    if ('indexedDB' in window) {
      // This is a more aggressive approach - you might want to be more selective
      console.log('IndexedDB clearing not implemented (would need specific database names)');
    }
  } catch (error) {
    console.warn('Failed to clear IndexedDB:', error);
  }

  console.log('Cache clearing completed');
}

// Clear user-specific cache entries only (less aggressive)
export async function clearUserSpecificCache(userId?: string) {
  console.log('Clearing user-specific cache for user:', userId);
  
  try {
    // Clear advanced cache with user tags
    const { globalCache } = await import('@/utils/advancedCache');
    globalCache.invalidateByTags(['user', 'subscription', 'usage']);
    if (userId) {
      globalCache.invalidateByTags([`user_${userId}`]);
    }
    console.log('User-specific cache cleared');
  } catch (error) {
    console.warn('Failed to clear user-specific cache:', error);
  }

  try {
    // Clear user-specific localStorage items
    if (typeof window !== 'undefined') {
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('user') || key.includes('subscription') || key.includes('auth'))) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));
      console.log('User-specific localStorage items cleared:', keysToRemove);
    }
  } catch (error) {
    console.warn('Failed to clear user-specific localStorage:', error);
  }
}
