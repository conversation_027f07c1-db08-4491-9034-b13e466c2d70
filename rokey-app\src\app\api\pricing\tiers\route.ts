import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Return pricing tiers data
    const pricingTiers = [
      {
        id: 'professional',
        name: 'Professional',
        price: 29,
        currency: 'USD',
        interval: 'month',
        features: [
          'Unlimited API requests',
          'Access to 300+ models',
          'Intelligent role routing',
          'Enterprise security',
          'No request limits',
          'Priority support'
        ],
        popular: true
      }
    ];

    return NextResponse.json({
      success: true,
      data: pricingTiers
    });
  } catch (error) {
    console.error('Error fetching pricing tiers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch pricing tiers' },
      { status: 500 }
    );
  }
}
