// Phase 2A: Performance Tracking and Monitoring Utility
export class PerformanceTracker {
  private static instance: PerformanceTracker;
  private metrics: Map<string, number[]> = new Map();
  private readonly maxSamples = 100;

  static getInstance(): PerformanceTracker {
    if (!PerformanceTracker.instance) {
      PerformanceTracker.instance = new PerformanceTracker();
    }
    return PerformanceTracker.instance;
  }

  // Track API endpoint performance
  trackEndpoint(endpoint: string, duration: number, status: number = 200) {
    const key = `${endpoint}_${status >= 400 ? 'error' : 'success'}`;
    
    if (!this.metrics.has(key)) {
      this.metrics.set(key, []);
    }
    
    const measurements = this.metrics.get(key)!;
    measurements.push(duration);
    
    // Keep only recent measurements
    if (measurements.length > this.maxSamples) {
      measurements.shift();
    }
  }

  // Get performance statistics for an endpoint
  getStats(endpoint: string): {
    success: PerformanceStats | null;
    error: PerformanceStats | null;
  } {
    const successKey = `${endpoint}_success`;
    const errorKey = `${endpoint}_error`;
    
    return {
      success: this.calculateStats(this.metrics.get(successKey)),
      error: this.calculateStats(this.metrics.get(errorKey))
    };
  }

  private calculateStats(measurements?: number[]): PerformanceStats | null {
    if (!measurements || measurements.length === 0) return null;

    const sorted = [...measurements].sort((a, b) => a - b);
    const sum = measurements.reduce((acc, val) => acc + val, 0);
    
    return {
      count: measurements.length,
      average: Math.round(sum / measurements.length),
      median: Math.round(sorted[Math.floor(sorted.length / 2)]),
      p95: Math.round(sorted[Math.floor(sorted.length * 0.95)]),
      p99: Math.round(sorted[Math.floor(sorted.length * 0.99)]),
      min: Math.round(sorted[0]),
      max: Math.round(sorted[sorted.length - 1]),
      recent: measurements.slice(-5).map(m => Math.round(m))
    };
  }

  // Get overall performance summary
  getSummary(): PerformanceSummary {
    const endpoints = new Set<string>();
    
    // Extract unique endpoints
    for (const key of this.metrics.keys()) {
      const endpoint = key.replace(/_success$|_error$/, '');
      endpoints.add(endpoint);
    }

    const summary: PerformanceSummary = {
      totalEndpoints: endpoints.size,
      endpoints: {}
    };

    for (const endpoint of endpoints) {
      summary.endpoints[endpoint] = this.getStats(endpoint);
    }

    return summary;
  }

  // Performance health check
  getHealthStatus(): PerformanceHealth {
    const summary = this.getSummary();
    let status: 'excellent' | 'good' | 'fair' | 'poor' = 'excellent';
    const issues: string[] = [];

    for (const [endpoint, stats] of Object.entries(summary.endpoints)) {
      if (stats.success) {
        const avg = stats.success.average;
        const p95 = stats.success.p95;

        // Define performance thresholds
        if (endpoint.includes('/api/custom-configs')) {
          if (avg > 1000) {
            status = 'poor';
            issues.push(`Custom configs API slow: ${avg}ms average`);
          } else if (avg > 500) {
            status = status === 'excellent' ? 'fair' : status;
            issues.push(`Custom configs API could be faster: ${avg}ms average`);
          }
        } else if (endpoint.includes('/api/chat/conversations')) {
          if (avg > 500) {
            status = 'poor';
            issues.push(`Chat conversations API slow: ${avg}ms average`);
          } else if (avg > 200) {
            status = status === 'excellent' ? 'fair' : status;
            issues.push(`Chat conversations API could be faster: ${avg}ms average`);
          }
        } else if (endpoint.includes('/api/system-status')) {
          if (avg > 2000) {
            status = 'poor';
            issues.push(`System status API slow: ${avg}ms average`);
          } else if (avg > 1000) {
            status = status === 'excellent' ? 'fair' : status;
            issues.push(`System status API could be faster: ${avg}ms average`);
          }
        }

        // Check for high error rates
        if (stats.error && stats.error.count > 0) {
          const errorRate = stats.error.count / (stats.success.count + stats.error.count);
          if (errorRate > 0.1) {
            status = 'poor';
            issues.push(`High error rate for ${endpoint}: ${Math.round(errorRate * 100)}%`);
          }
        }
      }
    }

    return { status, issues };
  }

  // Clear all metrics
  clear() {
    this.metrics.clear();
  }

  // Export metrics for analysis
  exportMetrics(): Record<string, number[]> {
    const exported: Record<string, number[]> = {};
    for (const [key, values] of this.metrics.entries()) {
      exported[key] = [...values];
    }
    return exported;
  }
}

// Types
interface PerformanceStats {
  count: number;
  average: number;
  median: number;
  p95: number;
  p99: number;
  min: number;
  max: number;
  recent: number[];
}

interface PerformanceSummary {
  totalEndpoints: number;
  endpoints: Record<string, {
    success: PerformanceStats | null;
    error: PerformanceStats | null;
  }>;
}

interface PerformanceHealth {
  status: 'excellent' | 'good' | 'fair' | 'poor';
  issues: string[];
}

// Middleware wrapper for automatic tracking
export function withPerformanceTracking<T extends (...args: any[]) => Promise<any>>(
  endpoint: string,
  fn: T
): T {
  return (async (...args: any[]) => {
    const start = performance.now();
    const tracker = PerformanceTracker.getInstance();
    
    try {
      const result = await fn(...args);
      const duration = performance.now() - start;
      
      // Determine status from result
      let status = 200;
      if (result && typeof result === 'object' && 'status' in result) {
        status = result.status;
      }
      
      tracker.trackEndpoint(endpoint, duration, status);
      return result;
    } catch (error) {
      const duration = performance.now() - start;
      tracker.trackEndpoint(endpoint, duration, 500);
      throw error;
    }
  }) as T;
}

// React hook for performance monitoring
export function usePerformanceMonitoring() {
  const tracker = PerformanceTracker.getInstance();
  
  return {
    getStats: (endpoint: string) => tracker.getStats(endpoint),
    getSummary: () => tracker.getSummary(),
    getHealth: () => tracker.getHealthStatus(),
    clear: () => tracker.clear()
  };
}

// Console logging utility for development
export function logPerformanceReport() {
  const tracker = PerformanceTracker.getInstance();
  const summary = tracker.getSummary();
  const health = tracker.getHealthStatus();
  
  console.group('🚀 Performance Report (Phase 2A)');
  console.log(`Overall Health: ${health.status.toUpperCase()}`);
  
  if (health.issues.length > 0) {
    console.group('⚠️ Issues:');
    health.issues.forEach(issue => console.warn(issue));
    console.groupEnd();
  }
  
  console.group('📊 Endpoint Performance:');
  for (const [endpoint, stats] of Object.entries(summary.endpoints)) {
    if (stats.success) {
      console.log(`${endpoint}:`, {
        avg: `${stats.success.average}ms`,
        p95: `${stats.success.p95}ms`,
        count: stats.success.count,
        errors: stats.error?.count || 0
      });
    }
  }
  console.groupEnd();
  console.groupEnd();
}

// Export singleton instance
export const performanceTracker = PerformanceTracker.getInstance();
