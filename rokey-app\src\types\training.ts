// Types for training/fine-tuning functionality

export interface TrainingJob {
  id: string;
  user_id?: string;
  custom_api_config_id: string;
  name: string;
  description?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  training_data?: TrainingData;

  parameters?: TrainingParameters;
  progress_percentage: number;
  error_message?: string;
  fine_tuned_model_id?: string;
  created_at: string;
  updated_at: string;
  started_at?: string;
  completed_at?: string;
}

export interface TrainingData {
  prompts: TrainingPrompt[];
}

export interface TrainingPrompt {
  id: string;
  prompt: string;
  expected_response: string;
  category?: string;
  weight?: number; // For importance weighting
}



export interface TrainingParameters {
  learning_rate?: number;
  epochs?: number;
  batch_size?: number;
  max_sequence_length?: number;
  temperature?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  // Note: Most LLM fine-tuning services have limited parameter control
  // These are more relevant for custom training setups
}

export interface NewTrainingJob {
  custom_api_config_id: string;
  name: string;
  description?: string;
  training_data?: TrainingData;
  parameters?: TrainingParameters;
}


