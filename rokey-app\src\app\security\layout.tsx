import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Security & Trust - RouKey',
  description: 'Learn how <PERSON><PERSON><PERSON><PERSON> protects your data, API keys, and AI requests with enterprise-grade security measures.',
  keywords: ['security', 'data protection', 'encryption', 'API security', 'RouKey', 'enterprise security'],
  openGraph: {
    title: 'Security & Trust - RouKey',
    description: 'Learn how Rou<PERSON>ey protects your data, API keys, and AI requests with enterprise-grade security measures.',
    type: 'website',
    url: 'https://roukey.online/security',
    images: [
      {
        url: 'https://roukey.online/og-security.jpg',
        width: 1200,
        height: 630,
        alt: 'RouKey Security & Trust',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Security & Trust - RouKey',
    description: 'Enterprise-grade security for your AI routing platform.',
    images: ['https://roukey.online/og-security.jpg'],
  },
  alternates: {
    canonical: 'https://roukey.online/security',
  },
};

export default function SecurityLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
