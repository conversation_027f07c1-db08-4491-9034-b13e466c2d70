import { NextResponse } from 'next/server';

export async function POST() {
  try {
    // Return cache clearing instructions for the client
    return NextResponse.json({
      success: true,
      message: 'Cache clearing instructions',
      instructions: [
        'Open browser DevTools (F12)',
        'Right-click on the refresh button',
        'Select "Empty Cache and Hard Reload"',
        'Or use Ctrl+Shift+R (Windows) / Cmd+Shift+R (Mac)'
      ],
      clientScript: `
        // Client-side cache clearing
        if ('caches' in window) {
          caches.keys().then(names => {
            names.forEach(name => {
              caches.delete(name);
            });
          });
        }
        
        // Clear localStorage and sessionStorage
        localStorage.clear();
        sessionStorage.clear();
        
        console.log('✅ Browser caches cleared');
      `
    });
  } catch (error) {
    console.error('Error in cache clearing endpoint:', error);
    return NextResponse.json(
      { error: 'Failed to provide cache clearing instructions' },
      { status: 500 }
    );
  }
}
