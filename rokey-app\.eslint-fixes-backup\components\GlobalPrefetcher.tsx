'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

const CRITICAL_ROUTES = [
  '/features',
  '/pricing', 
  '/about',
  '/auth/signin',
  '/auth/signup'
];

export default function GlobalPrefetcher() {
  const router = useRouter();

  useEffect(() => {
    // Prefetch critical routes immediately on app load
    const prefetchCriticalRoutes = () => {
      CRITICAL_ROUTES.forEach(route => {
        router.prefetch(route);
      });
    };

    // Use requestIdleCallback for better performance
    if (typeof window !== 'undefined') {
      if ('requestIdleCallback' in window) {
        window.requestIdleCallback(prefetchCriticalRoutes, { timeout: 2000 });
      } else {
        // Fallback for browsers without requestIdleCallback
        setTimeout(prefetchCriticalRoutes, 100);
      }
    }
  }, [router]);

  // This component doesn't render anything
  return null;
}
