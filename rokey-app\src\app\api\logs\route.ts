import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { z } from 'zod';

const DEFAULT_PAGE_SIZE = 20;
const VALID_SORT_COLUMNS = [
  'request_timestamp', 
  'status_code', 
  'llm_provider_name', 
  'llm_model_name', 
  'llm_provider_latency_ms', 
  'processing_duration_ms',
  'tokens_prompt',
  'tokens_completion',
  'custom_api_config_id',
  'role_used'
  // Add other sortable columns from request_logs as needed
] as const; // Use const assertion for stricter enum type

// Zod schema for query parameters
const LogsQuerySchema = z.object({
  page: z.coerce.number().int().positive().optional().default(1),
  pageSize: z.coerce.number().int().positive().optional().default(DEFAULT_PAGE_SIZE),
  // Ensure incoming string is parsed to Date object, then validated
  startDate: z.string().datetime({ offset: true }).optional().transform((val) => val ? new Date(val) : undefined),
  endDate: z.string().datetime({ offset: true }).optional().transform((val) => val ? new Date(val) : undefined),
  customApiConfigId: z.string().uuid().optional(),
  status: z.enum(['success', 'error']).optional(),
  sortBy: z.enum(VALID_SORT_COLUMNS).optional().default('request_timestamp'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
  // TODO: Add other filter fields as needed, e.g., llm_provider_name, role_used
});

export async function GET(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    if (!supabase) {
      console.error('Supabase client could not be initialized in /api/logs.');
      return NextResponse.json({ error: 'Server configuration error.' }, { status: 500 });
    }

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      console.error('Authentication failed in /api/logs:', authError);
      return NextResponse.json({ error: 'Unauthorized: You must be logged in to view logs.' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validationResult = LogsQuerySchema.safeParse(queryParams);

    if (!validationResult.success) {
      console.error('Invalid query parameters for /api/logs:', validationResult.error.flatten());
      return NextResponse.json({ error: 'Invalid query parameters', issues: validationResult.error.flatten() }, { status: 400 });
    }

    const {
      page,
      pageSize,
      startDate,
      endDate,
      customApiConfigId,
      status,
      sortBy,
      sortOrder,
    } = validationResult.data;

    let query = supabase
      .from('request_logs')
      .select('*', { count: 'exact' })
      .eq('user_id', user.id); // Explicitly filter by authenticated user

    // Apply filters
    if (startDate) {
      query = query.gte('request_timestamp', startDate.toISOString());
    }
    if (endDate) {
      // To make endDate inclusive for the day, set time to end of day
      const inclusiveEndDate = new Date(endDate);
      inclusiveEndDate.setUTCHours(23, 59, 59, 999); // Use UTC for consistency with ISO strings
      query = query.lte('request_timestamp', inclusiveEndDate.toISOString());
    }
    if (customApiConfigId) {
      query = query.eq('custom_api_config_id', customApiConfigId);
    }
    if (status) {
      if (status === 'success') {
        query = query.gte('status_code', 200).lt('status_code', 300);
      } else if (status === 'error') {
        query = query.gte('status_code', 400);
      }
    }
    
    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    const offset = (page - 1) * pageSize;
    query = query.range(offset, offset + pageSize - 1);

    const { data: logs, error, count } = await query;

    if (error) {
      console.error('Error fetching logs from Supabase:', error);
      return NextResponse.json({ error: 'Failed to fetch logs', details: error.message }, { status: 500 });
    }

    const response = NextResponse.json({
      logs,
      pagination: {
        currentPage: page,
        pageSize,
        totalCount: count || 0,
        totalPages: Math.ceil((count || 0) / pageSize),
      },
      sorting: { // Optionally return current sorting state
        sortBy,
        sortOrder
      }
    });

    // Add cache headers for better performance
    response.headers.set('Cache-Control', 'private, max-age=30, stale-while-revalidate=60');

    return response;

  } catch (e: any) {
    console.error('Unexpected error in /api/logs GET handler:', e);
    return NextResponse.json({ error: 'An unexpected server error occurred.', details: e.message }, { status: 500 });
  }
} 