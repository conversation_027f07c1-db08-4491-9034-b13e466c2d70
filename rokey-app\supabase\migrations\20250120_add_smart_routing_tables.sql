-- Migration: Smart Cost-Optimized and A/B Routing Tables
-- Date: 2025-01-20
-- Description: Add tables for quality metrics tracking, A/B testing, and smart cost optimization

-- 1. Create routing_quality_metrics table for tracking model performance
CREATE TABLE IF NOT EXISTS public.routing_quality_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    custom_api_config_id UUID REFERENCES public.custom_api_configs(id) ON DELETE CASCADE NOT NULL,
    api_key_id UUID REFERENCES public.api_keys(id) ON DELETE CASCADE NOT NULL,
    model_used TEXT NOT NULL,
    provider TEXT NOT NULL,
    
    -- Request details
    prompt_text TEXT NOT NULL,
    prompt_complexity_level INTEGER CHECK (prompt_complexity_level >= 1 AND prompt_complexity_level <= 5),
    task_category TEXT, -- e.g., 'coding', 'writing', 'analysis', 'chat', 'reasoning'
    
    -- Quality metrics
    quality_score DECIMAL(3,2) CHECK (quality_score >= 0.0 AND quality_score <= 10.0),
    user_rating INTEGER CHECK (user_rating >= 1 AND user_rating <= 5),
    response_length INTEGER,
    response_time_ms INTEGER,
    
    -- Cost metrics
    tokens_prompt INTEGER,
    tokens_completion INTEGER,
    cost_usd DECIMAL(10,8),
    cost_per_quality_point DECIMAL(10,8), -- cost / quality_score
    
    -- Behavioral signals
    user_copied_response BOOLEAN DEFAULT FALSE,
    user_regenerated BOOLEAN DEFAULT FALSE,
    user_asked_followup BOOLEAN DEFAULT FALSE,
    conversation_continued BOOLEAN DEFAULT FALSE,
    
    -- Metadata
    routing_strategy TEXT NOT NULL,
    was_ab_test BOOLEAN DEFAULT FALSE,
    ab_test_group TEXT, -- 'control' or 'variant'
    
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- 2. Create cost_optimization_profiles table for user-specific cost-quality preferences
CREATE TABLE IF NOT EXISTS public.cost_optimization_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    custom_api_config_id UUID REFERENCES public.custom_api_configs(id) ON DELETE CASCADE NOT NULL,
    
    -- Learning phase tracking
    learning_phase_requests INTEGER DEFAULT 0,
    learning_phase_completed BOOLEAN DEFAULT FALSE,
    
    -- Cost-quality thresholds per complexity level
    simple_task_min_quality DECIMAL(3,2) DEFAULT 6.0,
    moderate_task_min_quality DECIMAL(3,2) DEFAULT 7.0,
    complex_task_min_quality DECIMAL(3,2) DEFAULT 8.0,
    
    -- Model tier preferences (calculated from performance data)
    preferred_cheap_models JSONB DEFAULT '[]'::jsonb, -- Array of model IDs
    preferred_moderate_models JSONB DEFAULT '[]'::jsonb,
    preferred_premium_models JSONB DEFAULT '[]'::jsonb,
    
    -- Performance statistics
    total_requests INTEGER DEFAULT 0,
    total_cost_saved DECIMAL(10,6) DEFAULT 0,
    average_quality_score DECIMAL(3,2),
    
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    
    UNIQUE(user_id, custom_api_config_id)
);

-- 3. Create ab_test_assignments table for A/B routing experiments
CREATE TABLE IF NOT EXISTS public.ab_test_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    custom_api_config_id UUID REFERENCES public.custom_api_configs(id) ON DELETE CASCADE NOT NULL,
    
    -- A/B test configuration
    test_percentage DECIMAL(3,2) DEFAULT 15.0 CHECK (test_percentage >= 0.0 AND test_percentage <= 100.0),
    current_test_model_id UUID REFERENCES public.api_keys(id) ON DELETE SET NULL,
    control_model_id UUID REFERENCES public.api_keys(id) ON DELETE SET NULL,
    
    -- Test tracking
    total_requests INTEGER DEFAULT 0,
    test_requests INTEGER DEFAULT 0,
    control_requests INTEGER DEFAULT 0,
    
    -- Performance comparison
    test_avg_quality DECIMAL(3,2),
    control_avg_quality DECIMAL(3,2),
    test_avg_cost DECIMAL(10,8),
    control_avg_cost DECIMAL(10,8),
    
    -- Test status
    test_active BOOLEAN DEFAULT TRUE,
    test_started_at TIMESTAMPTZ DEFAULT now(),
    test_ended_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    
    UNIQUE(user_id, custom_api_config_id)
);

-- 4. Create model_cost_tiers table for categorizing models by cost
CREATE TABLE IF NOT EXISTS public.model_cost_tiers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    model_id TEXT NOT NULL,
    provider TEXT NOT NULL,
    
    -- Cost classification
    cost_tier TEXT NOT NULL CHECK (cost_tier IN ('cheap', 'moderate', 'premium')),
    avg_cost_per_1k_tokens DECIMAL(10,8) NOT NULL,
    
    -- Performance characteristics
    typical_quality_score DECIMAL(3,2),
    best_for_tasks JSONB DEFAULT '[]'::jsonb, -- Array of task categories
    
    -- Auto-calculated fields
    cost_efficiency_score DECIMAL(5,3), -- quality_score / cost_per_1k_tokens
    
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    
    UNIQUE(model_id, provider)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_routing_quality_metrics_user_config ON public.routing_quality_metrics(user_id, custom_api_config_id);
CREATE INDEX IF NOT EXISTS idx_routing_quality_metrics_created_at ON public.routing_quality_metrics(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_routing_quality_metrics_model_quality ON public.routing_quality_metrics(model_used, quality_score DESC);
CREATE INDEX IF NOT EXISTS idx_routing_quality_metrics_cost_quality ON public.routing_quality_metrics(cost_per_quality_point ASC);

CREATE INDEX IF NOT EXISTS idx_cost_optimization_profiles_user_config ON public.cost_optimization_profiles(user_id, custom_api_config_id);

CREATE INDEX IF NOT EXISTS idx_ab_test_assignments_user_config ON public.ab_test_assignments(user_id, custom_api_config_id);
CREATE INDEX IF NOT EXISTS idx_ab_test_assignments_active ON public.ab_test_assignments(test_active) WHERE test_active = TRUE;

CREATE INDEX IF NOT EXISTS idx_model_cost_tiers_tier ON public.model_cost_tiers(cost_tier);
CREATE INDEX IF NOT EXISTS idx_model_cost_tiers_efficiency ON public.model_cost_tiers(cost_efficiency_score DESC);

-- Add RLS policies
ALTER TABLE public.routing_quality_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cost_optimization_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ab_test_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.model_cost_tiers ENABLE ROW LEVEL SECURITY;

-- RLS policies for routing_quality_metrics
CREATE POLICY "Users can view their own quality metrics" ON public.routing_quality_metrics
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own quality metrics" ON public.routing_quality_metrics
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own quality metrics" ON public.routing_quality_metrics
    FOR UPDATE USING (auth.uid() = user_id);

-- RLS policies for cost_optimization_profiles
CREATE POLICY "Users can view their own cost profiles" ON public.cost_optimization_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own cost profiles" ON public.cost_optimization_profiles
    FOR ALL USING (auth.uid() = user_id);

-- RLS policies for ab_test_assignments
CREATE POLICY "Users can view their own A/B tests" ON public.ab_test_assignments
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can manage their own A/B tests" ON public.ab_test_assignments
    FOR ALL USING (auth.uid() = user_id);

-- RLS policies for model_cost_tiers (read-only for all authenticated users)
CREATE POLICY "Authenticated users can view model cost tiers" ON public.model_cost_tiers
    FOR SELECT USING (auth.role() = 'authenticated');

-- Add triggers for updated_at
CREATE TRIGGER handle_updated_at_routing_quality_metrics 
    BEFORE UPDATE ON public.routing_quality_metrics 
    FOR EACH ROW EXECUTE FUNCTION public.moddatetime();

CREATE TRIGGER handle_updated_at_cost_optimization_profiles 
    BEFORE UPDATE ON public.cost_optimization_profiles 
    FOR EACH ROW EXECUTE FUNCTION public.moddatetime();

CREATE TRIGGER handle_updated_at_ab_test_assignments 
    BEFORE UPDATE ON public.ab_test_assignments 
    FOR EACH ROW EXECUTE FUNCTION public.moddatetime();

CREATE TRIGGER handle_updated_at_model_cost_tiers 
    BEFORE UPDATE ON public.model_cost_tiers 
    FOR EACH ROW EXECUTE FUNCTION public.moddatetime();

-- Add comments
COMMENT ON TABLE public.routing_quality_metrics IS 'Tracks quality and performance metrics for each routing decision';
COMMENT ON TABLE public.cost_optimization_profiles IS 'Stores user-specific cost-quality optimization preferences and learning data';
COMMENT ON TABLE public.ab_test_assignments IS 'Manages A/B testing assignments for routing optimization';
COMMENT ON TABLE public.model_cost_tiers IS 'Categorizes models by cost tiers for smart routing decisions';
