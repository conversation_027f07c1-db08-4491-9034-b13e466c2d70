'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/badge';
import {
  Copy,
  Trash2,
  Calendar,
  Activity,
  Shield,
  Globe
} from 'lucide-react';
import { toast } from 'sonner';
import { formatDistanceToNow } from 'date-fns';

interface ApiKeyCardProps {
  apiKey: {
    id: string;
    key_name: string;
    key_prefix: string;
    masked_key: string;
    permissions: {
      chat: boolean;
      streaming: boolean;
      all_models: boolean;
    };
    rate_limit_per_minute: number;
    rate_limit_per_hour: number;
    rate_limit_per_day: number;
    allowed_ips: string[];
    allowed_domains: string[];
    total_requests: number;
    last_used_at?: string;
    status: 'active' | 'inactive' | 'revoked' | 'expired';
    expires_at?: string;
    created_at: string;
    custom_api_configs: {
      id: string;
      name: string;
    };
  };
  onRevoke: (apiKeyId: string) => void;
}

export function ApiKeyCard({ apiKey, onRevoke }: ApiKeyCardProps) {
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('API key copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy API key');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-900/30 text-green-300 border-green-500/50';
      case 'inactive':
        return 'bg-yellow-900/30 text-yellow-300 border-yellow-500/50';
      case 'revoked':
        return 'bg-red-900/30 text-red-300 border-red-500/50';
      case 'expired':
        return 'bg-gray-800/50 text-gray-300 border-gray-600/50';
      default:
        return 'bg-gray-800/50 text-gray-300 border-gray-600/50';
    }
  };

  const isExpired = apiKey.expires_at && new Date(apiKey.expires_at) < new Date();
  const isActive = apiKey.status === 'active' && !isExpired;

  return (
    <div className={`bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6 transition-all duration-200 hover:border-gray-700/50 ${!isActive ? 'opacity-75' : ''}`}>
      <div className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <h3 className="text-lg font-semibold text-white">{apiKey.key_name}</h3>
            <p className="text-sm text-gray-400">
              Configuration: {apiKey.custom_api_configs.name}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={getStatusColor(apiKey.status)}>
              {apiKey.status}
            </Badge>
            {isExpired && (
              <Badge className="bg-red-900/30 text-red-300 border-red-500/50">
                Expired
              </Badge>
            )}
          </div>
        </div>
      </div>

      <div className="space-y-4">
        {/* API Key Display */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-300">API Key (Masked)</label>
          <div className="flex items-center gap-2 p-3 bg-gray-800/50 rounded-lg border border-gray-700">
            <code className="flex-1 text-sm font-mono text-gray-300">
              {apiKey.key_prefix}_{'*'.repeat(28)}{typeof apiKey.masked_key === 'string' ? apiKey.masked_key.slice(-4) : 'xxxx'}
            </code>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(`${apiKey.key_prefix}_${'*'.repeat(28)}${typeof apiKey.masked_key === 'string' ? apiKey.masked_key.slice(-4) : 'xxxx'}`)}
              className="h-8 w-8 p-0 text-gray-400 hover:text-white hover:bg-gray-700/50"
              title="Copy masked key (for reference only)"
            >
              <Copy className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex items-center gap-2 text-xs text-amber-400 bg-amber-900/20 p-2 rounded">
            <span>⚠️</span>
            <span>Full API key was only shown once during creation for security. Save it securely when creating new keys.</span>
          </div>
        </div>

        {/* Permissions */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-300">Permissions</label>
          <div className="flex flex-wrap gap-2">
            {apiKey.permissions.chat && (
              <Badge variant="secondary" className="text-xs bg-blue-900/30 text-blue-300 border-blue-500/50">
                Chat Completions
              </Badge>
            )}
            {apiKey.permissions.streaming && (
              <Badge variant="secondary" className="text-xs bg-green-900/30 text-green-300 border-green-500/50">
                Streaming
              </Badge>
            )}
            {apiKey.permissions.all_models && (
              <Badge variant="secondary" className="text-xs bg-purple-900/30 text-purple-300 border-purple-500/50">
                All Models
              </Badge>
            )}
          </div>
        </div>



        {/* Security Restrictions */}
        {(apiKey.allowed_ips.length > 0 || apiKey.allowed_domains.length > 0) && (
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-300 flex items-center gap-1">
              <Shield className="h-4 w-4" />
              Security Restrictions
            </label>
            <div className="space-y-1 text-xs">
              {apiKey.allowed_ips.length > 0 && (
                <div className="flex items-center gap-1 text-gray-400">
                  <span className="font-medium">IPs:</span>
                  <span>{apiKey.allowed_ips.join(', ')}</span>
                </div>
              )}
              {apiKey.allowed_domains.length > 0 && (
                <div className="flex items-center gap-1 text-gray-400">
                  <Globe className="h-3 w-3" />
                  <span className="font-medium">Domains:</span>
                  <span>{apiKey.allowed_domains.join(', ')}</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Usage Stats */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-300 flex items-center gap-1">
            <Activity className="h-4 w-4" />
            Usage Statistics
          </label>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">Total Requests:</span>
              <span className="ml-2 font-semibold text-white">{apiKey.total_requests.toLocaleString()}</span>
            </div>
            {apiKey.last_used_at && (
              <div>
                <span className="text-gray-400">Last Used:</span>
                <span className="ml-2 font-semibold text-white">
                  {formatDistanceToNow(new Date(apiKey.last_used_at), { addSuffix: true })}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Expiration */}
        {apiKey.expires_at && (
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-300 flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              Expiration
            </label>
            <div className="text-sm">
              <span className={`font-semibold ${isExpired ? 'text-red-400' : 'text-white'}`}>
                {new Date(apiKey.expires_at).toLocaleDateString()} at{' '}
                {new Date(apiKey.expires_at).toLocaleTimeString()}
              </span>
              {!isExpired && (
                <span className="ml-2 text-gray-400">
                  ({formatDistanceToNow(new Date(apiKey.expires_at), { addSuffix: true })})
                </span>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-end pt-2 border-t border-gray-700">
          {apiKey.status !== 'revoked' && (
            <Button
              variant="destructive"
              size="sm"
              onClick={() => onRevoke(apiKey.id)}
              className="text-xs"
            >
              <Trash2 className="h-3 w-3 mr-1" />
              Revoke
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
