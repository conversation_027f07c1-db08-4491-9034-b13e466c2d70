'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  isStale: boolean;
  promise?: Promise<T>;
}

interface FetchOptions {
  cacheTime?: number; // How long to cache data (ms)
  staleTime?: number; // How long before data is considered stale (ms)
  refetchOnWindowFocus?: boolean;
  refetchOnReconnect?: boolean;
  retryCount?: number;
  retryDelay?: number;
  backgroundRefetch?: boolean;
}

interface FetchState<T> {
  data: T | null;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  isStale: boolean;
  lastFetched: number | null;
}

class OptimizedFetchCache {
  private cache = new Map<string, CacheEntry<any>>();
  private subscribers = new Map<string, Set<() => void>>();
  private retryTimeouts = new Map<string, NodeJS.Timeout>();

  get<T>(key: string): CacheEntry<T> | undefined {
    return this.cache.get(key);
  }

  set<T>(key: string, data: T, cacheTime: number, staleTime: number) {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      isStale: false,
    };

    this.cache.set(key, entry);

    // Set stale timer
    setTimeout(() => {
      const currentEntry = this.cache.get(key);
      if (currentEntry && currentEntry.timestamp === entry.timestamp) {
        currentEntry.isStale = true;
        this.notifySubscribers(key);
      }
    }, staleTime);

    // Set cache expiry timer
    setTimeout(() => {
      const currentEntry = this.cache.get(key);
      if (currentEntry && currentEntry.timestamp === entry.timestamp) {
        this.cache.delete(key);
      }
    }, cacheTime);

    this.notifySubscribers(key);
  }

  subscribe(key: string, callback: () => void) {
    if (!this.subscribers.has(key)) {
      this.subscribers.set(key, new Set());
    }
    this.subscribers.get(key)!.add(callback);

    return () => {
      const subs = this.subscribers.get(key);
      if (subs) {
        subs.delete(callback);
        if (subs.size === 0) {
          this.subscribers.delete(key);
        }
      }
    };
  }

  private notifySubscribers(key: string) {
    const subs = this.subscribers.get(key);
    if (subs) {
      subs.forEach(callback => callback());
    }
  }

  setPromise<T>(key: string, promise: Promise<T>) {
    const entry = this.cache.get(key);
    if (entry) {
      entry.promise = promise;
    }
  }

  clearRetryTimeout(key: string) {
    const timeout = this.retryTimeouts.get(key);
    if (timeout) {
      clearTimeout(timeout);
      this.retryTimeouts.delete(key);
    }
  }

  setRetryTimeout(key: string, timeout: NodeJS.Timeout) {
    this.clearRetryTimeout(key);
    this.retryTimeouts.set(key, timeout);
  }

  delete(key: string) {
    this.cache.delete(key);
    this.clearRetryTimeout(key);
  }

  clear() {
    this.cache.clear();
    this.subscribers.clear();
    this.retryTimeouts.forEach(timeout => clearTimeout(timeout));
    this.retryTimeouts.clear();
  }
}

// Global cache instance
const globalCache = new OptimizedFetchCache();

export function useOptimizedFetch<T>(
  key: string | null,
  fetcher: () => Promise<T>,
  options: FetchOptions = {}
): FetchState<T> & { refetch: () => Promise<void>; invalidate: () => void } {
  const {
    cacheTime = 300000, // 5 minutes
    staleTime = 30000, // 30 seconds
    refetchOnWindowFocus = true,
    refetchOnReconnect = true,
    retryCount = 3,
    retryDelay = 1000,
    backgroundRefetch = true,
  } = options;

  const [state, setState] = useState<FetchState<T>>({
    data: null,
    isLoading: false,
    isError: false,
    error: null,
    isStale: false,
    lastFetched: null,
  });

  const retryCountRef = useRef(0);
  const isMountedRef = useRef(true);

  // Initialize state from cache
  useEffect(() => {
    if (!key) return;

    const cached = globalCache.get<T>(key);
    if (cached) {
      setState(prev => ({
        ...prev,
        data: cached.data,
        isStale: cached.isStale,
        lastFetched: cached.timestamp,
        isLoading: false,
        isError: false,
        error: null,
      }));
    }
  }, [key]);

  const fetchData = useCallback(async (isBackground = false) => {
    if (!key || !isMountedRef.current) return;

    // Check if there's already a pending request
    const cached = globalCache.get<T>(key);
    if (cached?.promise) {
      try {
        const data = await cached.promise;
        if (isMountedRef.current) {
          setState(prev => ({
            ...prev,
            data,
            isLoading: false,
            isError: false,
            error: null,
            isStale: false,
            lastFetched: Date.now(),
          }));
        }
        return;
      } catch (error) {
        // Continue with normal error handling
      }
    }

    if (!isBackground) {
      setState(prev => ({ ...prev, isLoading: true, isError: false, error: null }));
    }

    const fetchPromise = fetcher();
    globalCache.setPromise(key, fetchPromise);

    try {
      const data = await fetchPromise;
      retryCountRef.current = 0;
      globalCache.clearRetryTimeout(key);

      if (isMountedRef.current) {
        setState(prev => ({
          ...prev,
          data,
          isLoading: false,
          isError: false,
          error: null,
          isStale: false,
          lastFetched: Date.now(),
        }));

        globalCache.set(key, data, cacheTime, staleTime);
      }
    } catch (error) {
      const err = error as Error;
      
      if (retryCountRef.current < retryCount) {
        retryCountRef.current++;
        const delay = retryDelay * Math.pow(2, retryCountRef.current - 1); // Exponential backoff
        
        const timeout = setTimeout(() => {
          fetchData(isBackground);
        }, delay);
        
        globalCache.setRetryTimeout(key, timeout);
      } else {
        if (isMountedRef.current) {
          setState(prev => ({
            ...prev,
            isLoading: false,
            isError: true,
            error: err,
          }));
        }
        retryCountRef.current = 0;
      }
    }
  }, [key, fetcher, cacheTime, staleTime, retryCount, retryDelay]);

  // Subscribe to cache updates
  useEffect(() => {
    if (!key) return;

    const unsubscribe = globalCache.subscribe(key, () => {
      const cached = globalCache.get<T>(key);
      if (cached && isMountedRef.current) {
        setState(prev => ({
          ...prev,
          data: cached.data,
          isStale: cached.isStale,
          lastFetched: cached.timestamp,
        }));

        // Background refetch if data is stale
        if (cached.isStale && backgroundRefetch) {
          fetchData(true);
        }
      }
    });

    return unsubscribe;
  }, [key, fetchData, backgroundRefetch]);

  // Initial fetch
  useEffect(() => {
    if (!key) return;

    const cached = globalCache.get<T>(key);
    if (!cached || cached.isStale) {
      fetchData();
    }
  }, [key, fetchData]);

  // Refetch on window focus
  useEffect(() => {
    if (!refetchOnWindowFocus || !key) return;

    const handleFocus = () => {
      const cached = globalCache.get<T>(key);
      if (cached && cached.isStale) {
        fetchData(true);
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [key, fetchData, refetchOnWindowFocus]);

  // Refetch on reconnect
  useEffect(() => {
    if (!refetchOnReconnect || !key) return;

    const handleOnline = () => {
      const cached = globalCache.get<T>(key);
      if (cached && cached.isStale) {
        fetchData(true);
      }
    };

    window.addEventListener('online', handleOnline);
    return () => window.removeEventListener('online', handleOnline);
  }, [key, fetchData, refetchOnReconnect]);

  // Cleanup
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (key) {
        globalCache.clearRetryTimeout(key);
      }
    };
  }, [key]);

  const refetch = useCallback(async () => {
    retryCountRef.current = 0;
    await fetchData();
  }, [fetchData]);

  const invalidate = useCallback(() => {
    if (key) {
      globalCache.delete(key);
    }
  }, [key]);

  return {
    ...state,
    refetch,
    invalidate,
  };
}

// Utility function to preload data
export function preloadData<T>(key: string, fetcher: () => Promise<T>, options: FetchOptions = {}) {
  const { cacheTime = 300000, staleTime = 30000 } = options;
  
  const cached = globalCache.get<T>(key);
  if (cached && !cached.isStale) {
    return Promise.resolve(cached.data);
  }

  const promise = fetcher();
  globalCache.setPromise(key, promise);

  promise.then(data => {
    globalCache.set(key, data, cacheTime, staleTime);
  }).catch(() => {
    // Silently fail for preloading
  });

  return promise;
}

// Phase 2B: Enhanced batch fetching
export function useBatchFetch<T>(
  requests: Array<{ key: string; fetcher: () => Promise<T> }>,
  options: FetchOptions = {}
) {
  // Fix: Call hooks at the top level, not inside map
  const results: Array<FetchState<T> & { refetch: () => Promise<void>; invalidate: () => void }> = [];
  for (const { key, fetcher } of requests) {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    results.push(useOptimizedFetch(key, fetcher, options));
  }

  return {
    data: results.map(r => r.data),
    isLoading: results.some(r => r.isLoading),
    isError: results.some(r => r.isError),
    errors: results.map(r => r.error),
    refetchAll: () => Promise.all(results.map(r => r.refetch())),
    invalidateAll: () => results.forEach(r => r.invalidate()),
    isAllLoaded: results.every(r => r.data !== null && !r.isLoading)
  };
}

// Phase 2B: Prefetch multiple resources
export function prefetchResources<T>(
  resources: Array<{ key: string; fetcher: () => Promise<T> }>,
  options: FetchOptions = {}
) {
  return Promise.allSettled(
    resources.map(({ key, fetcher }) => preloadData(key, fetcher, options))
  );
}

// Clear all cache
export function clearAllCache() {
  globalCache.clear();
}
