import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'RouKey Features - Advanced AI Gateway with Multi-Role Orchestration | RouKey',
  description: 'Discover RouKey\'s unique features: Multi-Role Orchestration, Smart Cost Optimization, 300+ AI models, unlimited requests, document processing, and advanced analytics. The only AI gateway you\'ll ever need.',
  keywords: 'AI gateway features, multi-role orchestration, cost optimization, AI routing, unlimited API requests, document processing, AI analytics, smart routing',
  openGraph: {
    title: 'RouKey Features - Advanced AI Gateway with Multi-Role Orchestration',
    description: 'Explore RouKey\'s revolutionary features including Multi-Role Orchestration, Smart Cost Optimization, and access to 300+ AI models with unlimited requests.',
    type: 'website',
    url: 'https://roukey.online/features',
  },
  alternates: {
    canonical: 'https://roukey.online/features',
  },
};

export default function FeaturesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
