import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Contact RouKey - Get in Touch with Our AI Gateway Team | RouKey',
  description: 'Contact RouKey for support, sales inquiries, partnerships, or technical questions. Get help with AI gateway setup, routing strategies, and enterprise solutions. Fast response guaranteed.',
  keywords: 'contact <PERSON>ou<PERSON>ey, AI gateway support, technical support, sales inquiry, partnership, enterprise solutions, customer service',
  openGraph: {
    title: 'Contact RouKey - Get in Touch with Our AI Gateway Team',
    description: 'Need help with <PERSON>ou<PERSON><PERSON>? Contact our team for support, sales inquiries, partnerships, or technical questions. Fast response guaranteed.',
    type: 'website',
    url: 'https://roukey.online/contact',
  },
  alternates: {
    canonical: 'https://roukey.online/contact',
  },
};

export default function ContactLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
