export interface UserGeneratedApi<PERSON>ey {
  id: string;
  user_id: string;
  custom_api_config_id: string;
  key_name: string;
  key_prefix: string;
  key_hash: string;
  encrypted_key_suffix: string;
  permissions: {
    chat: boolean;
    streaming: boolean;
    all_models: boolean;
  };
  allowed_ips: string[];
  allowed_domains: string[];
  total_requests: number;
  last_used_at?: string;
  last_used_ip?: string;
  status: 'active' | 'inactive' | 'revoked' | 'expired';
  expires_at?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateUserApiKeyRequest {
  custom_api_config_id: string;
  key_name: string;
  permissions?: {
    chat?: boolean;
    streaming?: boolean;
    all_models?: boolean;
  };
  allowed_ips?: string[];
  allowed_domains?: string[];
  expires_at?: string;
}

export interface UserApiKeyUsageLog {
  id: string;
  user_generated_api_key_id: string;
  user_id: string;
  custom_api_config_id: string;
  request_timestamp: string;
  ip_address?: string;
  user_agent?: string;
  referer?: string;
  endpoint: string;
  http_method: string;
  status_code?: number;
  model_used?: string;
  provider_used?: string;
  tokens_prompt?: number;
  tokens_completion?: number;
  cost_usd?: number;
  response_time_ms?: number;
  error_message?: string;
  error_type?: string;
  created_at: string;
}

export interface UserApiKeyRateLimit {
  id: string;
  user_generated_api_key_id: string;
  window_type: 'minute' | 'hour' | 'day';
  window_start: string;
  request_count: number;
  created_at: string;
  updated_at: string;
}

export interface GeneratedApiKeyResponse {
  id: string;
  key_name: string;
  api_key: string; // Full key - only shown once
  key_prefix: string;
  permissions: UserGeneratedApiKey['permissions'];
  created_at: string;
  expires_at?: string;
}

export interface ApiKeyValidationResult {
  isValid: boolean;
  apiKey?: UserGeneratedApiKey;
  error?: string;
}
