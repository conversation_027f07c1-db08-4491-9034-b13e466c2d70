'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ExclamationTriangleIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';
import { SubscriptionTier } from '@/lib/stripe-client';
import { useRouter } from 'next/navigation';

interface LimitIndicatorProps {
  current: number;
  limit: number;
  label: string;
  tier: SubscriptionTier;
  showUpgradeHint?: boolean;
  className?: string;
  theme?: 'light' | 'dark';
}

export function LimitIndicator({
  current,
  limit,
  label,
  tier,
  showUpgradeHint = true,
  className = '',
  theme = 'light'
}: LimitIndicatorProps) {
  const router = useRouter();
  const isUnlimited = limit >= 999999;
  const percentage = isUnlimited ? 0 : (current / limit) * 100;
  const isNearLimit = percentage >= 80;
  const isAtLimit = current >= limit && !isUnlimited;

  const getStatusColor = () => {
    if (theme === 'dark') {
      if (isUnlimited) return 'text-green-400';
      if (isAtLimit) return 'text-red-400';
      if (isNearLimit) return 'text-yellow-400';
      return 'text-green-400';
    } else {
      if (isUnlimited) return 'text-green-600';
      if (isAtLimit) return 'text-red-600';
      if (isNearLimit) return 'text-yellow-600';
      return 'text-green-600';
    }
  };

  const getStatusIcon = () => {
    const iconClass = theme === 'dark' ? 'w-5 h-5' : 'w-5 h-5';
    const greenColor = theme === 'dark' ? 'text-green-400' : 'text-green-600';
    const redColor = theme === 'dark' ? 'text-red-400' : 'text-red-600';
    const yellowColor = theme === 'dark' ? 'text-yellow-400' : 'text-yellow-600';

    if (isUnlimited) return <CheckCircleIcon className={`${iconClass} ${greenColor}`} />;
    if (isAtLimit) return <XCircleIcon className={`${iconClass} ${redColor}`} />;
    if (isNearLimit) return <ExclamationTriangleIcon className={`${iconClass} ${yellowColor}`} />;
    return <CheckCircleIcon className={`${iconClass} ${greenColor}`} />;
  };

  const getProgressBarColor = () => {
    if (isUnlimited) return 'bg-green-500';
    if (isAtLimit) return 'bg-red-500';
    if (isNearLimit) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className={`flex items-center justify-between text-sm ${className}`}>
      <div className="flex items-center space-x-2">
        <span className={theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}>{label}:</span>
        {!isUnlimited && (
          <div className={`w-16 rounded-full h-1.5 ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'}`}>
            <motion.div
              className={`h-1.5 rounded-full ${getProgressBarColor()}`}
              initial={{ width: 0 }}
              animate={{ width: `${Math.min(percentage, 100)}%` }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            />
          </div>
        )}
      </div>
      <div className="flex items-center space-x-1">
        <span className={`text-xs font-medium ${getStatusColor()}`}>
          {isUnlimited ? 'Unlimited' : `${current}/${limit}`}
        </span>
        {(isAtLimit || isNearLimit) && showUpgradeHint && (
          <button
            className={`text-xs underline ml-2 ${
              theme === 'dark'
                ? 'text-orange-400 hover:text-orange-300'
                : 'text-orange-600 hover:text-orange-700'
            }`}
            onClick={() => {
              router.push('/billing');
            }}
          >
            Upgrade
          </button>
        )}
      </div>
    </div>
  );
}
