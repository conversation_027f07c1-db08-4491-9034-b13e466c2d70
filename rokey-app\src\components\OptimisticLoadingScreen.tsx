'use client';

import React from 'react';
import {
  HomeIcon,
  KeyIcon,
  MapIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  ChartBarIcon,
  BeakerIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { useNavigationSafe } from '@/contexts/NavigationContext';
// Import skeleton components
import {
  DashboardSkeleton,
  MyModelsSkeleton,
  RoutingSetupSkeleton,
  TrainingSkeleton,
  AnalyticsSkeleton
} from '@/components/LoadingSkeleton';

interface OptimisticLoadingScreenProps {
  targetRoute: string | null;
}

// Route configurations for different loading screens
const routeConfigs = {
  '/dashboard': {
    title: 'Dashboard',
    subtitle: 'Loading overview & analytics...',
    icon: HomeIcon,
    color: 'text-blue-500',
    bgColor: 'bg-blue-50',
  },
  '/my-models': {
    title: 'My Models',
    subtitle: 'Loading API key management...',
    icon: KeyIcon,
    color: 'text-green-500',
    bgColor: 'bg-green-50',
  },
  '/playground': {
    title: 'Playground',
    subtitle: 'Loading model testing environment...',
    icon: BeakerIcon,
    color: 'text-orange-500',
    bgColor: 'bg-orange-50',
  },
  '/routing-setup': {
    title: 'Routing Setup',
    subtitle: 'Loading routing configuration...',
    icon: MapIcon,
    color: 'text-purple-500',
    bgColor: 'bg-purple-50',
  },
  '/logs': {
    title: 'Logs',
    subtitle: 'Loading request history...',
    icon: DocumentTextIcon,
    color: 'text-gray-500',
    bgColor: 'bg-gray-50',
  },
  '/training': {
    title: 'Prompt Engineering',
    subtitle: 'Loading custom prompts...',
    icon: AcademicCapIcon,
    color: 'text-indigo-500',
    bgColor: 'bg-indigo-50',
  },
  '/analytics': {
    title: 'Analytics',
    subtitle: 'Loading advanced insights...',
    icon: ChartBarIcon,
    color: 'text-pink-500',
    bgColor: 'bg-pink-50',
  },
};

export default function OptimisticLoadingScreen({ targetRoute }: OptimisticLoadingScreenProps) {
  const navigationContext = useNavigationSafe();
  const { clearNavigation } = navigationContext || { clearNavigation: () => {} };

  // Get route config or default
  const config = targetRoute ? routeConfigs[targetRoute as keyof typeof routeConfigs] : null;

  if (!config) {
    // Generic loading screen for unknown routes
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-pulse">
            <BeakerIcon className="w-8 h-8 text-gray-400" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Loading...</h2>
          <p className="text-gray-500">Please wait while we load the page</p>
        </div>
      </div>
    );
  }

  // Simple inline skeleton components
  const SimplePlaygroundSkeleton = () => (
    <div className="space-y-6 animate-fade-in">
      <div className="flex justify-between items-center">
        <div className="h-8 bg-gray-200 rounded animate-pulse w-1/4"></div>
        <div className="h-8 bg-gray-200 rounded animate-pulse w-20"></div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-1 space-y-3">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="h-16 bg-gray-200 rounded-lg animate-pulse"></div>
          ))}
        </div>
        <div className="lg:col-span-3">
          <div className="h-96 bg-gray-200 rounded-lg animate-pulse"></div>
        </div>
      </div>
    </div>
  );

  const SimpleLogsSkeleton = () => (
    <div className="space-y-6 animate-fade-in">
      <div className="flex justify-between items-center">
        <div className="h-8 bg-gray-200 rounded animate-pulse w-1/4"></div>
        <div className="flex space-x-3">
          <div className="h-8 bg-gray-200 rounded animate-pulse w-20"></div>
          <div className="h-8 bg-gray-200 rounded animate-pulse w-16"></div>
        </div>
      </div>
      <div className="space-y-3">
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="h-12 bg-gray-200 rounded-lg animate-pulse"></div>
        ))}
      </div>
    </div>
  );

  // Render the appropriate skeleton based on the target route
  const renderSkeleton = () => {
    switch (targetRoute) {
      case '/dashboard':
        return <DashboardSkeleton />;
      case '/my-models':
        return <MyModelsSkeleton />;
      case '/playground':
        return <SimplePlaygroundSkeleton />;
      case '/routing-setup':
        return <RoutingSetupSkeleton />;
      case '/logs':
        return <SimpleLogsSkeleton />;
      case '/training':
        return <TrainingSkeleton />;
      case '/analytics':
        return <AnalyticsSkeleton />;
      default:
        return <DashboardSkeleton />; // Fallback to dashboard skeleton
    }
  };

  return (
    <div className="relative">
      {/* Manual clear button (top right) */}
      <button
        onClick={clearNavigation}
        className="absolute top-4 right-4 z-10 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
        title="Cancel loading"
      >
        <XMarkIcon className="w-5 h-5" />
      </button>

      {/* Render the page-specific skeleton */}
      {renderSkeleton()}
    </div>
  );
}
