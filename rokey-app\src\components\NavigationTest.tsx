'use client';

import React, { useState, useEffect } from 'react';
import { useNavigation } from '@/contexts/NavigationContext';
import { useNavigationCache, useNavigationPerformance } from '@/hooks/useNavigationCache';
import { usePredictiveNavigation } from '@/hooks/usePredictiveNavigation';

export default function NavigationTest() {
  const [isClient, setIsClient] = useState(false);
  const [isDevelopment, setIsDevelopment] = useState(false);

  const {
    isNavigating,
    targetRoute,
    navigateOptimistically,
    isPageCached,
    navigationHistory
  } = useNavigation();

  const {
    metrics,
    getPerformanceInsights,
    cachedPages,
    cacheSize
  } = useNavigationCache();

  const {
    getAverageTime,
    getPerformanceGrade,
    navigationTimes
  } = useNavigationPerformance();

  const {
    predictions,
    insights,
    isLearning,
    clearPatterns
  } = usePredictiveNavigation();

  const [testResults, setTestResults] = useState<string[]>([]);

  // Handle hydration safely
  useEffect(() => {
    setIsClient(true);
    setIsDevelopment(process.env.NODE_ENV === 'development');
  }, []);

  const testRoutes = [
    '/dashboard',
    '/playground',
    '/my-models',
    '/logs',
    '/routing-setup',
    '/analytics'
  ];

  const runNavigationTest = async () => {
    setTestResults(['🧪 Starting navigation performance test...']);

    for (let i = 0; i < testRoutes.length; i++) {
      const route = testRoutes[i];
      const startTime = performance.now();

      setTestResults(prev => [...prev, `📍 Testing navigation to ${route}...`]);

      // Simulate rapid navigation
      navigateOptimistically(route);

      // Wait for navigation to complete
      await new Promise(resolve => setTimeout(resolve, 200));

      const endTime = performance.now();
      const duration = endTime - startTime;
      const isCached = isPageCached(route);

      setTestResults(prev => [...prev,
        `✅ ${route}: ${duration.toFixed(2)}ms ${isCached ? '(cached)' : '(fresh)'}`
      ]);
    }

    // Test rapid successive navigation
    setTestResults(prev => [...prev, '🔄 Testing rapid successive navigation...']);

    const rapidRoutes = ['/dashboard', '/playground', '/logs', '/dashboard'];
    for (const route of rapidRoutes) {
      navigateOptimistically(route);
      await new Promise(resolve => setTimeout(resolve, 50)); // Very rapid
    }

    setTestResults(prev => [...prev, '✅ Rapid navigation test completed']);

    // Show final metrics
    setTimeout(() => {
      setTestResults(prev => [...prev,
        `📊 Performance Grade: ${getPerformanceGrade()}`,
        `⚡ Average Time: ${getAverageTime().toFixed(2)}ms`,
        `💾 Cache Size: ${cacheSize} pages`,
        `📈 Cache Hit Rate: ${metrics.cacheHitRate.toFixed(1)}%`
      ]);
    }, 1000);
  };

  // Don't render anything until client-side hydration is complete
  if (!isClient || !isDevelopment) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm z-50">
      <h3 className="font-semibold text-gray-900 mb-2">Navigation Test</h3>
      
      <div className="space-y-2 mb-4">
        <div className="text-xs text-gray-600">
          Current: {targetRoute || 'None'}
        </div>
        <div className="text-xs text-gray-600">
          Status: {isNavigating ? '🔄 Navigating' : '✅ Ready'}
        </div>
        <div className="text-xs text-gray-600">
          History: {navigationHistory.length} pages
        </div>
        <div className="text-xs text-gray-600">
          Learning: {isLearning ? '🧠 Active' : '📚 Training'}
        </div>
        {predictions.length > 0 && (
          <div className="text-xs text-blue-600">
            Predictions: {predictions.slice(0, 2).join(', ')}
          </div>
        )}
      </div>

      <div className="space-y-2">
        <button
          onClick={runNavigationTest}
          className="w-full px-3 py-2 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
        >
          Run Test
        </button>
        <button
          onClick={clearPatterns}
          className="w-full px-3 py-2 bg-gray-500 text-white text-sm rounded hover:bg-gray-600 transition-colors"
        >
          Reset Learning
        </button>
      </div>

      {testResults.length > 0 && (
        <div className="mt-4 max-h-40 overflow-y-auto">
          <div className="text-xs space-y-1">
            {testResults.map((result, index) => (
              <div key={index} className="text-gray-700">
                {result}
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="mt-4 pt-2 border-t border-gray-200">
        <div className="text-xs text-gray-500">
          Performance Insights:
        </div>
        <div className="text-xs text-gray-600 mt-1">
          {getPerformanceInsights().map((insight, index) => (
            <div key={index}>• {insight}</div>
          ))}
        </div>

        {insights.length > 0 && (
          <>
            <div className="text-xs text-gray-500 mt-2">
              Learning Insights:
            </div>
            <div className="text-xs text-blue-600 mt-1">
              {insights.map((insight, index) => (
                <div key={index}>🧠 {insight}</div>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
}
