import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'About <PERSON><PERSON>Key - The Solo-Built AI Gateway Revolution | RouKey',
  description: 'Meet <PERSON><PERSON>, the solo developer who built Rou<PERSON>ey from frustration with expensive AI routing. Discover how one developer\'s late-night coding sessions created the ultimate AI gateway with unlimited requests to 300+ models.',
  keywords: '<PERSON><PERSON><PERSON><PERSON> founder, AI gateway creator, solo developer, AI routing platform, unlimited AI requests, multi-model AI, cost-effective AI solutions',
  openGraph: {
    title: 'About RouKey - The Solo-Built AI Gateway Revolution',
    description: 'From $500 monthly API bills to unlimited AI access - the inspiring story of how Rou<PERSON>ey was built by one developer who refused to accept broken solutions.',
    type: 'website',
    url: 'https://roukey.online/about',
    images: [
      {
        url: 'https://roukey.online/founder.jpg',
        width: 1200,
        height: 630,
        alt: '<PERSON><PERSON> - Founder of RouKey',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'About <PERSON><PERSON><PERSON>ey - The Solo-Built AI Gateway Revolution',
    description: 'Meet the solo developer who built <PERSON><PERSON><PERSON>ey to solve the frustrations of expensive AI routing and unlimited access to 300+ AI models.',
    images: ['https://roukey.online/founder.jpg'],
  },
  alternates: {
    canonical: 'https://roukey.online/about',
  },
};

export default function AboutLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
