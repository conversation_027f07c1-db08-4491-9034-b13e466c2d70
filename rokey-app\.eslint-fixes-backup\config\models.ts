export interface Model {
  id: string;
  name: string;
  provider: string; // This refers to the 'name' in the Provider interface
  description?: string;
  apiUrl?: string;
}

export interface Provider {
  id: string; // e.g., 'openai', 'google'
  name: string; // e.g., 'OpenAI', 'Google' - This should match ApiKeyProvider values for consistency
  apiBaseUrl?: string;
  models: Model[]; // Will be empty as DB is the source of truth for model instances
}

// Pruned list to only include providers that are expected to have models in the database
// based on PROVIDER_CONFIG from supabaseWriter.py
// UPDATED: This list is now curated to only show providers with direct RoKey backend integration.
// Users can access other models via the OpenRouter option.
export const llmProviders: Provider[] = [
  { id: "openai", name: "OpenAI", apiBaseUrl: "https://api.openai.com/v1/chat/completions", models: [] },
  { id: "google", name: "Google", apiBaseUrl: "https://generativelanguage.googleapis.com/v1beta/openai/chat/completions", models: [] },
  { id: "anthropic", name: "Anthropic", apiBaseUrl: "https://api.anthropic.com/v1/chat/completions", models: [] },
  { id: "deepseek", name: "DeepSeek", apiBaseUrl: "https://api.deepseek.com/chat/completions", models: [] },
  { id: "xai", name: "xAI (Grok)", apiBaseUrl: "https://api.x.ai/v1/chat/completions", models: [] },
  { id: "openrouter", name: "OpenRouter", apiBaseUrl: "https://openrouter.ai/api/v1/chat/completions", models: [] },
];

export const getAllModels = (): Model[] => {
  // This function might need re-evaluation if its direct use of llmProviders.models is critical elsewhere.
  // For now, it will return an empty array as all nested models arrays are empty.
  return llmProviders.reduce((acc, provider) => acc.concat(provider.models), [] as Model[]);
};

export const getModelById = (providerId: string, modelId: string): Model | undefined => {
  // This function will likely not find any models as the nested models arrays are empty.
  // If this function is used, it needs to be adapted to a DB query or API call.
  const provider = llmProviders.find(p => p.id === providerId);
  if (!provider) return undefined;
  return provider.models.find(m => m.id === modelId); // Will always be undefined with empty models arrays
}; 