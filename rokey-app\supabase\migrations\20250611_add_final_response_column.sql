-- Add final_response column to orchestration_executions table
-- Migration: 20250611_add_final_response_column.sql

-- Add final_response column to store the synthesized output
ALTER TABLE public.orchestration_executions 
ADD COLUMN IF NOT EXISTS final_response TEXT;

-- Add comment for the new column
COMMENT ON COLUMN public.orchestration_executions.final_response IS 'The final synthesized response combining all specialist outputs';

-- Add index for final_response for analytics (partial index for non-null values)
CREATE INDEX IF NOT EXISTS orchestration_executions_final_response_idx 
ON public.orchestration_executions(id) 
WHERE final_response IS NOT NULL;
