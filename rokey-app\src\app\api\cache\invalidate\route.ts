import { NextRequest, NextResponse } from 'next/server';
import { trainingDataCache } from '@/lib/cache/trainingCache';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, configId } = body;

    if (!type || !configId) {
      return NextResponse.json({ error: 'Missing type or configId' }, { status: 400 });
    }

    let invalidated = false;

    switch (type) {
      case 'training':
        invalidated = trainingDataCache.invalidate(configId);
        console.log(`[Cache Invalidation] Training cache invalidated for config: ${configId} (${invalidated ? 'success' : 'not cached'})`);
        break;
      default:
        return NextResponse.json({ error: 'Invalid cache type' }, { status: 400 });
    }

    return NextResponse.json({ 
      success: true, 
      invalidated,
      message: `${type} cache ${invalidated ? 'invalidated' : 'was not cached'} for config: ${configId}`
    });

  } catch (error) {
    console.error('[Cache Invalidation] Error:', error);
    return NextResponse.json({ error: 'Failed to invalidate cache' }, { status: 500 });
  }
}
