import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';

// Admin endpoint to populate model cost tiers
export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClientFromRequest(request);

    // Get all models from the database
    const { data: models, error: modelsError } = await supabase
      .from('models')
      .select('id, name, display_name, provider, input_token_price, output_token_price');

    if (modelsError) {
      console.error('[Cost Tiers] Error fetching models:', modelsError);
      return NextResponse.json({ error: 'Failed to fetch models' }, { status: 500 });
    }

    if (!models || models.length === 0) {
      return NextResponse.json({ error: 'No models found' }, { status: 404 });
    }

    // Calculate cost tiers for each model
    const costTiers = models.map((model: any) => {
      if (!model.input_token_price || !model.output_token_price) {
        return null; // Skip models without pricing
      }

      // Calculate average cost per 1000 tokens (50/50 input/output ratio)
      const avgCostPer1kTokens = (model.input_token_price * 500) + (model.output_token_price * 500);

      // Determine cost tier based on pricing thresholds
      let costTier: 'cheap' | 'moderate' | 'premium';
      let typicalQualityScore: number;
      let bestForTasks: string[];

      if (avgCostPer1kTokens <= 0.001) { // $0.001 per 1K tokens or less
        costTier = 'cheap';
        typicalQualityScore = 6.5;
        bestForTasks = ['chat', 'simple_questions', 'basic_writing'];
      } else if (avgCostPer1kTokens <= 0.01) { // $0.01 per 1K tokens or less
        costTier = 'moderate';
        typicalQualityScore = 7.5;
        bestForTasks = ['analysis', 'writing', 'explanations', 'moderate_coding'];
      } else {
        costTier = 'premium';
        typicalQualityScore = 8.5;
        bestForTasks = ['complex_reasoning', 'advanced_coding', 'research', 'creative_writing'];
      }

      // Calculate cost efficiency score (quality per dollar)
      const costEfficiencyScore = typicalQualityScore / avgCostPer1kTokens;

      return {
        model_id: model.id,
        provider: model.provider,
        cost_tier: costTier,
        avg_cost_per_1k_tokens: avgCostPer1kTokens,
        typical_quality_score: typicalQualityScore,
        best_for_tasks: bestForTasks,
        cost_efficiency_score: costEfficiencyScore
      };
    }).filter(Boolean); // Remove null entries

    // Insert cost tiers into database (upsert to handle duplicates)
    const { data: insertedTiers, error: insertError } = await supabase
      .from('model_cost_tiers')
      .upsert(costTiers, { 
        onConflict: 'model_id,provider',
        ignoreDuplicates: false 
      })
      .select();

    if (insertError) {
      console.error('[Cost Tiers] Error inserting cost tiers:', insertError);
      return NextResponse.json({ error: 'Failed to insert cost tiers' }, { status: 500 });
    }

    console.log(`[Cost Tiers] Successfully populated ${insertedTiers?.length || 0} cost tiers`);

    return NextResponse.json({
      success: true,
      message: `Successfully populated ${insertedTiers?.length || 0} model cost tiers`,
      tiers: insertedTiers
    });

  } catch (error) {
    console.error('[Cost Tiers] Unexpected error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET endpoint to view current cost tiers
export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClientFromRequest(request);

    const { data: costTiers, error } = await supabase
      .from('model_cost_tiers')
      .select('*')
      .order('cost_tier', { ascending: true })
      .order('cost_efficiency_score', { ascending: false });

    if (error) {
      console.error('[Cost Tiers] Error fetching cost tiers:', error);
      return NextResponse.json({ error: 'Failed to fetch cost tiers' }, { status: 500 });
    }

    // Group by cost tier for better visualization
    const groupedTiers = {
      cheap: costTiers?.filter(t => t.cost_tier === 'cheap') || [],
      moderate: costTiers?.filter(t => t.cost_tier === 'moderate') || [],
      premium: costTiers?.filter(t => t.cost_tier === 'premium') || []
    };

    return NextResponse.json({
      success: true,
      totalTiers: costTiers?.length || 0,
      tiers: groupedTiers,
      allTiers: costTiers
    });

  } catch (error) {
    console.error('[Cost Tiers] Unexpected error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
