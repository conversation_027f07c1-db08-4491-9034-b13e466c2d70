'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { usePathname } from 'next/navigation';
import { useRoutePrefetch } from './useRoutePrefetch';

interface NavigationPattern {
  from: string;
  to: string;
  frequency: number;
  lastUsed: number;
  avgTimeSpent: number;
}

interface UserBehavior {
  patterns: NavigationPattern[];
  sessionStartTime: number;
  totalNavigations: number;
  preferredRoutes: string[];
  timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';
}

const STORAGE_KEY = 'rokey_navigation_patterns';
const PATTERN_THRESHOLD = 2; // Minimum frequency to consider a pattern
const PREFETCH_DELAY = 1000; // Wait 1 second before prefetching predictions

export function usePredictiveNavigation() {
  const [userBehavior, setUserBehavior] = useState<UserBehavior | null>(null);
  const [predictions, setPredictions] = useState<string[]>([]);
  const pathname = usePathname();
  const { prefetchRoute } = useRoutePrefetch();
  const lastNavigationTime = useRef<number>(Date.now());
  const sessionStartTime = useRef<number>(Date.now());

  // Load user behavior patterns from localStorage
  useEffect(() => {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      try {
        const patterns = JSON.parse(stored);
        setUserBehavior({
          ...patterns,
          sessionStartTime: sessionStartTime.current
        });
      } catch (error) {
        console.warn('Failed to load navigation patterns:', error);
      }
    } else {
      // Initialize new user behavior
      setUserBehavior({
        patterns: [],
        sessionStartTime: sessionStartTime.current,
        totalNavigations: 0,
        preferredRoutes: [],
        timeOfDay: getTimeOfDay()
      });
    }
  }, []);

  // Get current time of day
  function getTimeOfDay(): 'morning' | 'afternoon' | 'evening' | 'night' {
    const hour = new Date().getHours();
    if (hour >= 6 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 17) return 'afternoon';
    if (hour >= 17 && hour < 21) return 'evening';
    return 'night';
  }

  // Track navigation patterns
  const trackNavigation = useCallback((fromRoute: string, toRoute: string) => {
    if (!userBehavior || fromRoute === toRoute) return;

    const now = Date.now();
    const timeSpent = now - lastNavigationTime.current;
    lastNavigationTime.current = now;

    setUserBehavior(prev => {
      if (!prev) return null;

      const updatedPatterns = [...prev.patterns];
      const existingPattern = updatedPatterns.find(
        p => p.from === fromRoute && p.to === toRoute
      );

      if (existingPattern) {
        existingPattern.frequency += 1;
        existingPattern.lastUsed = now;
        existingPattern.avgTimeSpent = 
          (existingPattern.avgTimeSpent + timeSpent) / 2;
      } else {
        updatedPatterns.push({
          from: fromRoute,
          to: toRoute,
          frequency: 1,
          lastUsed: now,
          avgTimeSpent: timeSpent
        });
      }

      // Update preferred routes
      const routeFrequency = new Map<string, number>();
      updatedPatterns.forEach(pattern => {
        routeFrequency.set(pattern.to, (routeFrequency.get(pattern.to) || 0) + pattern.frequency);
      });

      const preferredRoutes = Array.from(routeFrequency.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([route]) => route);

      const updated = {
        ...prev,
        patterns: updatedPatterns,
        totalNavigations: prev.totalNavigations + 1,
        preferredRoutes,
        timeOfDay: getTimeOfDay()
      };

      // Save to localStorage
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
      } catch (error) {
        console.warn('Failed to save navigation patterns:', error);
      }

      return updated;
    });
  }, [userBehavior]);

  // Generate predictions based on current route and patterns
  const generatePredictions = useCallback(() => {
    if (!userBehavior || !pathname) return [];

    const currentTime = getTimeOfDay();
    const recentPatterns = userBehavior.patterns
      .filter(p => p.from === pathname && p.frequency >= PATTERN_THRESHOLD)
      .sort((a, b) => {
        // Weight by frequency and recency
        const aScore = a.frequency * (1 + (Date.now() - a.lastUsed) / (1000 * 60 * 60 * 24));
        const bScore = b.frequency * (1 + (Date.now() - b.lastUsed) / (1000 * 60 * 60 * 24));
        return bScore - aScore;
      });

    // Get top 3 predictions
    const predictions = recentPatterns
      .slice(0, 3)
      .map(p => p.to);

    // Add time-based predictions (routes commonly visited at this time)
    const timeBasedRoutes = userBehavior.patterns
      .filter(p => {
        const patternTime = new Date(p.lastUsed).getHours();
        const currentHour = new Date().getHours();
        return Math.abs(patternTime - currentHour) <= 2; // Within 2 hours
      })
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, 2)
      .map(p => p.to);

    // Combine and deduplicate
    const combined = [...new Set([...predictions, ...timeBasedRoutes])];
    
    return combined.slice(0, 4); // Max 4 predictions
  }, [userBehavior, pathname]);

  // Update predictions when route changes
  useEffect(() => {
    if (userBehavior) {
      const newPredictions = generatePredictions();
      setPredictions(newPredictions);

      // Prefetch predicted routes after a delay
      const timer = setTimeout(() => {
        newPredictions.forEach((route, index) => {
          // Stagger prefetching to avoid overwhelming
          setTimeout(() => {
            prefetchRoute(route, { 
              priority: index === 0 ? 'high' : 'low',
              delay: index * 200 
            });
          }, index * 100);
        });
      }, PREFETCH_DELAY);

      return () => clearTimeout(timer);
    }
  }, [userBehavior, pathname, generatePredictions, prefetchRoute]);

  // Track navigation when pathname changes
  const previousPathname = useRef<string>(pathname);
  useEffect(() => {
    if (previousPathname.current && previousPathname.current !== pathname) {
      trackNavigation(previousPathname.current, pathname);
    }
    previousPathname.current = pathname;
  }, [pathname, trackNavigation]);

  // Get navigation insights
  const getInsights = useCallback(() => {
    if (!userBehavior) return [];

    const insights = [];
    
    // Most visited route
    if (userBehavior.preferredRoutes.length > 0) {
      insights.push(`Most visited: ${userBehavior.preferredRoutes[0]}`);
    }

    // Navigation frequency
    if (userBehavior.totalNavigations > 10) {
      insights.push(`${userBehavior.totalNavigations} total navigations this session`);
    }

    // Time-based patterns
    const currentTimePatterns = userBehavior.patterns.filter(p => {
      const patternTime = new Date(p.lastUsed).getHours();
      const currentHour = new Date().getHours();
      return Math.abs(patternTime - currentHour) <= 1;
    });

    if (currentTimePatterns.length > 0) {
      insights.push(`${currentTimePatterns.length} patterns match current time`);
    }

    return insights;
  }, [userBehavior]);

  // Clear patterns (for testing or reset)
  const clearPatterns = useCallback(() => {
    localStorage.removeItem(STORAGE_KEY);
    setUserBehavior({
      patterns: [],
      sessionStartTime: Date.now(),
      totalNavigations: 0,
      preferredRoutes: [],
      timeOfDay: getTimeOfDay()
    });
    setPredictions([]);
  }, []);

  return {
    predictions,
    userBehavior,
    insights: getInsights(),
    trackNavigation,
    clearPatterns,
    isLearning: userBehavior?.totalNavigations ? userBehavior.totalNavigations > 5 : false
  };
}

// Hook for intelligent route suggestions based on context
export function useContextualSuggestions() {
  const pathname = usePathname();
  const [suggestions, setSuggestions] = useState<Array<{
    route: string;
    reason: string;
    priority: 'high' | 'medium' | 'low';
  }>>([]);

  useEffect(() => {
    const contextualSuggestions = [];

    switch (pathname) {
      case '/dashboard':
        contextualSuggestions.push(
          { route: '/playground', reason: 'Test your models', priority: 'high' as const },
          { route: '/my-models', reason: 'Manage API keys', priority: 'medium' as const },
          { route: '/logs', reason: 'Check recent activity', priority: 'medium' as const }
        );
        break;
      
      case '/my-models':
        contextualSuggestions.push(
          { route: '/playground', reason: 'Test new configuration', priority: 'high' as const },
          { route: '/routing-setup', reason: 'Configure routing', priority: 'high' as const },
          { route: '/logs', reason: 'View API usage', priority: 'low' as const }
        );
        break;
      
      case '/playground':
        contextualSuggestions.push(
          { route: '/logs', reason: 'View request details', priority: 'medium' as const },
          { route: '/my-models', reason: 'Switch configuration', priority: 'medium' as const },
          { route: '/training', reason: 'Customize prompts', priority: 'low' as const }
        );
        break;
      
      case '/logs':
        contextualSuggestions.push(
          { route: '/playground', reason: 'Test similar requests', priority: 'high' as const },
          { route: '/analytics', reason: 'Detailed analysis', priority: 'medium' as const },
          { route: '/my-models', reason: 'Adjust configuration', priority: 'low' as const }
        );
        break;
    }

    setSuggestions(contextualSuggestions);
  }, [pathname]);

  return suggestions;
}
