import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/service-role';

export async function POST(req: NextRequest) {
  try {
    const { email, password, firstName, lastName, plan } = await req.json();

    if (!email || !password || !firstName || !lastName || !plan) {
      return NextResponse.json(
        { error: 'All fields are required' },
        { status: 400 }
      );
    }

    if (!['starter', 'professional', 'enterprise'].includes(plan)) {
      return NextResponse.json(
        { error: 'Invalid plan selected' },
        { status: 400 }
      );
    }

    const supabase = createServiceRoleClient();

    console.log('Creating paid user account for:', email, 'plan:', plan);

    // Create user account with admin privileges (don't sign them in)
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm email
      user_metadata: {
        full_name: `${firstName} ${lastName}`,
        first_name: firstName,
        last_name: lastName,
        plan: plan,
        payment_status: 'pending'
      },
    });

    console.log('User creation result:', {
      success: !!authData.user,
      userId: authData.user?.id,
      error: authError?.message
    });

    if (authError) {
      console.error('Error creating user:', authError);
      return NextResponse.json(
        { error: authError.message },
        { status: 400 }
      );
    }

    if (!authData.user) {
      return NextResponse.json(
        { error: 'Failed to create user' },
        { status: 500 }
      );
    }

    console.log('Creating user profile with pending status for user:', authData.user.id);

    // Create user profile with pending status
    const { error: profileError } = await supabase
      .from('user_profiles')
      .insert({
        id: authData.user.id,
        full_name: `${firstName} ${lastName}`,
        subscription_tier: plan,
        subscription_status: 'inactive', // Will be activated after payment
        user_status: 'pending', // Cannot sign in until payment is complete
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });

    console.log('Profile creation result:', {
      success: !profileError,
      error: profileError?.message
    });

    if (profileError) {
      console.error('Error creating user profile:', profileError);
      // Try to clean up the auth user if profile creation fails
      await supabase.auth.admin.deleteUser(authData.user.id);
      return NextResponse.json(
        { error: 'Failed to create user profile' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      user: {
        id: authData.user.id,
        email: authData.user.email,
        plan: plan,
        status: 'pending'
      },
      message: 'Account created successfully. Please complete payment to activate your account.',
    });

  } catch (error) {
    console.error('Error in paid signup:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET endpoint for testing
export async function GET() {
  return NextResponse.json({
    message: 'Paid signup endpoint',
    usage: 'POST /api/auth/paid-signup with { email, password, firstName, lastName, plan }'
  });
}
