// Debug package fallback for browser environments
// This prevents the webpack error when debug package is imported

// Create a no-op debug function that matches the debug package API exactly
function createDebugFunction(namespace) {
  // The main debug function - this is what gets called
  function debugFn() {
    // No-op in browser
  }

  // Add all required properties and methods
  debugFn.enabled = false;
  debugFn.namespace = namespace || '';
  debugFn.useColors = false;
  debugFn.color = 0;
  debugFn.diff = 0;
  debugFn.log = function() {};
  debugFn.extend = function(ns) {
    const newNamespace = namespace ? `${namespace}:${ns}` : ns;
    return createDebugFunction(newNamespace);
  };
  debugFn.destroy = function() {};

  return debugFn;
}

// Main debug function - this is what gets imported
function debug(namespace) {
  return createDebugFunction(namespace);
}

// Add static properties to the main debug function
debug.enabled = function() { return false; };
debug.names = [];
debug.skips = [];
debug.formatters = {};
debug.selectColor = function() { return 0; };
debug.humanize = function(val) { return val; };
debug.destroy = function() {};
debug.colors = [];
debug.log = function() {};
debug.extend = function(ns) { return createDebugFunction(ns); };

// For compatibility with different import styles
debug.default = debug;

// Export for both CommonJS and ES modules
module.exports = debug;
module.exports.default = debug;
