import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Rou<PERSON>ey Blog - AI Technology, Lean Startup & Cost-Effective Development',
  description: 'Expert insights on AI API gateways, multi-model routing, lean startup methodologies, and cost-effective development strategies. Learn from real-world experiences building scalable AI solutions.',
  keywords: [
    'AI API gateway',
    'multi-model routing',
    'lean startup',
    'bootstrap startup',
    'AI cost optimization',
    'API management',
    'AI development',
    'startup without funding',
    'MVP development',
    'AI infrastructure',
    'cost-effective AI',
    'AI model comparison',
    'SaaS development',
    'AI routing strategies',
    'technical blog'
  ],
  authors: [{ name: '<PERSON>', url: 'https://roukey.online' }],
  creator: '<PERSON><PERSON><PERSON><PERSON>',
  publisher: '<PERSON><PERSON><PERSON><PERSON>',
  openGraph: {
    title: 'RouKey Blog - AI Technology & Lean Startup Insights',
    description: 'Expert insights on AI API gateways, multi-model routing, lean startup methodologies, and cost-effective development strategies.',
    url: 'https://roukey.online/blog',
    siteName: 'Rou<PERSON>ey',
    type: 'website',
    images: [
      {
        url: 'https://roukey.online/og-blog.jpg',
        width: 1200,
        height: 630,
        alt: 'RouKey Blog - AI Technology & Startup Insights',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'RouKey Blog - AI Technology & Lean Startup Insights',
    description: 'Expert insights on AI API gateways, multi-model routing, lean startup methodologies, and cost-effective development strategies.',
    images: ['https://roukey.online/og-blog.jpg'],
    creator: '@roukey_ai',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  alternates: {
    canonical: 'https://roukey.online/blog',
  },
  category: 'Technology',
};

export default function BlogLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
