'use client';

import React from 'react';

export default function ManageKeysLoadingSkeleton() {
  return (
    <div className="space-y-8 animate-fade-in">
      {/* Header Skeleton */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="w-8 h-8 bg-gray-700 rounded animate-pulse"></div>
          <div>
            <div className="h-8 bg-gray-700 rounded w-48 mb-2 animate-pulse"></div>
            <div className="h-4 bg-gray-700 rounded w-64 animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Add New Key Form Skeleton */}
      <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6">
        <div className="h-6 bg-gray-700 rounded w-32 mb-6 animate-pulse"></div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Provider Selection */}
          <div>
            <div className="h-4 bg-gray-700 rounded w-20 mb-2 animate-pulse"></div>
            <div className="h-10 bg-gray-700 rounded animate-pulse"></div>
          </div>

          {/* Model Selection */}
          <div>
            <div className="h-4 bg-gray-700 rounded w-16 mb-2 animate-pulse"></div>
            <div className="h-10 bg-gray-700 rounded animate-pulse"></div>
          </div>

          {/* API Key Input */}
          <div>
            <div className="h-4 bg-gray-700 rounded w-20 mb-2 animate-pulse"></div>
            <div className="h-10 bg-gray-700 rounded animate-pulse"></div>
          </div>

          {/* Label Input */}
          <div>
            <div className="h-4 bg-gray-700 rounded w-12 mb-2 animate-pulse"></div>
            <div className="h-10 bg-gray-700 rounded animate-pulse"></div>
          </div>

          {/* Temperature Slider */}
          <div className="md:col-span-2">
            <div className="h-4 bg-gray-700 rounded w-24 mb-2 animate-pulse"></div>
            <div className="h-6 bg-gray-700 rounded animate-pulse"></div>
          </div>
        </div>

        {/* Save Button */}
        <div className="mt-6">
          <div className="h-10 bg-gray-700 rounded w-32 animate-pulse"></div>
        </div>
      </div>

      {/* Existing Keys Section Skeleton */}
      <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="h-6 bg-gray-700 rounded w-40 animate-pulse"></div>
          <div className="h-8 bg-gray-700 rounded w-24 animate-pulse"></div>
        </div>
        
        {/* Key Cards Skeleton */}
        <div className="space-y-4">
          {[1, 2, 3].map((index) => (
            <div key={index} className="border border-gray-700/50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gray-700 rounded-lg animate-pulse"></div>
                  <div>
                    <div className="h-5 bg-gray-700 rounded w-32 mb-2 animate-pulse"></div>
                    <div className="h-4 bg-gray-700 rounded w-48 animate-pulse"></div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-8 bg-gray-700 rounded w-20 animate-pulse"></div>
                  <div className="h-8 bg-gray-700 rounded w-16 animate-pulse"></div>
                  <div className="h-8 bg-gray-700 rounded w-16 animate-pulse"></div>
                </div>
              </div>

              {/* Key Details */}
              <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <div className="h-3 bg-gray-700 rounded w-16 mb-1 animate-pulse"></div>
                  <div className="h-4 bg-gray-700 rounded w-20 animate-pulse"></div>
                </div>
                <div>
                  <div className="h-3 bg-gray-700 rounded w-12 mb-1 animate-pulse"></div>
                  <div className="h-4 bg-gray-700 rounded w-16 animate-pulse"></div>
                </div>
                <div>
                  <div className="h-3 bg-gray-700 rounded w-20 mb-1 animate-pulse"></div>
                  <div className="h-4 bg-gray-700 rounded w-24 animate-pulse"></div>
                </div>
                <div>
                  <div className="h-3 bg-gray-700 rounded w-14 mb-1 animate-pulse"></div>
                  <div className="h-4 bg-gray-700 rounded w-18 animate-pulse"></div>
                </div>
              </div>

              {/* Assigned Roles */}
              <div className="mt-4">
                <div className="h-4 bg-gray-700 rounded w-28 mb-2 animate-pulse"></div>
                <div className="flex flex-wrap gap-2">
                  {[1, 2, 3].map((roleIndex) => (
                    <div key={roleIndex} className="h-6 bg-gray-700 rounded-full w-20 animate-pulse"></div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Custom Roles Section Skeleton */}
      <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="h-6 bg-gray-700 rounded w-36 animate-pulse"></div>
          <div className="h-8 bg-gray-700 rounded w-28 animate-pulse"></div>
        </div>

        <div className="space-y-3">
          {[1, 2].map((index) => (
            <div key={index} className="flex items-center justify-between p-3 border border-gray-700/50 rounded-lg">
              <div>
                <div className="h-4 bg-gray-700 rounded w-32 mb-1 animate-pulse"></div>
                <div className="h-3 bg-gray-700 rounded w-48 animate-pulse"></div>
              </div>
              <div className="h-8 bg-gray-700 rounded w-16 animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Compact loading skeleton for quick transitions
export function CompactManageKeysLoadingSkeleton() {
  return (
    <div className="space-y-6 animate-fade-in">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <div className="w-8 h-8 bg-gray-700 rounded animate-pulse"></div>
        <div>
          <div className="h-6 bg-gray-700 rounded w-40 mb-1 animate-pulse"></div>
          <div className="h-4 bg-gray-700 rounded w-56 animate-pulse"></div>
        </div>
      </div>

      {/* Quick Key Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {[1, 2, 3, 4].map((index) => (
          <div key={index} className="bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gray-700 rounded animate-pulse"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-700 rounded w-24 mb-1 animate-pulse"></div>
                <div className="h-3 bg-gray-700 rounded w-32 animate-pulse"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
