import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'About the Developer - <PERSON><PERSON> | Rou<PERSON>ey',
  description: 'Meet <PERSON><PERSON>, the solo developer behind RouKey. Learn about his journey from startup failure to building the ultimate AI routing platform.',
  keywords: '<PERSON><PERSON>, RouKey developer, AI gateway founder, solo developer, startup journey',
  openGraph: {
    title: 'About the Developer - Okoro <PERSON> | RouKey',
    description: 'Meet the solo developer behind <PERSON><PERSON><PERSON><PERSON> - from startup failure to building the ultimate AI routing platform.',
    type: 'website',
    url: 'https://roukey.online/about-developer',
    images: [
      {
        url: '/founder.jpg',
        width: 1200,
        height: 630,
        alt: '<PERSON><PERSON> - <PERSON>',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'About the Developer - Okoro <PERSON> | RouKey',
    description: 'Meet the solo developer behind <PERSON><PERSON><PERSON><PERSON> - from startup failure to building the ultimate AI routing platform.',
    images: ['/founder.jpg'],
  },
};

export default function AboutDeveloperLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
