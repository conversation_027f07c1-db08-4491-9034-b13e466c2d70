'use client';

import { useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';
import { prefetcher } from '@/utils/cacheStrategy';
import { usePerformanceOptimization } from '@/hooks/usePerformanceOptimization';

interface PerformanceTrackerProps {
  enableUserBehaviorTracking?: boolean;
  enableNavigationTracking?: boolean;
  enableInteractionTracking?: boolean;
}

export default function PerformanceTracker({
  enableUserBehaviorTracking = true,
  enableNavigationTracking = true,
  enableInteractionTracking = true
}: PerformanceTrackerProps) {
  const pathname = usePathname();
  const previousPathname = useRef<string>('');
  const navigationStartTime = useRef<number>(0);
  const { exportMetrics } = usePerformanceOptimization('PerformanceTracker');

  // Track navigation patterns
  useEffect(() => {
    if (!enableNavigationTracking) return;

    const currentPath = pathname;
    const previousPath = previousPathname.current;

    if (previousPath && previousPath !== currentPath) {
      // Track navigation pattern for intelligent prefetching
      prefetcher.trackNavigation(previousPath, currentPath);
      
      // Log navigation performance
      const navigationTime = performance.now() - navigationStartTime.current;
      console.log(`🔄 Navigation ${previousPath} → ${currentPath}: ${navigationTime.toFixed(2)}ms`);
      
      // Performance warnings
      if (navigationTime > 1000) {
        console.warn(`⚠️ Slow navigation detected: ${navigationTime.toFixed(2)}ms`);
      } else if (navigationTime < 300) {
        console.log(`✅ Fast navigation: ${navigationTime.toFixed(2)}ms`);
      }
    }

    previousPathname.current = currentPath;
    navigationStartTime.current = performance.now();
  }, [pathname, enableNavigationTracking]);

  // Track user interactions for prefetching optimization
  useEffect(() => {
    if (!enableInteractionTracking || typeof window === 'undefined') return;

    const trackInteraction = (event: Event) => {
      const target = event.target as HTMLElement;
      
      // Track link hovers for prefetching
      if (event.type === 'mouseenter' && target.tagName === 'A') {
        const href = target.getAttribute('href');
        if (href && href.startsWith('/')) {
          console.log(`🔗 Link hover detected: ${href}`);
          prefetcher.schedulePrefetch(href);
        }
      }
      
      // Track button clicks for UX optimization
      if (event.type === 'click' && (target.tagName === 'BUTTON' || target.closest('button'))) {
        const buttonText = target.textContent?.trim() || 'Unknown';
        console.log(`🖱️ Button clicked: ${buttonText}`);
        
        // Prefetch likely next pages based on button context
        if (buttonText.toLowerCase().includes('get started') || buttonText.toLowerCase().includes('sign up')) {
          prefetcher.schedulePrefetch('/auth/signup');
        } else if (buttonText.toLowerCase().includes('pricing')) {
          prefetcher.schedulePrefetch('/pricing');
        } else if (buttonText.toLowerCase().includes('features')) {
          prefetcher.schedulePrefetch('/features');
        }
      }
    };

    // Add event listeners
    document.addEventListener('mouseenter', trackInteraction, true);
    document.addEventListener('click', trackInteraction, true);

    return () => {
      document.removeEventListener('mouseenter', trackInteraction, true);
      document.removeEventListener('click', trackInteraction, true);
    };
  }, [enableInteractionTracking]);

  // Track user behavior patterns
  useEffect(() => {
    if (!enableUserBehaviorTracking || typeof window === 'undefined') return;

    let scrollTimeout: NodeJS.Timeout;
    let isScrolling = false;
    let scrollStartTime = 0;
    let maxScrollDepth = 0;

    const trackScrollBehavior = () => {
      if (!isScrolling) {
        isScrolling = true;
        scrollStartTime = performance.now();
      }

      // Track scroll depth
      const scrollDepth = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100;
      maxScrollDepth = Math.max(maxScrollDepth, scrollDepth);

      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        isScrolling = false;
        const scrollDuration = performance.now() - scrollStartTime;
        
        console.log(`📜 Scroll session: ${scrollDuration.toFixed(2)}ms, max depth: ${maxScrollDepth.toFixed(1)}%`);
        
        // Prefetch based on scroll behavior
        if (maxScrollDepth > 80) {
          // User scrolled far down, likely interested in content
          if (pathname === '/') {
            prefetcher.schedulePrefetch('/pricing');
            prefetcher.schedulePrefetch('/features');
          } else if (pathname === '/features') {
            prefetcher.schedulePrefetch('/auth/signup');
          }
        }
        
        maxScrollDepth = 0;
      }, 150);
    };

    // Track time spent on page
    const pageStartTime = performance.now();
    const trackPageTime = () => {
      const timeOnPage = performance.now() - pageStartTime;
      console.log(`⏱️ Time on ${pathname}: ${(timeOnPage / 1000).toFixed(2)}s`);
      
      // Prefetch based on engagement time
      if (timeOnPage > 10000) { // 10+ seconds indicates engagement
        if (pathname === '/') {
          prefetcher.schedulePrefetch('/auth/signup');
        } else if (pathname === '/pricing') {
          prefetcher.schedulePrefetch('/auth/signup');
        }
      }
    };

    // Add scroll listener
    window.addEventListener('scroll', trackScrollBehavior, { passive: true });
    
    // Track page visibility changes
    const handleVisibilityChange = () => {
      if (document.hidden) {
        trackPageTime();
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Track page unload
    const handleBeforeUnload = () => {
      trackPageTime();
      
      // Export performance metrics for debugging
      if (process.env.NODE_ENV === 'development') {
        exportMetrics();
      }
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      clearTimeout(scrollTimeout);
      window.removeEventListener('scroll', trackScrollBehavior);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      trackPageTime();
    };
  }, [pathname, enableUserBehaviorTracking, exportMetrics]);

  // Performance monitoring for Core Web Vitals
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        const lcp = lastEntry.startTime;
        
        console.log(`📊 LCP: ${lcp.toFixed(2)}ms`);
        
        if (lcp > 2500) {
          console.warn('⚠️ Poor LCP performance');
        } else if (lcp < 1200) {
          console.log('✅ Excellent LCP performance');
        }
      });
      
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (entry.processingStart && entry.startTime) {
            const fid = entry.processingStart - entry.startTime;
            console.log(`📊 FID: ${fid.toFixed(2)}ms`);

            if (fid > 100) {
              console.warn('⚠️ Poor FID performance');
            } else if (fid < 50) {
              console.log('✅ Excellent FID performance');
            }
          }
        });
      });
      
      fidObserver.observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift (CLS)
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        
        console.log(`📊 CLS: ${clsValue.toFixed(4)}`);
        
        if (clsValue > 0.25) {
          console.warn('⚠️ Poor CLS performance');
        } else if (clsValue < 0.1) {
          console.log('✅ Excellent CLS performance');
        }
      });
      
      clsObserver.observe({ entryTypes: ['layout-shift'] });

      return () => {
        lcpObserver.disconnect();
        fidObserver.disconnect();
        clsObserver.disconnect();
      };
    }
  }, []);

  // This component doesn't render anything
  return null;
}
