import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { PREDEFINED_ROLES, getRoleById } from '@/config/roles'; // For validation
import { hasFeatureAccess } from '@/lib/stripe-client';

interface RouteParams {
  params: Promise<{
    apiKeyId: string;
  }>;
}

// GET /api/keys/:apiKeyId/roles
// Lists all assigned roles for a specific API key.
export async function GET(request: NextRequest, { params }: RouteParams) {
  const supabase = createSupabaseServerClientFromRequest(request);
  const { apiKeyId } = await params;

  if (!apiKeyId) {
    return NextResponse.json({ error: 'API Key ID is required' }, { status: 400 });
  }
  // TODO: Milestone 13: Auth check - user owns the API key or its parent custom_config.
  // For now, we also need the user context if we were to enrich with their custom roles by name.
  // const { data: { user: authUser } } = await supabase.auth.getUser(); 

  try {
    const { data, error } = await supabase
      .from('api_key_role_assignments')
      .select('role_name, created_at') // Could select more if needed, like the full Role object by joining or mapping
      .eq('api_key_id', apiKeyId);

    if (error) {
      console.error('Supabase error fetching role assignments:', error);
      return NextResponse.json({ error: 'Failed to fetch role assignments', details: error.message }, { status: 500 });
    }

    // Enrich data: Attempt to get details for predefined roles.
    // For custom roles, we'd need to fetch them based on the user who owns the config to get name/description.
    // This GET endpoint is primarily for listing assigned role_names; the client can fetch full details if needed.
    const enrichedData = data.map(assignment => {
        const predefinedRoleDetails = getRoleById(assignment.role_name);
        // If it's not predefined, it might be a custom role. The client has the custom roles list.
        return {
            ...assignment,
            role_details: predefinedRoleDetails || { id: assignment.role_name, name: assignment.role_name, description: 'Custom role (details managed globally)' } 
        };
    });

    return NextResponse.json(enrichedData || [], { status: 200 });
  } catch (e: any) {
    console.error('Error in GET /api/keys/:apiKeyId/roles:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}

// POST /api/keys/:apiKeyId/roles
// Assigns a new role (predefined or user's global custom) to an API key.
export async function POST(request: NextRequest, { params }: RouteParams) {
  const supabase = createSupabaseServerClientFromRequest(request);
  const { apiKeyId } = await params;

  // Get authenticated user from session
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();
  if (sessionError || !session?.user) {
    console.error('Authentication error in POST role assignment:', sessionError);
    return NextResponse.json({ error: 'Unauthorized: You must be logged in to assign roles.' }, { status: 401 });
  }

  const authenticatedUserId = session.user.id;

  if (!apiKeyId) {
    return NextResponse.json({ error: 'API Key ID is required' }, { status: 400 });
  }

  try {
    const { role_name } = await request.json();

    if (!role_name || typeof role_name !== 'string') {
      return NextResponse.json({ error: 'Role name (role_id) is required and must be a string' }, { status: 400 });
    }

    // Fetch API key details and the user_id of the custom_api_config owner
    const { data: apiKeyRecord, error: apiKeyFetchError } = await supabase
      .from('api_keys')
      .select(`
        custom_api_config_id,
        custom_api_configs ( user_id )
      `)
      .eq('id', apiKeyId)
      .single();

    if (apiKeyFetchError || !apiKeyRecord) {
      console.error('API Key not found or error fetching details:', apiKeyFetchError);
      return NextResponse.json({ error: 'API Key not found or failed to fetch its details' }, { status: 404 });
    }

    // Ensure the fetched apiKeyRecord.custom_api_configs is not null and has a user_id
    // Supabase typing for nested selects can be tricky, so we ensure structure.
    const configOwnerUserId = (apiKeyRecord.custom_api_configs as unknown as { user_id: string })?.user_id;

    if (!configOwnerUserId) {
        console.error('Could not determine the owner of the Custom API Configuration for the API Key.');
        return NextResponse.json({ error: 'Could not determine the config owner for the API Key.' }, { status: 500 });
    }

    // Authorization: Check if the authenticated user owns the config to which this API key belongs.
    // This is a critical check that should ideally be part of RLS or a reusable middleware.
    // For now, implementing it directly here.
    // TEMPORARY: If configOwnerUserId was not found, but it's the placeholder user, this check is effectively bypassed.
    if (configOwnerUserId && authenticatedUserId !== configOwnerUserId) {
        console.warn(`User ${authenticatedUserId} attempted to assign role to API key ${apiKeyId} owned by user ${configOwnerUserId}.`);
        return NextResponse.json({ error: 'Forbidden. You do not own the configuration this API key belongs to.' }, { status: 403 });
    }

    // Check user's subscription tier for custom roles access
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('tier')
      .eq('user_id', authenticatedUserId)
      .eq('status', 'active')
      .single();

    const userTier = subscription?.tier || 'free';

    // Check if user has access to custom roles feature
    if (!hasFeatureAccess(userTier as any, 'custom_roles')) {
      return NextResponse.json({
        error: `Role assignment is not available on the ${userTier} plan. Please upgrade to assign roles to your API keys.`
      }, { status: 403 });
    }

    // Validate if the role_name is valid for this user
    const isPredefined = PREDEFINED_ROLES.some(r => r.id === role_name);
    let isUserCustomRole = false;
    if (!isPredefined) {
      const { data: customRole, error: customRoleError } = await supabase
        .from('user_custom_roles')
        .select('id')
        .eq('user_id', authenticatedUserId) // Role must belong to the authenticated user
        .eq('role_id', role_name)          // Match by the string role_id
        .maybeSingle(); // Use maybeSingle as it might not exist
      
      if (customRoleError) {
        console.error('Error checking for user custom role:', customRoleError);
        return NextResponse.json({ error: 'Error validating role.', details: customRoleError.message }, { status: 500 });
      }
      if (customRole) {
        isUserCustomRole = true;
      }
    }

    if (!isPredefined && !isUserCustomRole) {
      return NextResponse.json({ error: `Invalid role_name: ${role_name}. Not a predefined role or a custom role you own.` }, { status: 400 });
    }

    // The custom_api_config_id is already available from apiKeyRecord
    const { custom_api_config_id } = apiKeyRecord;

    const { data: assignmentData, error: assignmentError } = await supabase
      .from('api_key_role_assignments')
      .insert({
        api_key_id: apiKeyId,
        custom_api_config_id: custom_api_config_id,
        role_name: role_name,
      })
      .select()
      .single();

    if (assignmentError) {
      console.error('Supabase error assigning role:', assignmentError);
      if (assignmentError.code === '23505') { // unique_violation
        // The unique constraint `unique_api_key_role` on (api_key_id, role_name) should handle this primarily.
        // The constraint `unique_role_per_custom_config` on (custom_api_config_id, role_name) might be too restrictive if we allow multiple keys in one config to have the same *custom* role, but it makes sense for predefined roles like 'summarizer'.
        // Given roles are now more flexible, `unique_api_key_role` is the more important one.
        // If `unique_role_per_custom_config` is still active and causing issues for custom roles, it might need to be re-evaluated or removed for custom roles.
        if (assignmentError.message.includes('unique_api_key_role')){
            return NextResponse.json({ error: 'This API key already has this role assigned.', details: assignmentError.message }, { status: 409 });
        }
        // This constraint `unique_role_per_custom_config` was designed when roles were simpler.
        // It means a role_name (e.g., 'translator') can only be assigned to ONE key within a single Custom API Configuration.
        // This might still be desired behavior for predefined roles to avoid ambiguity.
        // For custom roles, a user might want to assign their `my_special_role` to multiple keys in the same config.
        // This needs careful thought. For now, we respect existing constraints.
        if (assignmentError.message.includes('unique_role_per_custom_config')){
            return NextResponse.json({ error: 'This role is already assigned to another API key in this Custom Model (config). Check unique_role_per_custom_config constraint.', details: assignmentError.message }, { status: 409 });
        }
         return NextResponse.json({ error: 'Failed to assign role: This role may already be assigned in a way that violates a uniqueness constraint.', details: assignmentError.message, code: assignmentError.code }, { status: 409 });
      }
      return NextResponse.json({ error: 'Failed to assign role to API key', details: assignmentError.message }, { status: 500 });
    }

    return NextResponse.json(assignmentData, { status: 201 });
  } catch (e: any) {
    console.error('Error in POST /api/keys/:apiKeyId/roles:', e);
    if (e.name === 'SyntaxError') { // Or check for specific JSON parsing error type
        return NextResponse.json({ error: 'Invalid request body: Malformed JSON.' }, { status: 400 });
    }
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
} 