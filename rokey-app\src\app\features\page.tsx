'use client';

import { motion } from 'framer-motion';
import {
  BoltIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  CpuChipIcon,
  ArrowPathIcon,
  CloudIcon,
  CodeBracketIcon,
  LightBulbIcon,
  RocketLaunchIcon,
  CogIcon,
  SparklesIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  DocumentTextIcon,
  BeakerIcon,
  AcademicCapIcon,
  CheckCircleIcon,
  StarIcon,
  KeyIcon,
  CircleStackIcon,
  GlobeAltIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import LandingNavbar from '@/components/landing/LandingNavbar';
import Footer from '@/components/landing/Footer';
import InstantLink from '@/components/ui/InstantLink';
import EnhancedGridBackground from '@/components/landing/EnhancedGridBackground';

// Core Features - RouKey's Main Capabilities
const coreFeatures = [
  {
    icon: SparklesIcon,
    title: "Smart AI Routing",
    description: "Intelligent request classification and automatic routing to the optimal AI model for each task type.",
    features: ["Role-based routing", "Automatic model selection", "Real-time optimization", "Seamless provider switching"]
  },
  {
    icon: UserGroupI<PERSON>,
    title: "Multi-Role Orchestration",
    description: "Coordinate multiple AI agents with different specializations to handle complex workflows.",
    features: ["Sequential workflows", "Supervisor coordination", "Hierarchical task management", "Memory persistence"]
  },
  {
    icon: CurrencyDollarIcon,
    title: "Cost Optimization",
    description: "Reduce AI costs by up to 70% through intelligent routing and semantic caching.",
    features: ["Smart provider selection", "Semantic caching", "Usage analytics", "Cost tracking"]
  }
];

// Advanced Features
const advancedFeatures = [
  {
    icon: CircleStackIcon,
    title: "Semantic Caching",
    description: "Advanced caching with Jina embeddings to reduce redundant API calls and improve response times."
  },
  {
    icon: DocumentTextIcon,
    title: "Knowledge Base Integration",
    description: "Upload documents and let RouKey enhance responses with your custom knowledge base."
  },
  {
    icon: ChartBarIcon,
    title: "Analytics & Monitoring",
    description: "Comprehensive analytics dashboard with request tracking, cost analysis, and performance metrics."
  },
  {
    icon: ShieldCheckIcon,
    title: "Enterprise Security",
    description: "BYOK architecture ensures your API keys and data remain secure and under your control."
  },
  {
    icon: GlobeAltIcon,
    title: "Global Provider Support",
    description: "Support for 15+ AI providers including OpenAI, Anthropic, Google, Groq, and more."
  },
  {
    icon: ClockIcon,
    title: "Async Processing",
    description: "Handle long-running tasks with async endpoints and webhook notifications."
  }
];



export default function FeaturesPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#040716] to-[#1C051C] relative overflow-hidden">
      {/* Enhanced Grid Background */}
      <EnhancedGridBackground
        gridSize={45}
        opacity={0.06}
        color="#ff6b35"
        variant="premium"
        animated={true}
        className="absolute inset-0"
      />

      {/* Center Gradient */}
      <div className="absolute inset-0 bg-gradient-radial from-[#1C051C]/30 via-transparent to-transparent"></div>

      <LandingNavbar />

      <main className="pt-20 relative z-10">
        {/* Hero Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-20"
            >
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6">
                Smart AI Routing.
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]">
                  {" "}Unlimited Scale.
                </span>
              </h1>
              <p className="text-xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
                The only AI gateway with intelligent multi-role orchestration and smart cost optimization.
                Route requests to the perfect model every time while saving up to 70% on AI costs.
              </p>

              {/* Feature Pills */}
              <div className="flex flex-wrap justify-center gap-4 mb-16">
                <div className="bg-white/10 backdrop-blur-sm px-6 py-3 rounded-full border border-white/20">
                  <span className="text-white font-medium">🎯 Intelligent Role Detection</span>
                </div>
                <div className="bg-white/10 backdrop-blur-sm px-6 py-3 rounded-full border border-white/20">
                  <span className="text-white font-medium">🔄 Multi-Agent Workflows</span>
                </div>
                <div className="bg-white/10 backdrop-blur-sm px-6 py-3 rounded-full border border-white/20">
                  <span className="text-white font-medium">💰 70% Cost Savings</span>
                </div>
              </div>
            </motion.div>

            {/* Core Features Section */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-20">
              {coreFeatures.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10 hover:border-white/20 transition-all duration-300 group"
                >
                  <div className="flex items-center mb-6">
                    <div className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-3 rounded-xl mr-4">
                      <feature.icon className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white">{feature.title}</h3>
                  </div>
                  <p className="text-gray-300 mb-6 leading-relaxed">{feature.description}</p>
                  <ul className="space-y-2">
                    {feature.features.map((item, idx) => (
                      <li key={idx} className="flex items-center text-gray-400">
                        <CheckCircleIcon className="h-4 w-4 text-[#ff6b35] mr-2 flex-shrink-0" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Advanced Features Section */}
        <section className="py-20 bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Enterprise-Ready Features
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Production-grade capabilities designed for high-performance AI applications
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {advancedFeatures.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 hover:border-[#ff6b35]/30 transition-all duration-300 group hover:bg-white/10"
                >
                  <div className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] p-3 rounded-lg w-fit mb-4">
                    <feature.icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-3">{feature.title}</h3>
                  <p className="text-gray-300 leading-relaxed">{feature.description}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Optimize Your AI Costs?
              </h2>
              <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
                Join developers who've reduced their AI costs by 70% while gaining access to intelligent multi-role orchestration that no other gateway offers.
              </p>

              {/* Feature highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                  <SparklesIcon className="w-8 h-8 text-[#ff6b35] mx-auto mb-3" />
                  <div className="text-white font-semibold">Multi-Role Orchestration</div>
                  <div className="text-gray-400 text-sm">Unique to RouKey</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                  <CurrencyDollarIcon className="w-8 h-8 text-[#ff6b35] mx-auto mb-3" />
                  <div className="text-white font-semibold">Smart Cost Optimization</div>
                  <div className="text-gray-400 text-sm">Save up to 70%</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                  <BoltIcon className="w-8 h-8 text-[#ff6b35] mx-auto mb-3" />
                  <div className="text-white font-semibold">Unlimited Requests</div>
                  <div className="text-gray-400 text-sm">No limits, ever</div>
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <InstantLink
                  href="/pricing"
                  className="inline-flex items-center px-12 py-5 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-bold rounded-2xl hover:shadow-2xl hover:scale-105 transition-all duration-200 text-xl"
                >
                  <SparklesIcon className="mr-3 h-6 w-6" />
                  Start Building Now
                </InstantLink>
                <InstantLink
                  href="/routing-strategies"
                  className="inline-flex items-center px-12 py-5 bg-white/10 backdrop-blur-sm text-white font-bold rounded-2xl border-2 border-white/20 hover:border-white/40 hover:bg-white/20 transition-all duration-200 text-xl"
                >
                  <CogIcon className="mr-3 h-6 w-6" />
                  Explore Routing
                </InstantLink>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
