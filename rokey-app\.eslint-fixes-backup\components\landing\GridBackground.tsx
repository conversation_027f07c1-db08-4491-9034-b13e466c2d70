'use client';

import { useEffect, useRef } from 'react';

interface GridBackgroundProps {
  className?: string;
  gridSize?: number;
  opacity?: number;
  color?: string;
  animated?: boolean;
}

export default function GridBackground({ 
  className = '', 
  gridSize = 40,
  opacity = 0.1,
  color = '#000000',
  animated = false
}: GridBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect();
      canvas.width = rect.width * window.devicePixelRatio;
      canvas.height = rect.height * window.devicePixelRatio;
      ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
      canvas.style.width = rect.width + 'px';
      canvas.style.height = rect.height + 'px';
    };

    const drawGrid = (offset = 0) => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      const rect = canvas.getBoundingClientRect();
      const width = rect.width;
      const height = rect.height;

      ctx.strokeStyle = color;
      ctx.globalAlpha = opacity;
      ctx.lineWidth = 1;

      // Draw vertical lines
      for (let x = (offset % gridSize); x < width; x += gridSize) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
      }

      // Draw horizontal lines
      for (let y = (offset % gridSize); y < height; y += gridSize) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(width, y);
        ctx.stroke();
      }
    };

    let animationId: number;
    const startTime = Date.now();

    const animate = () => {
      if (animated) {
        const elapsed = Date.now() - startTime;
        const offset = (elapsed * 0.02) % gridSize; // Slow movement
        drawGrid(offset);
        animationId = requestAnimationFrame(animate);
      } else {
        drawGrid();
      }
    };

    const handleResize = () => {
      resizeCanvas();
      animate();
    };

    resizeCanvas();
    animate();

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [gridSize, opacity, color, animated]);

  return (
    <canvas
      ref={canvasRef}
      className={`absolute inset-0 pointer-events-none ${className}`}
      style={{ zIndex: 1 }}
    />
  );
}
