import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'RouKey Routing Strategies - Advanced AI Gateway Routing | RouKey',
  description: 'Explore RouKey\'s advanced routing strategies: Multi-Role Orchestration, Complexity-Based Routing, Smart Cost Optimization, A/B Testing, and more. Choose the perfect strategy for your AI applications.',
  keywords: 'AI routing strategies, multi-role orchestration, complexity routing, cost optimization, A/B testing, intelligent routing, AI gateway strategies',
  openGraph: {
    title: 'RouKey Routing Strategies - Advanced AI Gateway Routing',
    description: 'Discover RouKey\'s revolutionary routing strategies including Multi-Role Orchestration, Smart Cost Optimization, and intelligent complexity-based routing.',
    type: 'website',
    url: 'https://roukey.online/routing-strategies',
  },
  alternates: {
    canonical: 'https://roukey.online/routing-strategies',
  },
};

export default function RoutingStrategiesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
