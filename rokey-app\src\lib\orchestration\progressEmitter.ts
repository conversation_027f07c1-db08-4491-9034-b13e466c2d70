/**
 * Rou<PERSON>ey Orchestration Progress Emitter
 * 
 * Captures orchestration progress events and emits them to the frontend
 * for real-time status updates with proper RouKey branding.
 */

import { EventEmitter } from 'events';

export interface OrchestrationProgressEvent {
  id: string;
  timestamp: string;
  type: 'classification_start' | 'classification_complete' | 'role_selection' | 'workflow_selection' | 
        'agent_creation_start' | 'agent_creation_complete' | 'supervisor_init' | 'task_planning' |
        'agent_work_start' | 'agent_work_complete' | 'supervisor_synthesis' | 'orchestration_complete' | 'error';
  message: string;
  data?: any;
  colorIndex?: number; // For cycling through colors
}

class OrchestrationProgressEmitter extends EventEmitter {
  private static instance: OrchestrationProgressEmitter;
  private currentColorIndex = 0;
  private readonly colors = [
    'blue',    // Classification
    'purple',  // Role selection
    'indigo',  // Workflow selection
    'cyan',    // Agent creation
    'teal',    // Supervisor init
    'green',   // Task planning
    'yellow',  // Agent work
    'orange',  // Synthesis
    'emerald', // Completion
    'red'      // Error
  ];

  private constructor() {
    super();
  }

  static getInstance(): OrchestrationProgressEmitter {
    if (!OrchestrationProgressEmitter.instance) {
      OrchestrationProgressEmitter.instance = new OrchestrationProgressEmitter();
    }
    return OrchestrationProgressEmitter.instance;
  }

  private getNextColor(): string {
    const color = this.colors[this.currentColorIndex % this.colors.length];
    this.currentColorIndex++;
    return color;
  }

  emitProgress(type: OrchestrationProgressEvent['type'], message: string, data?: any): void {
    const event: OrchestrationProgressEvent = {
      id: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
      type,
      message,
      data,
      colorIndex: this.currentColorIndex
    };

    // Increment color index for visual differentiation
    this.currentColorIndex++;

    console.log(`[Progress Emitter] ${message}`);
    this.emit('progress', event);
  }

  // Convenience methods for different progress types
  classificationStart(): void {
    this.emitProgress('classification_start', '🔍 Multi-role task detected by RouKey\'s Classifier');
  }

  classificationComplete(roles: string[], threshold: number): void {
    this.emitProgress('classification_complete', `✅ Detected ${roles.length} roles: ${roles.join(', ')}`, { roles, threshold });
  }

  roleSelectionComplete(selectedRoles: string[], filteredRoles: string[]): void {
    this.emitProgress('role_selection', `🎯 Selected ${selectedRoles.length} roles for orchestration`, { selectedRoles, filteredRoles });
  }

  workflowSelectionComplete(workflowType: string, reasoning: string): void {
    this.emitProgress('workflow_selection', `🏗️ RouKey Multi-Role System selected ${workflowType} workflow`, { workflowType, reasoning });
  }

  agentCreationStart(): void {
    this.emitProgress('agent_creation_start', '🤖 Creating specialized agents...');
  }

  agentCreationComplete(agents: Array<{ role: string, apiKey: string }>): void {
    this.emitProgress('agent_creation_complete', `✅ Created ${agents.length} specialized agents`, { agents });
  }

  supervisorInitStart(): void {
    this.emitProgress('supervisor_init', '👑 Initializing supervisor coordination...');
  }

  supervisorInitComplete(supervisorRole: string): void {
    this.emitProgress('supervisor_init', '✅ Supervisor ready for coordination', { supervisorRole });
  }

  taskPlanningStart(): void {
    this.emitProgress('task_planning', '📋 Planning task distribution...');
  }

  taskPlanningComplete(plan: string): void {
    this.emitProgress('task_planning', '✅ Task distribution planned', { plan });
  }

  agentWorkStart(role: string, task: string): void {
    this.emitProgress('agent_work_start', `🚀 ${role} agent starting work...`, { role, task });
  }

  agentWorkComplete(role: string, result: string): void {
    this.emitProgress('agent_work_complete', `✅ ${role} agent completed work`, { role, result });
  }

  supervisorSynthesisStart(): void {
    this.emitProgress('supervisor_synthesis', '🔄 Supervisor synthesizing results...');
  }

  supervisorSynthesisComplete(synthesis: string): void {
    this.emitProgress('supervisor_synthesis', '✅ Final synthesis complete', { synthesis });
  }

  orchestrationComplete(result: any): void {
    this.emitProgress('orchestration_complete', '🎉 RouKey Multi-Role orchestration complete!', { result });
  }

  error(step: string, error: string): void {
    this.emitProgress('error', `❌ Error in ${step}: ${error}`, { step, error });
  }

  // Reset color index for new orchestration sessions
  resetColorIndex(): void {
    this.currentColorIndex = 0;
  }

  // Get current color for UI
  getCurrentColor(): string {
    return this.colors[(this.currentColorIndex - 1) % this.colors.length] || 'blue';
  }
}

export default OrchestrationProgressEmitter;
