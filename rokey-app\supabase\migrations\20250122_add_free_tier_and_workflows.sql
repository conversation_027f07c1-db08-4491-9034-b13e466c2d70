-- Migration: Add Free Tier Support and Workflow Automation Tables
-- Date: 2025-01-22
-- Description: Updates subscription system to support free tier and adds workflow automation infrastructure

-- 1. Update subscription tiers to include 'free'
-- First, let's check if we need to update any existing constraints
DO $$
BEGIN
    -- Update any existing check constraints on subscription_tier
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name LIKE '%subscription_tier%' 
        AND table_name = 'user_profiles'
    ) THEN
        ALTER TABLE user_profiles DROP CONSTRAINT IF EXISTS user_profiles_subscription_tier_check;
    END IF;
    
    -- Add updated constraint that includes 'free'
    ALTER TABLE user_profiles ADD CONSTRAINT user_profiles_subscription_tier_check 
    CHECK (subscription_tier IN ('free', 'starter', 'professional', 'enterprise'));
END $$;

-- 2. Update subscriptions table to handle free tier
-- Add a column to track if this is a free tier (no Stripe subscription)
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS is_free_tier BOOLEAN DEFAULT FALSE;

-- 3. Create workflows table for workflow automation
CREATE TABLE IF NOT EXISTS workflows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    
    -- Workflow definition
    nodes JSONB NOT NULL DEFAULT '[]'::jsonb,
    edges JSONB NOT NULL DEFAULT '[]'::jsonb,
    
    -- Trigger configuration
    triggers JSONB NOT NULL DEFAULT '[]'::jsonb,
    
    -- Status and settings
    is_active BOOLEAN DEFAULT FALSE,
    is_template BOOLEAN DEFAULT FALSE,
    template_category TEXT,
    
    -- Execution settings
    max_execution_time_seconds INTEGER DEFAULT 300,
    retry_count INTEGER DEFAULT 3,
    
    -- Metadata
    tags TEXT[] DEFAULT '{}',
    version INTEGER DEFAULT 1,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    last_executed_at TIMESTAMPTZ,
    
    -- Constraints
    CONSTRAINT workflows_name_length CHECK (length(name) >= 1 AND length(name) <= 100),
    CONSTRAINT workflows_max_execution_time CHECK (max_execution_time_seconds > 0 AND max_execution_time_seconds <= 3600)
);

-- 4. Create workflow executions table
CREATE TABLE IF NOT EXISTS workflow_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID REFERENCES workflows(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- Execution details
    status TEXT NOT NULL CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled', 'timeout')),
    trigger_type TEXT NOT NULL, -- 'manual', 'webhook', 'schedule', 'api'
    trigger_data JSONB,
    
    -- Input/Output
    input_data JSONB,
    output_data JSONB,
    
    -- Error handling
    error_message TEXT,
    error_details JSONB,
    
    -- Performance metrics
    execution_time_ms INTEGER,
    nodes_executed INTEGER DEFAULT 0,
    nodes_total INTEGER DEFAULT 0,
    
    -- Timestamps
    started_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    completed_at TIMESTAMPTZ,
    
    -- Metadata
    execution_context JSONB DEFAULT '{}'::jsonb
);

-- 5. Create workflow execution logs table
CREATE TABLE IF NOT EXISTS workflow_execution_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    execution_id UUID REFERENCES workflow_executions(id) ON DELETE CASCADE NOT NULL,
    workflow_id UUID REFERENCES workflows(id) ON DELETE CASCADE NOT NULL,
    
    -- Log details
    node_id TEXT NOT NULL,
    node_type TEXT NOT NULL,
    log_level TEXT NOT NULL CHECK (log_level IN ('debug', 'info', 'warn', 'error')),
    message TEXT NOT NULL,
    
    -- Additional data
    data JSONB,
    duration_ms INTEGER,
    
    -- Timestamp
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- 6. Create workflow usage tracking table
CREATE TABLE IF NOT EXISTS workflow_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- Monthly usage tracking
    month_year TEXT NOT NULL, -- Format: 'YYYY-MM'
    
    -- Usage counters
    executions_count INTEGER DEFAULT 0,
    active_workflows_count INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    
    -- Ensure one record per user per month
    UNIQUE(user_id, month_year)
);

-- 7. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_workflows_user_id ON workflows(user_id);
CREATE INDEX IF NOT EXISTS idx_workflows_is_active ON workflows(is_active);
CREATE INDEX IF NOT EXISTS idx_workflows_is_template ON workflows(is_template);
CREATE INDEX IF NOT EXISTS idx_workflows_template_category ON workflows(template_category);

CREATE INDEX IF NOT EXISTS idx_workflow_executions_workflow_id ON workflow_executions(workflow_id);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_user_id ON workflow_executions(user_id);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_status ON workflow_executions(status);
CREATE INDEX IF NOT EXISTS idx_workflow_executions_started_at ON workflow_executions(started_at);

CREATE INDEX IF NOT EXISTS idx_workflow_execution_logs_execution_id ON workflow_execution_logs(execution_id);
CREATE INDEX IF NOT EXISTS idx_workflow_execution_logs_workflow_id ON workflow_execution_logs(workflow_id);
CREATE INDEX IF NOT EXISTS idx_workflow_execution_logs_created_at ON workflow_execution_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_workflow_usage_user_id ON workflow_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_workflow_usage_month_year ON workflow_usage(month_year);

-- 8. Create RLS (Row Level Security) policies
ALTER TABLE workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_execution_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_usage ENABLE ROW LEVEL SECURITY;

-- Workflows policies
CREATE POLICY "Users can view their own workflows" ON workflows
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own workflows" ON workflows
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own workflows" ON workflows
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own workflows" ON workflows
    FOR DELETE USING (auth.uid() = user_id);

-- Template workflows are publicly readable
CREATE POLICY "Template workflows are publicly readable" ON workflows
    FOR SELECT USING (is_template = true);

-- Workflow executions policies
CREATE POLICY "Users can view their own workflow executions" ON workflow_executions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own workflow executions" ON workflow_executions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own workflow executions" ON workflow_executions
    FOR UPDATE USING (auth.uid() = user_id);

-- Workflow execution logs policies
CREATE POLICY "Users can view logs for their workflow executions" ON workflow_execution_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM workflow_executions 
            WHERE workflow_executions.id = workflow_execution_logs.execution_id 
            AND workflow_executions.user_id = auth.uid()
        )
    );

CREATE POLICY "System can insert workflow execution logs" ON workflow_execution_logs
    FOR INSERT WITH CHECK (true); -- System service will handle this

-- Workflow usage policies
CREATE POLICY "Users can view their own workflow usage" ON workflow_usage
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can manage workflow usage" ON workflow_usage
    FOR ALL USING (true); -- System service will handle this

-- 9. Create functions for workflow management
CREATE OR REPLACE FUNCTION update_workflow_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER workflows_updated_at_trigger
    BEFORE UPDATE ON workflows
    FOR EACH ROW
    EXECUTE FUNCTION update_workflow_updated_at();

-- 10. Create function to increment workflow usage
CREATE OR REPLACE FUNCTION increment_workflow_usage(
    p_user_id UUID,
    p_execution_count INTEGER DEFAULT 1
)
RETURNS void AS $$
DECLARE
    current_month TEXT := to_char(now(), 'YYYY-MM');
BEGIN
    INSERT INTO workflow_usage (user_id, month_year, executions_count)
    VALUES (p_user_id, current_month, p_execution_count)
    ON CONFLICT (user_id, month_year)
    DO UPDATE SET 
        executions_count = workflow_usage.executions_count + p_execution_count,
        updated_at = now();
END;
$$ LANGUAGE plpgsql;

-- 11. Create function to get user workflow limits based on subscription tier
CREATE OR REPLACE FUNCTION get_user_workflow_limits(p_user_id UUID)
RETURNS TABLE (
    max_active_workflows INTEGER,
    max_executions_per_month INTEGER,
    can_use_ai_nodes BOOLEAN,
    can_create_custom_integrations BOOLEAN
) AS $$
DECLARE
    user_tier TEXT;
BEGIN
    -- Get user's subscription tier
    SELECT COALESCE(up.subscription_tier, 'free') INTO user_tier
    FROM user_profiles up
    WHERE up.id = p_user_id;
    
    -- Return limits based on tier
    CASE user_tier
        WHEN 'free' THEN
            RETURN QUERY SELECT 3, 100, false, false;
        WHEN 'starter' THEN
            RETURN QUERY SELECT 10, 1000, true, false;
        WHEN 'professional' THEN
            RETURN QUERY SELECT 50, 10000, true, false;
        WHEN 'enterprise' THEN
            RETURN QUERY SELECT 999999, 999999, true, true;
        ELSE
            -- Default to free tier limits
            RETURN QUERY SELECT 3, 100, false, false;
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- 12. Insert default free tier for existing users without subscriptions
INSERT INTO user_profiles (id, subscription_tier, created_at, updated_at)
SELECT 
    au.id,
    'free',
    now(),
    now()
FROM auth.users au
LEFT JOIN user_profiles up ON au.id = up.id
WHERE up.id IS NULL
ON CONFLICT (id) DO NOTHING;

-- 13. Create some sample workflow templates
INSERT INTO workflows (
    user_id,
    name,
    description,
    nodes,
    edges,
    triggers,
    is_template,
    template_category,
    is_active
) VALUES 
(
    '00000000-0000-0000-0000-000000000000', -- System user for templates
    'Welcome Email Automation',
    'Send a welcome email when a new user signs up via webhook',
    '[
        {
            "id": "trigger-1",
            "type": "webhook",
            "name": "User Signup Webhook",
            "config": {"path": "/webhook/user-signup"},
            "position": {"x": 100, "y": 100}
        },
        {
            "id": "email-1",
            "type": "email",
            "name": "Send Welcome Email",
            "config": {
                "to": "{{trigger.email}}",
                "subject": "Welcome to RouKey!",
                "template": "welcome"
            },
            "position": {"x": 300, "y": 100}
        }
    ]'::jsonb,
    '[
        {
            "id": "edge-1",
            "source": "trigger-1",
            "target": "email-1"
        }
    ]'::jsonb,
    '[
        {
            "type": "webhook",
            "config": {"path": "/webhook/user-signup"}
        }
    ]'::jsonb,
    true,
    'Email Automation',
    false
),
(
    '00000000-0000-0000-0000-000000000000',
    'Slack Notification on API Error',
    'Send Slack notification when API error rate exceeds threshold',
    '[
        {
            "id": "trigger-1",
            "type": "schedule",
            "name": "Check API Errors",
            "config": {"cron": "*/5 * * * *"},
            "position": {"x": 100, "y": 100}
        },
        {
            "id": "condition-1",
            "type": "conditional",
            "name": "Error Rate > 5%",
            "config": {"condition": "{{api_error_rate}} > 0.05"},
            "position": {"x": 300, "y": 100}
        },
        {
            "id": "slack-1",
            "type": "slack",
            "name": "Send Alert",
            "config": {
                "channel": "#alerts",
                "message": "🚨 API error rate is {{api_error_rate}}%"
            },
            "position": {"x": 500, "y": 100}
        }
    ]'::jsonb,
    '[
        {
            "id": "edge-1",
            "source": "trigger-1",
            "target": "condition-1"
        },
        {
            "id": "edge-2",
            "source": "condition-1",
            "target": "slack-1",
            "condition": "true"
        }
    ]'::jsonb,
    '[
        {
            "type": "schedule",
            "config": {"cron": "*/5 * * * *"}
        }
    ]'::jsonb,
    true,
    'Monitoring',
    false
)
ON CONFLICT DO NOTHING;

-- 14. Add comments for documentation
COMMENT ON TABLE workflows IS 'Stores user-created workflow definitions and templates';
COMMENT ON TABLE workflow_executions IS 'Tracks individual workflow execution instances';
COMMENT ON TABLE workflow_execution_logs IS 'Detailed logs for workflow execution debugging';
COMMENT ON TABLE workflow_usage IS 'Monthly usage tracking for billing and limits';

COMMENT ON FUNCTION get_user_workflow_limits(UUID) IS 'Returns workflow limits based on user subscription tier';
COMMENT ON FUNCTION increment_workflow_usage(UUID, INTEGER) IS 'Increments monthly workflow execution count for a user';
