'use client';

import React from 'react';
import { SubscriptionTier } from '@/lib/stripe-client';
import { StarIcon, TrophyIcon, ShieldCheckIcon, SparklesIcon } from '@heroicons/react/24/solid';

interface TierBadgeProps {
  tier: SubscriptionTier;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  className?: string;
  theme?: 'light' | 'dark';
}

const tierConfig = {
  free: {
    name: 'Free',
    light: {
      color: 'bg-gray-100 text-gray-800 border-gray-300',
      iconColor: 'text-gray-600'
    },
    dark: {
      color: 'bg-gray-800/50 text-gray-200 border-gray-600/50',
      iconColor: 'text-gray-400'
    },
    icon: ShieldCheckIcon
  },
  starter: {
    name: 'Starter',
    light: {
      color: 'bg-blue-100 text-blue-800 border-blue-300',
      iconColor: 'text-blue-600'
    },
    dark: {
      color: 'bg-blue-900/30 text-blue-200 border-blue-500/50',
      iconColor: 'text-blue-400'
    },
    icon: StarIcon
  },
  professional: {
    name: 'Professional',
    light: {
      color: 'bg-orange-100 text-orange-800 border-orange-300',
      iconColor: 'text-orange-600'
    },
    dark: {
      color: 'bg-orange-900/30 text-orange-200 border-orange-500/50',
      iconColor: 'text-orange-400'
    },
    icon: SparklesIcon
  },
  enterprise: {
    name: 'Enterprise',
    light: {
      color: 'bg-purple-100 text-purple-800 border-purple-300',
      iconColor: 'text-purple-600'
    },
    dark: {
      color: 'bg-purple-900/30 text-purple-200 border-purple-500/50',
      iconColor: 'text-purple-400'
    },
    icon: TrophyIcon
  }
};

const sizeConfig = {
  sm: {
    container: 'px-2 py-1 text-xs',
    icon: 'w-3 h-3'
  },
  md: {
    container: 'px-3 py-1 text-sm',
    icon: 'w-4 h-4'
  },
  lg: {
    container: 'px-4 py-2 text-base',
    icon: 'w-5 h-5'
  }
};

export function TierBadge({
  tier,
  size = 'md',
  showIcon = true,
  className = '',
  theme = 'light'
}: TierBadgeProps) {
  const config = tierConfig[tier];
  const sizeStyles = sizeConfig[size];
  const themeStyles = config[theme];
  const Icon = config.icon;

  return (
    <span className={`
      inline-flex items-center space-x-1 font-medium border rounded-full
      ${themeStyles.color}
      ${sizeStyles.container}
      ${className}
    `}>
      {showIcon && (
        <Icon className={`${sizeStyles.icon} ${themeStyles.iconColor}`} />
      )}
      <span>{config.name}</span>
    </span>
  );
}
