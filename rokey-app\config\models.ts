export interface Model {
  id: string;
  name: string;
  provider: string;
  description?: string;
  apiUrl?: string;
}

export interface Provider {
  id: string;
  name: string;
  apiBaseUrl?: string;
  models: Model[];
}

export const llmProviders: Provider[] = [
  {
    id: "openai",
    name: "OpenAI",
    apiBaseUrl: "https://api.openai.com/v1/chat/completions",
    models: [
      { id: "gpt-4.1", name: "GPT-4.1", provider: "OpenAI", description: "Flagship GPT model for complex tasks", apiUrl: "https://api.openai.com/v1" },
      { id: "o4-mini", name: "o4-mini", provider: "OpenAI", description: "Faster, more affordable reasoning model", apiUrl: "https://api.openai.com/v1" },
      { id: "o3", name: "o3", provider: "OpenAI", description: "Powerful reasoning model", apiUrl: "https://api.openai.com/v1" },
      { id: "o3-mini", name: "o3-mini", provider: "OpenAI", description: "Compact reasoning model", apiUrl: "https://api.openai.com/v1" },
      { id: "gpt-4.5-preview", name: "GPT-4.5 Preview", provider: "OpenAI", description: "Advanced reasoning model (to be discontinued July 14, 2025)", apiUrl: "https://api.openai.com/v1" },
      { id: "gpt-4o", name: "GPT-4o", provider: "OpenAI", description: "Multimodal model with image, text, and audio capabilities", apiUrl: "https://api.openai.com/v1" },
      { id: "gpt-4.1-mini", name: "GPT-4.1 mini", provider: "OpenAI", description: "Smaller variant of GPT-4.1", apiUrl: "https://api.openai.com/v1" },
      { id: "gpt-4.1-nano", name: "GPT-4.1 nano", provider: "OpenAI", description: "Most compact variant of GPT-4.1", apiUrl: "https://api.openai.com/v1" },
    ],
  },
  {
    id: "google",
    name: "Google",
    apiBaseUrl: "https://ai.google.dev/gemini-api",
    models: [
      { id: "gemini-2.5-pro", name: "Gemini 2.5 Pro", provider: "Google", description: "Most advanced reasoning model with native \"thinking\" capabilities", apiUrl: "https://ai.google.dev/gemini-api" },
      { id: "gemini-2.5-flash", name: "Gemini 2.5 Flash", provider: "Google", description: "Fast model with balanced performance", apiUrl: "https://ai.google.dev/gemini-api" },
      { id: "gemini-2.5-pro-preview-tts", name: "Gemini 2.5 Pro Preview TTS", provider: "Google", description: "Text-to-speech model for structured workflows", apiUrl: "https://ai.google.dev/gemini-api" },
      { id: "gemini-ultra", name: "Gemini Ultra", provider: "Google", description: "Legacy large enterprise model", apiUrl: "https://ai.google.dev/gemini-api" },
      { id: "gemini-2.5-pro", name: "Gemini 2.5 Pro", provider: "Google", description: "Most advanced reasoning model with native \"thinking\" capabilities", apiUrl: "https://ai.google.dev/gemini-api" },
      { id: "gemini-nano", name: "Gemini Nano", provider: "Google", description: "Legacy edge computing model", apiUrl: "https://ai.google.dev/gemini-api" },
      { id: "gemma-3", name: "Gemma 3", provider: "Google", description: "Open-weight LLM available in 1B, 4B, 12B, 27B parameter sizes", apiUrl: "https://ai.google.dev/gemini-api" },
      { id: "veo-3", name: "Veo 3", provider: "Google", description: "Video generation model (announced at I/O 2025)", apiUrl: "https://ai.google.dev/gemini-api" },
    ],
  },
  {
    id: "anthropic",
    name: "Anthropic",
    apiBaseUrl: "https://api.anthropic.com/v1/chat/completions",
    models: [
      { id: "claude-3.5-sonnet", name: "Claude 3.5 Sonnet", provider: "Anthropic", description: "Updated Sonnet model with enhanced capabilities", apiUrl: "https://api.anthropic.com/v1" },
      { id: "claude-3.5-haiku", name: "Claude 3.5 Haiku", provider: "Anthropic", description: "Updated Haiku model with improved performance", apiUrl: "https://api.anthropic.com/v1" },
      { id: "claude-3.7-sonnet", name: "Claude 3.7 Sonnet", provider: "Anthropic", description: "Latest model with improved reasoning", apiUrl: "https://api.anthropic.com/v1" },
      { id: "claude-3-opus", name: "Claude 3 Opus", provider: "Anthropic", description: "Most capable model for complex tasks", apiUrl: "https://api.anthropic.com/v1" },
      { id: "claude-3-sonnet", name: "Claude 3 Sonnet", provider: "Anthropic", description: "Balanced model for general use", apiUrl: "https://api.anthropic.com/v1" },
      { id: "claude-3-haiku", name: "Claude 3 Haiku", provider: "Anthropic", description: "Fast and efficient model", apiUrl: "https://api.anthropic.com/v1" },
      { id: "claude-4-opus", name: "Claude 4 Opus", provider: "Anthropic", description: "Next generation flagship model", apiUrl: "https://api.anthropic.com/v1" },
      { id: "claude-4-sonnet", name: "Claude 4 Sonnet", provider: "Anthropic", description: "Next generation flagship model", apiUrl: "https://api.anthropic.com/v1" },
    ],
  },
  {
    id: "meta",
    name: "Meta AI",
    apiBaseUrl: "https://llama.meta.com/api/",
    models: [
      { id: "llama-4-scout-17b", name: "Llama 4 Scout 17B", provider: "Meta AI", description: "Natively multimodal model with early fusion architecture", apiUrl: "https://llama.meta.com/api/" },
      { id: "llama-4-maverick-17b", name: "Llama 4 Maverick 17B", provider: "Meta AI", description: "\\\"Workhorse\\\" multimodal model for enterprise applications", apiUrl: "https://llama.meta.com/api/" },
      { id: "llama-4-behemoth", name: "Llama 4 Behemoth", provider: "Meta AI", description: "Most powerful Llama model (not yet released)", apiUrl: "https://llama.meta.com/api/" },
      { id: "llama-3.3", name: "Llama 3.3", provider: "Meta AI", description: "Previous generation large model", apiUrl: "https://llama.meta.com/api/" },
      { id: "llama-3.1", name: "Llama 3.1", provider: "Meta AI", description: "Previous generation with updates", apiUrl: "https://llama.meta.com/api/" },
      { id: "llama-2", name: "Llama 2", provider: "Meta AI", description: "Legacy open-weight model", apiUrl: "https://llama.meta.com/api/" },
    ],
  },
  {
    id: "mistral-ai",
    name: "Mistral AI",
    apiBaseUrl: "https://api.mistral.ai/v1",
    models: [
      { id: "mistral-medium-3", name: "Mistral Medium 3", provider: "Mistral AI", description: "Latest model delivering high performance at lower cost", apiUrl: "https://api.mistral.ai/v1" },
      { id: "mistral-small-3.1", name: "Mistral Small 3.1", provider: "Mistral AI", description: "Lightweight model with strong capabilities", apiUrl: "https://api.mistral.ai/v1" },
      { id: "mistral-large-2", name: "Mistral Large 2", provider: "Mistral AI", description: "Flagship reasoning model for complex tasks", apiUrl: "https://api.mistral.ai/v1" },
      { id: "mistral-small-2409", name: "Mistral Small 2409", provider: "Mistral AI", description: "Enterprise-grade small model", apiUrl: "https://api.mistral.ai/v1" },
      { id: "devstral", name: "Devstral", provider: "Mistral AI", description: "Specialized model for coding tasks", apiUrl: "https://api.mistral.ai/v1" },
      { id: "codestral-25.01", name: "Codestral 25.01", provider: "Mistral AI", description: "Code-specialized model", apiUrl: "https://api.mistral.ai/v1" },
      { id: "pixtral-12b-2409", name: "Pixtral-12b-2409", provider: "Mistral AI", description: "Multimodal model", apiUrl: "https://api.mistral.ai/v1" },
    ],
  },
  {
    id: "deepseek",
    name: "DeepSeek",
    apiBaseUrl: "https://api.deepseek.com/chat/completions",
    models: [
      { id: "deepseek-r1-0528", name: "DeepSeek-R1-0528", provider: "DeepSeek", description: "Updated reasoning model with 685B parameters", apiUrl: "https://platform.deepseek.com/api" },
      { id: "deepseek-r1-distill-qwen-32b", name: "DeepSeek-R1-Distill-Qwen-32B", provider: "DeepSeek", description: "Distilled model that can run on a single GPU", apiUrl: "https://platform.deepseek.com/api" },
      { id: "deepseek-v3", name: "DeepSeek-V3", provider: "DeepSeek", description: "Previous generation model", apiUrl: "https://platform.deepseek.com/api" },
      { id: "deepseek-r2", name: "DeepSeek-R2", provider: "DeepSeek", description: "Upcoming model (not yet released)", apiUrl: "https://platform.deepseek.com/api" },
    ],
  },
  {
    id: "alibaba",
    name: "Alibaba (Qwen)",
    apiBaseUrl: "https://www.alibabacloud.com/help/en/model-studio/models",
    models: [
      { id: "qwen-3", name: "Qwen 3", provider: "Alibaba", description: "Family of \\\"hybrid\\\" AI reasoning models", apiUrl: "https://www.alibabacloud.com/help/en/model-studio/models" },
      { id: "qwen-2.5-max", name: "Qwen 2.5-Max", provider: "Alibaba", description: "Large-scale MoE model trained on 20T tokens", apiUrl: "https://www.alibabacloud.com/help/en/model-studio/models" },
      { id: "qwen-2.5-vl", name: "Qwen 2.5-VL", provider: "Alibaba", description: "Vision-language model", apiUrl: "https://www.alibabacloud.com/help/en/model-studio/models" },
      { id: "qwen-2.5-more", name: "Qwen 2.5-More", provider: "Alibaba", description: "Generalist model with enhanced reasoning", apiUrl: "https://www.alibabacloud.com/help/en/model-studio/models" },
      { id: "qwen-2.5-turbo", name: "Qwen 2.5-turbo", provider: "Alibaba", description: "Fast model with thinking mode capability", apiUrl: "https://www.alibabacloud.com/help/en/model-studio/models" },
      { id: "qwen2.5-omni", name: "Qwen2.5-Omni", provider: "Alibaba", description: "Multimodal model ranked high on HuggingFace", apiUrl: "https://www.alibabacloud.com/help/en/model-studio/models" },
    ],
  },
  {
    id: "cohere",
    name: "Cohere",
    apiBaseUrl: "https://api.cohere.ai/v1",
    models: [
      { id: "command-a", name: "Command A", provider: "Cohere", description: "Latest enterprise model with high performance", apiUrl: "https://api.cohere.ai/v1" },
      { id: "embed-4", name: "Embed 4", provider: "Cohere", description: "Multimodal AI model designed for search", apiUrl: "https://api.cohere.ai/v1" },
      { id: "rerank-v3.5", name: "Rerank v3.5", provider: "Cohere", description: "Model for multilingual retrieval and reasoning", apiUrl: "https://api.cohere.ai/v1" },
      { id: "command-r-plus", name: "Command R+", provider: "Cohere", description: "Open-source model for non-commercial use", apiUrl: "https://api.cohere.ai/v1" },
    ],
  },
  {
    id: "xai",
    name: "xAI (Grok)",
    apiBaseUrl: "https://api.x.ai/v1/chat/completions",
    models: [
      { id: "grok-3", name: "Grok 3", provider: "xAI", description: "Latest flagship model with superior reasoning", apiUrl: "https://api.x.ai/v1" },
      { id: "grok-3-mini", name: "Grok 3 Mini (with Thinking)", provider: "xAI", description: "Lightweight model that thinks before responding", apiUrl: "https://api.x.ai/v1" },
      { id: "grok-3.5", name: "Grok 3.5", provider: "xAI", description: "Next generation model (upcoming)", apiUrl: "https://api.x.ai/v1" },
      { id: "grok-4", name: "Grok 4", provider: "xAI", description: "Future model with 20x compute of competitors (announced)", apiUrl: "https://api.x.ai/v1" },
    ],
  },
  {
    id: "tii",
    name: "Technology Innovation Institute (Falcon)",
    apiBaseUrl: "https://www.tii.ae/falcon",
    models: [
      { id: "falcon-3", name: "Falcon 3", provider: "TII", description: "Powerful small AI models that can run on laptops", apiUrl: "https://www.tii.ae/falcon" },
      { id: "falcon-h1-1.5b-deep", name: "Falcon-H1-1.5B-Deep", provider: "TII", description: "Hybrid transformer model competing with 7-10B models", apiUrl: "https://www.tii.ae/falcon" },
      { id: "falcon-h1-34b", name: "Falcon-H1-34B", provider: "TII", description: "Large hybrid model with high performance", apiUrl: "https://www.tii.ae/falcon" },
      { id: "falcon-arabic", name: "Falcon Arabic", provider: "TII", description: "First Arabic model in the Falcon series", apiUrl: "https://www.tii.ae/falcon" },
      { id: "falcon-2-11b", name: "Falcon 2 11B", provider: "TII", description: "Model with vision-to-language capabilities", apiUrl: "https://www.tii.ae/falcon" },
    ],
  },
  {
    id: "perplexity",
    name: "Perplexity",
    apiBaseUrl: "https://api.perplexity.ai/", // Assuming this is the general API base for pplx-online and R1-1776
    models: [
      { id: "sonar", name: "Sonar", provider: "Perplexity", description: "AI answer engine with search grounding", apiUrl: "https://sonar.perplexity.ai/" }, // Specific URL for Sonar
      { id: "pplx-online", name: "pplx-online", provider: "Perplexity", description: "Online LLMs focused on factual responses", apiUrl: "https://api.perplexity.ai/" },
      { id: "r1-1776", name: "R1-1776", provider: "Perplexity", description: "Open-source model", apiUrl: "https://api.perplexity.ai/" },
    ],
  },
  {
    id: "nvidia",
    name: "NVIDIA NIM Models",
    apiBaseUrl: "https://build.nvidia.com/models",
    models: [
      { id: "llama-3.3-nemotron-super-49b-v1", name: "Llama-3.3-nemotron-super-49b-v1", provider: "NVIDIA", description: "Optimized Meta model", apiUrl: "https://build.nvidia.com/models" },
      { id: "qwq-32b", name: "Qwq-32b", provider: "NVIDIA/Qwen", description: "Large language model", apiUrl: "https://build.nvidia.com/models" },
      { id: "cosmos-predict1-7b", name: "Cosmos-predict1-7b", provider: "NVIDIA", description: "Specialized prediction model", apiUrl: "https://build.nvidia.com/models" },
      { id: "audio2face-3d", name: "Audio2face-3d", provider: "NVIDIA", description: "Audio to facial animation model", apiUrl: "https://build.nvidia.com/models" },
    ],
  },
  {
    id: "baichuan",
    name: "Baichuan Inc.",
    apiBaseUrl: "https://www.baichuan-ai.com/api",
    models: [
      { id: "baichuan-m1", name: "Baichuan-M1", provider: "Baichuan Inc.", description: "Medical LLM series trained on 20T tokens", apiUrl: "https://www.baichuan-ai.com/api" },
      { id: "baichuan-omni-1.5", name: "Baichuan-Omni-1.5", provider: "Baichuan Inc.", description: "Enhanced multimodal model (text, image, audio, video)", apiUrl: "https://www.baichuan-ai.com/api" },
      { id: "baichuan-13b", name: "Baichuan 13B", provider: "Baichuan Inc.", description: "Base and Chat versions", apiUrl: "https://www.baichuan-ai.com/api" },
      { id: "baichuan-7b", name: "Baichuan 7B", provider: "Baichuan Inc.", description: "Smaller parameter model", apiUrl: "https://www.baichuan-ai.com/api" },
    ],
  },
  {
    id: "huggingface",
    name: "Hugging Face Open Source Models",
    // No general apiBaseUrl for Hugging Face, models have individual URLs or are used with libraries
    models: [
      { id: "microsoft-bitnet-b1.58-2b-4t", name: "microsoft/bitnet-b1.58-2B-4T", provider: "Microsoft", description: "Top-ranked model on Hugging Face", apiUrl: "https://huggingface.co/microsoft/bitnet-b1.58-2B-4T" },
      { id: "agentica-org-deepcoder-14b-preview", name: "agentica-org/DeepCoder-14B-Preview", provider: "Agentica", description: "Specialized coding model", apiUrl: "https://huggingface.co/agentica-org/DeepCoder-14B-Preview" },
      { id: "deepseek-ai-deepseek-r1", name: "deepseek-ai/DeepSeek-R1", provider: "DeepSeek", description: "Open-weight version", apiUrl: "https://huggingface.co/deepseek-ai/DeepSeek-R1" },
      { id: "thudm-cogvlm", name: "THUDM/CogVLM", provider: "Tsinghua University", description: "Vision-language model", apiUrl: "https://huggingface.co/THUDM/CogVLM" },
      { id: "meta-llama-llama-3.1-8b-instruct", name: "meta-llama/Llama-3.1-8B-Instruct", provider: "Meta AI", description: "Instruction-tuned Llama model", apiUrl: "https://huggingface.co/meta-llama/Llama-3.1-8B-Instruct" },
    ],
  },
  {
    id: "azure-openai",
    name: "Azure OpenAI Models",
    apiBaseUrl: "https://api.cognitive.microsoft.com/openai",
    models: [
      { id: "gpt-4.5-preview-azure", name: "GPT-4.5 Preview", provider: "Microsoft/OpenAI", description: "Latest GPT model for diverse text and image tasks", apiUrl: "https://api.cognitive.microsoft.com/openai" },
      { id: "o-series-azure", name: "o-series models", provider: "Microsoft/OpenAI", description: "Reasoning models with advanced problem-solving", apiUrl: "https://api.cognitive.microsoft.com/openai" },
      { id: "azure-openai-o3-mini", name: "Azure OpenAI o3-mini", provider: "Microsoft/OpenAI", description: "Compact reasoning model", apiUrl: "https://api.cognitive.microsoft.com/openai" },
      { id: "azure-openai-o1", name: "Azure OpenAI o1", provider: "Microsoft/OpenAI", description: "First-generation reasoning model", apiUrl: "https://api.cognitive.microsoft.com/openai" },
      { id: "azure-openai-o1-mini", name: "Azure OpenAI o1-mini", provider: "Microsoft/OpenAI", description: "Compact first-generation reasoning model", apiUrl: "https://api.cognitive.microsoft.com/openai" },
    ],
  },
  {
    id: "yi",
    name: "Yi Models",
    apiBaseUrl: "https://api.01.ai/v1",
    models: [
      { id: "yi-34b", name: "Yi-34B", provider: "01.AI", description: "Large foundation model", apiUrl: "https://api.01.ai/v1" },
      { id: "yi-34b-chat", name: "Yi-34B-Chat", provider: "01.AI", description: "Chat-optimized large model", apiUrl: "https://api.01.ai/v1" },
      { id: "yi-6b", name: "Yi-6B", provider: "01.AI", description: "Compact foundation model", apiUrl: "https://api.01.ai/v1" },
      { id: "yi-vl", name: "Yi-VL", provider: "01.AI", description: "Vision-Language model", apiUrl: "https://api.01.ai/v1" },
    ],
  },
  {
    id: "ollama",
    name: "Ollama (Local Deployment Models)",
    // No central API base URL for Ollama, as it's for local deployment
    models: [
      { id: "ollama-llama-3.3", name: "Llama 3.3", provider: "Meta (via Ollama)", description: "Latest Llama model for local deployment" },
      { id: "ollama-deepseek-r1", name: "DeepSeek-R1", provider: "DeepSeek (via Ollama)", description: "Reasoning model for local deployment" },
      { id: "ollama-phi-4", name: "Phi-4", provider: "Microsoft (via Ollama)", description: "Microsoft's efficient model for local deployment" },
      { id: "ollama-gemma-3", name: "Gemma 3", provider: "Google (via Ollama)", description: "Google's open model for local deployment" },
      { id: "ollama-mistral-small-3.1", name: "Mistral Small 3.1", provider: "Mistral AI (via Ollama)", description: "Efficient model for local deployment" },
      { id: "ollama-qwen-2.5-vl", name: "Qwen 2.5-VL", provider: "Alibaba (via Ollama)", description: "Vision-language model for local deployment" },
    ],
  }
];

export const getModelById = (providerId: string, modelId: string): Model | undefined => {
  const provider = llmProviders.find(p => p.id === providerId);
  if (!provider) return undefined;
  return provider.models.find(m => m.id === modelId);
};

export const getAllModels = (): Model[] => {
  return llmProviders.reduce((acc, provider) => {
    return acc.concat(provider.models);
  }, [] as Model[]);
}; 