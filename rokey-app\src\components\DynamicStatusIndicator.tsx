'use client';

import React, { useState, useEffect } from 'react';
import {
  CpuChipIcon,
  BoltIcon,
  CogIcon,
  CheckCircleIcon,
  PencilIcon,
  SparklesIcon,
  ClockIcon,
  BeakerIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
  LightBulbIcon,
  RocketLaunchIcon,
  FireIcon,
  CommandLineIcon,
  StarIcon,
  ShieldCheckIcon,
  UserGroupIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline';

// Additional icons for orchestration
import {
  TagIcon
} from '@heroicons/react/24/solid';

export type ProcessingStage = 
  | 'initializing'
  | 'analyzing'
  | 'routing'
  | 'complexity_analysis'
  | 'role_classification'
  | 'preparing'
  | 'connecting'
  | 'generating'
  | 'typing'
  | 'finalizing'
  | 'complete';

interface StatusConfig {
  icon: React.ComponentType<{ className?: string }>;
  text: string;
  description: string;
  bgColor: string;
  iconColor: string;
  borderColor: string;
  glowColor: string;
  gradientFrom: string;
  gradientTo: string;
  duration?: number; // Optional minimum duration in ms
}

const STATUS_CONFIGS: Record<ProcessingStage, StatusConfig> = {
  initializing: {
    icon: RocketLaunchIcon,
    text: 'Initializing',
    description: 'Starting up systems',
    bgColor: 'bg-gradient-to-r from-slate-50 to-gray-50',
    iconColor: 'text-slate-600',
    borderColor: 'border-slate-200/60',
    glowColor: 'shadow-slate-200/50',
    gradientFrom: 'from-slate-400',
    gradientTo: 'to-gray-400',
    duration: 200
  },
  analyzing: {
    icon: MagnifyingGlassIcon,
    text: 'Analyzing',
    description: 'Understanding your request',
    bgColor: 'bg-gradient-to-r from-cyan-50 to-blue-50',
    iconColor: 'text-cyan-600',
    borderColor: 'border-cyan-200/60',
    glowColor: 'shadow-cyan-200/50',
    gradientFrom: 'from-cyan-400',
    gradientTo: 'to-blue-400',
    duration: 300
  },
  routing: {
    icon: ArrowPathIcon,
    text: 'Smart routing',
    description: 'Finding optimal path',
    bgColor: 'bg-gradient-to-r from-indigo-50 to-purple-50',
    iconColor: 'text-indigo-600',
    borderColor: 'border-indigo-200/60',
    glowColor: 'shadow-indigo-200/50',
    gradientFrom: 'from-indigo-400',
    gradientTo: 'to-purple-400',
    duration: 400
  },
  complexity_analysis: {
    icon: LightBulbIcon,
    text: 'Analyzing complexity',
    description: 'Evaluating request depth',
    bgColor: 'bg-gradient-to-r from-amber-50 to-yellow-50',
    iconColor: 'text-amber-600',
    borderColor: 'border-amber-200/60',
    glowColor: 'shadow-amber-200/50',
    gradientFrom: 'from-amber-400',
    gradientTo: 'to-yellow-400',
    duration: 500
  },
  role_classification: {
    icon: StarIcon,
    text: 'Assembling specialists',
    description: 'Building expert team',
    bgColor: 'bg-gradient-to-r from-violet-50 to-purple-50',
    iconColor: 'text-violet-600',
    borderColor: 'border-violet-200/60',
    glowColor: 'shadow-violet-200/50',
    gradientFrom: 'from-violet-400',
    gradientTo: 'to-purple-400',
    duration: 600
  },
  preparing: {
    icon: BoltIcon,
    text: 'Preparing',
    description: 'Setting up processing',
    bgColor: 'bg-gradient-to-r from-orange-50 to-amber-50',
    iconColor: 'text-orange-600',
    borderColor: 'border-orange-200/60',
    glowColor: 'shadow-orange-200/50',
    gradientFrom: 'from-orange-400',
    gradientTo: 'to-amber-400',
    duration: 300
  },
  connecting: {
    icon: CommandLineIcon,
    text: 'Connecting',
    description: 'Establishing AI link',
    bgColor: 'bg-gradient-to-r from-rose-50 to-pink-50',
    iconColor: 'text-rose-600',
    borderColor: 'border-rose-200/60',
    glowColor: 'shadow-rose-200/50',
    gradientFrom: 'from-rose-400',
    gradientTo: 'to-pink-400',
    duration: 400
  },
  generating: {
    icon: FireIcon,
    text: 'Thinking deeply',
    description: 'AI processing in progress',
    bgColor: 'bg-gradient-to-r from-emerald-50 to-teal-50',
    iconColor: 'text-emerald-600',
    borderColor: 'border-emerald-200/60',
    glowColor: 'shadow-emerald-200/50',
    gradientFrom: 'from-emerald-400',
    gradientTo: 'to-teal-400',
    duration: 800
  },
  typing: {
    icon: PencilIcon,
    text: 'Streaming response',
    description: 'Delivering your answer',
    bgColor: 'bg-gradient-to-r from-green-50 to-emerald-50',
    iconColor: 'text-green-600',
    borderColor: 'border-green-200/60',
    glowColor: 'shadow-green-200/50',
    gradientFrom: 'from-green-400',
    gradientTo: 'to-emerald-400'
  },
  finalizing: {
    icon: SparklesIcon,
    text: 'Finalizing',
    description: 'Adding finishing touches',
    bgColor: 'bg-gradient-to-r from-teal-50 to-cyan-50',
    iconColor: 'text-teal-600',
    borderColor: 'border-teal-200/60',
    glowColor: 'shadow-teal-200/50',
    gradientFrom: 'from-teal-400',
    gradientTo: 'to-cyan-400',
    duration: 200
  },
  complete: {
    icon: ShieldCheckIcon,
    text: 'Complete',
    description: 'Response delivered',
    bgColor: 'bg-gradient-to-r from-green-50 to-lime-50',
    iconColor: 'text-green-600',
    borderColor: 'border-green-200/60',
    glowColor: 'shadow-green-200/50',
    gradientFrom: 'from-green-400',
    gradientTo: 'to-lime-400',
    duration: 100
  }
};

interface DynamicStatusIndicatorProps {
  currentStage: ProcessingStage;
  isStreaming?: boolean;
  className?: string;
  onStageChange?: (stage: ProcessingStage) => void;
  orchestrationStatus?: string; // Custom status text for orchestration
}

export default function DynamicStatusIndicator({
  currentStage,
  isStreaming = false,
  className = '',
  onStageChange,
  orchestrationStatus
}: DynamicStatusIndicatorProps) {
  const [displayStage, setDisplayStage] = useState<ProcessingStage>(currentStage);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [orchestrationColorIndex, setOrchestrationColorIndex] = useState(0);
  const [lastOrchestrationStatus, setLastOrchestrationStatus] = useState('');
  const [spinnerSpeed, setSpinnerSpeed] = useState(1); // 1 = normal speed, higher = faster

  // Color palette for orchestration progress cycling
  const orchestrationColors = [
    { bgColor: 'bg-gradient-to-r from-blue-50 to-indigo-50', iconColor: 'text-blue-600', borderColor: 'border-blue-200/60', glowColor: 'shadow-blue-200/50', gradientFrom: 'from-blue-400', gradientTo: 'to-indigo-400' },
    { bgColor: 'bg-gradient-to-r from-purple-50 to-violet-50', iconColor: 'text-purple-600', borderColor: 'border-purple-200/60', glowColor: 'shadow-purple-200/50', gradientFrom: 'from-purple-400', gradientTo: 'to-violet-400' },
    { bgColor: 'bg-gradient-to-r from-indigo-50 to-blue-50', iconColor: 'text-indigo-600', borderColor: 'border-indigo-200/60', glowColor: 'shadow-indigo-200/50', gradientFrom: 'from-indigo-400', gradientTo: 'to-blue-400' },
    { bgColor: 'bg-gradient-to-r from-cyan-50 to-teal-50', iconColor: 'text-cyan-600', borderColor: 'border-cyan-200/60', glowColor: 'shadow-cyan-200/50', gradientFrom: 'from-cyan-400', gradientTo: 'to-teal-400' },
    { bgColor: 'bg-gradient-to-r from-teal-50 to-emerald-50', iconColor: 'text-teal-600', borderColor: 'border-teal-200/60', glowColor: 'shadow-teal-200/50', gradientFrom: 'from-teal-400', gradientTo: 'to-emerald-400' },
    { bgColor: 'bg-gradient-to-r from-green-50 to-lime-50', iconColor: 'text-green-600', borderColor: 'border-green-200/60', glowColor: 'shadow-green-200/50', gradientFrom: 'from-green-400', gradientTo: 'to-lime-400' },
    { bgColor: 'bg-gradient-to-r from-yellow-50 to-amber-50', iconColor: 'text-yellow-600', borderColor: 'border-yellow-200/60', glowColor: 'shadow-yellow-200/50', gradientFrom: 'from-yellow-400', gradientTo: 'to-amber-400' },
    { bgColor: 'bg-gradient-to-r from-orange-50 to-red-50', iconColor: 'text-orange-600', borderColor: 'border-orange-200/60', glowColor: 'shadow-orange-200/50', gradientFrom: 'from-orange-400', gradientTo: 'to-red-400' },
    { bgColor: 'bg-gradient-to-r from-rose-50 to-pink-50', iconColor: 'text-rose-600', borderColor: 'border-rose-200/60', glowColor: 'shadow-rose-200/50', gradientFrom: 'from-rose-400', gradientTo: 'to-pink-400' },
    { bgColor: 'bg-gradient-to-r from-emerald-50 to-teal-50', iconColor: 'text-emerald-600', borderColor: 'border-emerald-200/60', glowColor: 'shadow-emerald-200/50', gradientFrom: 'from-emerald-400', gradientTo: 'to-teal-400' }
  ];

  // Use orchestration colors if in orchestration mode, otherwise use default config
  const isOrchestrationMode = !!orchestrationStatus;
  const currentOrchestrationColor = orchestrationColors[orchestrationColorIndex % orchestrationColors.length];

  // Get appropriate icon based on orchestration status
  const getOrchestrationIcon = (status: string) => {
    if (status.includes('🔍') || status.includes('detected')) return MagnifyingGlassIcon;
    if (status.includes('✅') || status.includes('complete')) return CheckCircleIcon;
    if (status.includes('🎯') || status.includes('Selected')) return TagIcon;
    if (status.includes('🏗️') || status.includes('workflow')) return CogIcon;
    if (status.includes('🤖') || status.includes('agent')) return UserGroupIcon;
    if (status.includes('👑') || status.includes('supervisor')) return StarIcon; // Use StarIcon for supervisor
    if (status.includes('📋') || status.includes('Planning')) return ClipboardDocumentListIcon;
    if (status.includes('🚀') || status.includes('starting')) return RocketLaunchIcon;
    if (status.includes('🔄') || status.includes('synthesizing')) return ArrowPathIcon;
    return STATUS_CONFIGS[displayStage].icon; // fallback
  };

  const config = isOrchestrationMode ? {
    ...STATUS_CONFIGS[displayStage],
    ...currentOrchestrationColor,
    icon: getOrchestrationIcon(orchestrationStatus)
  } : STATUS_CONFIGS[displayStage];

  const Icon = config.icon;

  // Handle stage transitions with speed-up animation
  useEffect(() => {
    if (currentStage !== displayStage) {
      console.log(`🎯 DynamicStatusIndicator: ${displayStage} → ${currentStage}`);
      setIsTransitioning(true);

      // Speed up animation during transition
      setTimeout(() => {
        setDisplayStage(currentStage);
        setIsTransitioning(false);
        onStageChange?.(currentStage);
      }, 200); // Brief transition period
    }
  }, [currentStage, displayStage, onStageChange]);

  // Handle orchestration status changes with color cycling and spinner speed
  useEffect(() => {
    if (orchestrationStatus && orchestrationStatus !== lastOrchestrationStatus) {
      console.log(`🎨 Orchestration status changed: ${orchestrationStatus}`);
      setLastOrchestrationStatus(orchestrationStatus);
      setOrchestrationColorIndex(prev => prev + 1);
      setIsTransitioning(true);

      // Speed up spinner on status change
      setSpinnerSpeed(2.5); // Fast speed during transition

      // Brief transition animation
      setTimeout(() => {
        setIsTransitioning(false);
      }, 300);

      // Return spinner to normal speed after a delay
      setTimeout(() => {
        setSpinnerSpeed(1); // Back to normal speed
      }, 1000);
    }
  }, [orchestrationStatus, lastOrchestrationStatus]);

  // Remove auto-progression - let the hook handle stage management

  return (
    <div className={`flex justify-start ${className}`}>
      {/* Compact modern icon with HIGHLY VISIBLE spinning animation */}
      <div className="relative w-6 h-6 rounded-full flex items-center justify-center mr-2.5 mt-0.5 flex-shrink-0">
        {/* Main progress spinner - dynamic speed with visible border */}
        <div
          className={`absolute -inset-2 w-10 h-10 rounded-full border-[3px] border-t-blue-500 border-r-transparent border-b-transparent border-l-transparent`}
          style={{
            animation: `spin ${1.2 / spinnerSpeed}s linear infinite`,
            borderTopColor: config.iconColor.replace('text-', ''),
            filter: 'drop-shadow(0 0 6px rgba(59, 130, 246, 0.4))'
          }}
        />

        {/* Secondary spinner - counter rotation with different color */}
        <div
          className={`absolute -inset-1.5 w-9 h-9 rounded-full border-[2px] border-t-purple-400 border-r-transparent border-b-transparent border-l-transparent`}
          style={{
            animation: `spin ${1.8 / spinnerSpeed}s linear infinite reverse`,
            borderTopColor: config.iconColor.replace('text-', '').replace('600', '400'),
            opacity: 0.7
          }}
        />

        {/* Tertiary spinner - fastest rotation */}
        <div
          className={`absolute -inset-1 w-8 h-8 rounded-full border-[1px] border-t-cyan-300 border-r-transparent border-b-transparent border-l-transparent`}
          style={{
            animation: `spin ${0.8 / spinnerSpeed}s linear infinite`,
            borderTopColor: config.iconColor.replace('text-', '').replace('600', '300'),
            opacity: 0.5
          }}
        />

        {/* Inner pulsing ring */}
        <div
          className={`absolute -inset-0.5 w-7 h-7 rounded-full border animate-pulse`}
          style={{
            borderColor: config.iconColor.replace('text-', '').replace('600', '200'),
            opacity: 0.3,
            animation: 'pulse 2s ease-in-out infinite'
          }}
        />

        {/* Core circle with enhanced glow */}
        <div className={`relative w-full h-full rounded-full flex items-center justify-center transition-all duration-500 ${config.bgColor} border-2 ${config.borderColor} shadow-lg backdrop-blur-sm`}
             style={{
               boxShadow: `0 0 12px ${config.iconColor.replace('text-', '')}40, 0 0 24px ${config.iconColor.replace('text-', '')}20`
             }}>
          <Icon className={`w-3.5 h-3.5 transition-all duration-500 ${config.iconColor} ${isTransitioning ? 'scale-125 rotate-12' : 'scale-100'} drop-shadow-lg`} />
        </div>
      </div>

      {/* Compact status bubble with modern design */}
      <div className={`max-w-[65%] rounded-xl px-3 py-2 transition-all duration-500 ${config.bgColor} ${config.borderColor} border ${config.glowColor} shadow-sm backdrop-blur-sm`}>
        <div className="flex items-center space-x-1.5">
          {/* Status text with enhanced typography */}
          <div className="transition-all duration-500">
            <span className={`text-xs font-semibold transition-colors duration-500 ${config.iconColor} tracking-wide`}>
              {orchestrationStatus || config.text}
            </span>
            {isStreaming && displayStage === 'typing' && !orchestrationStatus && (
              <span className={`ml-1.5 text-[10px] opacity-80 ${config.iconColor} font-medium`}>
                • Live
              </span>
            )}
            {orchestrationStatus && (
              <span className={`ml-1.5 text-[10px] opacity-80 ${config.iconColor} font-medium`}>
                • Orchestrating
              </span>
            )}
          </div>
        </div>

        {/* HIGHLY VISIBLE animated progress bar */}
        {(displayStage === 'generating' || displayStage === 'typing') && (
          <div className="mt-2">
            <div className="h-1 rounded-full overflow-hidden bg-white/30 backdrop-blur-sm border border-white/20">
              <div
                className={`h-full rounded-full transition-all duration-1000 bg-gradient-to-r ${config.gradientFrom} ${config.gradientTo} relative overflow-hidden`}
                style={{
                  width: displayStage === 'typing' ? '100%' : '60%',
                  animation: displayStage === 'typing'
                    ? 'progressPulse 1.5s ease-in-out infinite, progressShimmer 2s linear infinite'
                    : 'progressShimmer 2s linear infinite, progressGlow 3s ease-in-out infinite'
                }}
              >
                {/* Animated shine effect */}
                <div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
                  style={{
                    animation: 'progressShine 2s linear infinite',
                    transform: 'skewX(-20deg)'
                  }}
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced animation styles */}
      <style jsx>{`
        @keyframes progressShimmer {
          0% { background-position: -200% 0; }
          100% { background-position: 200% 0; }
        }

        @keyframes progressShine {
          0% { transform: translateX(-100%) skewX(-20deg); }
          100% { transform: translateX(300%) skewX(-20deg); }
        }

        @keyframes progressPulse {
          0%, 100% { opacity: 1; transform: scaleY(1); }
          50% { opacity: 0.8; transform: scaleY(1.1); }
        }

        @keyframes progressGlow {
          0%, 100% { filter: brightness(1) drop-shadow(0 0 2px currentColor); }
          50% { filter: brightness(1.2) drop-shadow(0 0 6px currentColor); }
        }
      `}</style>
    </div>
  );
}

// Hook for managing status progression
export function useStatusProgression() {
  const [currentStage, setCurrentStage] = useState<ProcessingStage>('initializing');
  const [stageHistory, setStageHistory] = useState<ProcessingStage[]>(['initializing']);

  const updateStage = (stage: ProcessingStage) => {
    setCurrentStage(stage);
    setStageHistory(prev => [...prev, stage]);
  };

  const reset = () => {
    setCurrentStage('initializing');
    setStageHistory(['initializing']);
  };

  return {
    currentStage,
    stageHistory,
    updateStage,
    reset
  };
}
