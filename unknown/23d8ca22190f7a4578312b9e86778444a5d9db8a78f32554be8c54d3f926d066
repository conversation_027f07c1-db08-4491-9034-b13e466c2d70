-- Phase 2A Performance Optimization: Database Indexes
-- Run this migration to add performance-critical indexes

-- 1. Optimize custom_api_configs queries
CREATE INDEX IF NOT EXISTS idx_custom_api_configs_created_at_desc 
ON custom_api_configs(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_custom_api_configs_user_created 
ON custom_api_configs(user_id, created_at DESC) 
WHERE user_id IS NOT NULL;

-- 2. Optimize chat_conversations queries
CREATE INDEX IF NOT EXISTS idx_chat_conversations_config_updated 
ON chat_conversations(custom_api_config_id, updated_at DESC);

CREATE INDEX IF NOT EXISTS idx_chat_conversations_user_updated 
ON chat_conversations(user_id, updated_at DESC) 
WHERE user_id IS NOT NULL;

-- 3. Optimize chat_messages queries for conversation loading
CREATE INDEX IF NOT EXISTS idx_chat_messages_conv_created_desc 
ON chat_messages(conversation_id, created_at DESC);

-- Composite index for efficient message stats queries
CREATE INDEX IF NOT EXISTS idx_chat_messages_conv_role_created 
ON chat_messages(conversation_id, role, created_at DESC);

-- 4. Optimize api_keys queries
CREATE INDEX IF NOT EXISTS idx_api_keys_config_status_active 
ON api_keys(custom_api_config_id, status) 
WHERE status = 'active';

CREATE INDEX IF NOT EXISTS idx_api_keys_config_default_chat 
ON api_keys(custom_api_config_id, is_default_general_chat_model) 
WHERE is_default_general_chat_model = true;

-- 5. Optimize request_logs queries for analytics
CREATE INDEX IF NOT EXISTS idx_request_logs_timestamp_desc 
ON request_logs(request_timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_request_logs_config_timestamp 
ON request_logs(custom_api_config_id, request_timestamp DESC);

-- Partial index for recent logs - using a fixed date instead of NOW()
-- Note: This index will need periodic recreation to maintain "recent" definition
CREATE INDEX IF NOT EXISTS idx_request_logs_recent
ON request_logs(request_timestamp DESC, status_code)
WHERE request_timestamp > '2025-01-01'::timestamptz;

-- 6. Optimize role assignments queries
CREATE INDEX IF NOT EXISTS idx_api_key_role_assignments_config_role 
ON api_key_role_assignments(custom_api_config_id, role_name);

-- 7. Add covering indexes for common query patterns
-- Custom configs with routing strategy
CREATE INDEX IF NOT EXISTS idx_custom_api_configs_covering 
ON custom_api_configs(id, name, created_at, updated_at, routing_strategy);

-- Chat conversations with title
CREATE INDEX IF NOT EXISTS idx_chat_conversations_covering 
ON chat_conversations(custom_api_config_id, id, title, created_at, updated_at) 
WHERE custom_api_config_id IS NOT NULL;

-- 8. Optimize training-related queries
CREATE INDEX IF NOT EXISTS idx_training_jobs_config_status 
ON training_jobs(custom_api_config_id, status, created_at DESC) 
WHERE status = 'completed';

-- 9. Add statistics for query planner optimization
-- Update table statistics to help PostgreSQL choose better query plans
ANALYZE custom_api_configs;
ANALYZE chat_conversations;
ANALYZE chat_messages;
ANALYZE api_keys;
ANALYZE request_logs;

-- 10. Create materialized view for frequently accessed conversation stats (optional)
-- This can be enabled if conversation loading is still slow after other optimizations
/*
CREATE MATERIALIZED VIEW IF NOT EXISTS conversation_stats AS
SELECT 
    c.id as conversation_id,
    c.custom_api_config_id,
    c.title,
    c.created_at,
    c.updated_at,
    COUNT(m.id) as message_count,
    MAX(m.created_at) as last_message_at,
    (
        SELECT content 
        FROM chat_messages m2 
        WHERE m2.conversation_id = c.id 
        ORDER BY m2.created_at DESC 
        LIMIT 1
    ) as last_message_content
FROM chat_conversations c
LEFT JOIN chat_messages m ON c.id = m.conversation_id
GROUP BY c.id, c.custom_api_config_id, c.title, c.created_at, c.updated_at;

-- Index for the materialized view
CREATE INDEX IF NOT EXISTS idx_conversation_stats_config_updated 
ON conversation_stats(custom_api_config_id, updated_at DESC);

-- Refresh function for the materialized view
CREATE OR REPLACE FUNCTION refresh_conversation_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY conversation_stats;
END;
$$ LANGUAGE plpgsql;

-- Optional: Set up automatic refresh (uncomment if needed)
-- This would refresh the materialized view every 5 minutes
-- SELECT cron.schedule('refresh-conversation-stats', '*/5 * * * *', 'SELECT refresh_conversation_stats();');
*/

-- 11. Performance monitoring queries
-- Use these to monitor index usage and query performance

-- Check index usage
/*
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;
*/

-- Check slow queries
/*
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
WHERE query LIKE '%custom_api_configs%' 
   OR query LIKE '%chat_conversations%'
   OR query LIKE '%chat_messages%'
ORDER BY mean_time DESC
LIMIT 10;
*/

-- Comments for maintenance
COMMENT ON INDEX idx_custom_api_configs_created_at_desc IS 'Phase 2A: Optimizes custom config listing by creation date';
COMMENT ON INDEX idx_chat_conversations_config_updated IS 'Phase 2A: Optimizes conversation loading for specific configs';
COMMENT ON INDEX idx_chat_messages_conv_created_desc IS 'Phase 2A: Optimizes message loading within conversations';
COMMENT ON INDEX idx_request_logs_recent IS 'Phase 2A: Optimizes recent activity queries for analytics';

-- Performance optimization complete
-- Expected improvements:
-- - Custom configs API: 9.5s -> <500ms (95% improvement)
-- - Chat conversations API: 3-8s -> <200ms (90%+ improvement)  
-- - System status API: 10s -> <1s (90% improvement)
-- - Overall page loads: 8s -> <2s (75% improvement)
