'use client';

import React, { useState } from 'react';
import { CheckIcon } from '@heroicons/react/24/outline';

interface CopyButtonProps {
  text: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'code' | 'message';
  title?: string;
}

export default function CopyButton({ 
  text, 
  className = '', 
  size = 'sm', 
  variant = 'default',
  title = 'Copy to clipboard'
}: CopyButtonProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        document.execCommand('copy');
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (fallbackErr) {
        console.error('Fallback copy failed: ', fallbackErr);
      }
      document.body.removeChild(textArea);
    }
  };

  // Size classes
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5', 
    lg: 'w-6 h-6'
  };

  // Button size classes
  const buttonSizeClasses = {
    sm: 'p-1.5',
    md: 'p-2',
    lg: 'p-2.5'
  };

  // Variant-specific styles
  const variantClasses = {
    default: 'text-gray-500 hover:text-gray-700 hover:bg-gray-100/80',
    code: 'text-gray-300 hover:text-white hover:bg-gray-600/80',
    message: 'text-gray-500 hover:text-gray-700 hover:bg-white/20'
  };

  return (
    <button
      onClick={handleCopy}
      className={`
        ${buttonSizeClasses[size]}
        ${variantClasses[variant]}
        rounded transition-all duration-200 cursor-pointer
        ${copied ? 'text-green-600' : ''}
        ${className}
      `}
      title={copied ? 'Copied!' : title}
    >
      {copied ? (
        <CheckIcon className={`${sizeClasses[size]} stroke-2`} />
      ) : (
        <svg className={`${sizeClasses[size]} stroke-2`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
      )}
    </button>
  );
}
