#!/bin/bash

# RouKey API Key Test Script (Bash/curl)
# Quick tests for the user-generated API key functionality

API_KEY="rk_live_c5ae5a36_9Nl8iikIL2SDSNt6R4TujRKEg8xzUkK6"
BASE_URL="http://localhost:3000"
ENDPOINT="$BASE_URL/api/external/v1/chat/completions"

echo "🚀 RouKey API Key Tests (curl)"
echo "🔑 API Key: ${API_KEY:0:20}..."
echo "🌐 Endpoint: $ENDPOINT"
echo "📅 Started at: $(date -Iseconds)"
echo ""

# Test 1: Simple greeting
echo "🧪 Test 1: Simple greeting"
echo "=" | tr '=' '=' | head -c 50; echo ""
curl -X POST "$ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -H "User-Agent: RouKey-Test-Script-Curl/1.0" \
  -d '{
    "messages": [{"role": "user", "content": "hi"}],
    "stream": false,
    "max_tokens": 100
  }' \
  -w "\n📊 Status: %{http_code}\n⏱️  Duration: %{time_total}s\n" \
  -s
echo ""

sleep 2

# Test 2: Code generation
echo "🧪 Test 2: Code generation"
echo "=" | tr '=' '=' | head -c 50; echo ""
curl -X POST "$ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -H "User-Agent: RouKey-Test-Script-Curl/1.0" \
  -d '{
    "messages": [{"role": "user", "content": "code a snake game in python"}],
    "stream": false,
    "max_tokens": 500
  }' \
  -w "\n📊 Status: %{http_code}\n⏱️  Duration: %{time_total}s\n" \
  -s
echo ""

sleep 2

# Test 3: Streaming test
echo "🧪 Test 3: Streaming response"
echo "=" | tr '=' '=' | head -c 50; echo ""
echo "📡 Streaming response:"
echo "─" | tr '─' '─' | head -c 30; echo ""
curl -X POST "$ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -H "User-Agent: RouKey-Test-Script-Curl/1.0" \
  -d '{
    "messages": [{"role": "user", "content": "hi"}],
    "stream": true,
    "max_tokens": 100
  }' \
  -N
echo ""
echo "─" | tr '─' '─' | head -c 30; echo ""

sleep 2

# Test 4: Role-based routing
echo "🧪 Test 4: Role-based routing"
echo "=" | tr '=' '=' | head -c 50; echo ""
curl -X POST "$ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -H "User-Agent: RouKey-Test-Script-Curl/1.0" \
  -d '{
    "messages": [{"role": "user", "content": "Explain quantum computing"}],
    "role": "science_expert",
    "stream": false,
    "max_tokens": 300
  }' \
  -w "\n📊 Status: %{http_code}\n⏱️  Duration: %{time_total}s\n" \
  -s
echo ""

sleep 2

# Test 5: Temperature test
echo "🧪 Test 5: Temperature test (creative)"
echo "=" | tr '=' '=' | head -c 50; echo ""
curl -X POST "$ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -H "User-Agent: RouKey-Test-Script-Curl/1.0" \
  -d '{
    "messages": [{"role": "user", "content": "Write a creative story about a robot"}],
    "temperature": 1.5,
    "stream": false,
    "max_tokens": 300
  }' \
  -w "\n📊 Status: %{http_code}\n⏱️  Duration: %{time_total}s\n" \
  -s
echo ""

echo "📅 Completed at: $(date -Iseconds)"
echo "✅ All curl tests completed!"
echo ""
echo "💡 For more detailed testing, use:"
echo "   node test-api-key.js"
echo "   python3 test-api-key.py"
