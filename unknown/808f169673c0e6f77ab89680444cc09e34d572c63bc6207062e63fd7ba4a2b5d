import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Terms of Service - RouKey',
  description: 'RouKey Terms of Service. Understand your rights and responsibilities when using our AI routing platform.',
  keywords: ['terms of service', 'user agreement', 'AI routing', 'RouKey', 'legal terms'],
  openGraph: {
    title: 'Terms of Service - RouKey',
    description: 'RouKey Terms of Service. Understand your rights and responsibilities when using our AI routing platform.',
    type: 'website',
    url: 'https://roukey.online/terms',
    images: [
      {
        url: 'https://roukey.online/og-terms.jpg',
        width: 1200,
        height: 630,
        alt: 'RouKey Terms of Service',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Terms of Service - RouKey',
    description: 'Understand your rights and responsibilities when using RouKey AI routing platform.',
    images: ['https://roukey.online/og-terms.jpg'],
  },
  alternates: {
    canonical: 'https://roukey.online/terms',
  },
};

export default function TermsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
