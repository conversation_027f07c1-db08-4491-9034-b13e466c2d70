-- Migration: Allow duplicate API keys but prevent duplicate models per configuration
-- This migration changes the uniqueness constraint from API keys to models within configurations

-- 1. Drop the existing unique constraint on API key hash per config
ALTER TABLE public.api_keys 
DROP CONSTRAINT IF EXISTS unique_key_hash_per_config;

-- 2. Add new unique constraint to prevent duplicate models per configuration
-- This ensures that within a single configuration, each model can only be used once
-- regardless of which API key is used
ALTER TABLE public.api_keys 
ADD CONSTRAINT unique_model_per_config UNIQUE (custom_api_config_id, predefined_model_id);

-- 3. Add comment explaining the new constraint
COMMENT ON CONSTRAINT unique_model_per_config ON public.api_keys IS 
'Ensures each model (provider + model combination) can only be used once per configuration, regardless of API key used. Allows duplicate API keys but prevents duplicate models.';

-- 4. Create index for performance on the new constraint
CREATE INDEX IF NOT EXISTS idx_api_keys_config_model ON public.api_keys(custom_api_config_id, predefined_model_id);

-- 5. Add comment explaining the change in behavior
COMMENT ON COLUMN public.api_keys.api_key_hash IS 
'SHA-256 hash of the raw API key. Note: Duplicate API keys are now allowed within a configuration, but duplicate models are not.';

-- Migration completed successfully
SELECT 'Migration completed: API keys can now be duplicated, but models must be unique per configuration' AS status;
