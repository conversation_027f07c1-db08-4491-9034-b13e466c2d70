/**
 * Test script for Jina Reranker integration
 * Run with: node test-reranker.js
 */

const { jina<PERSON>eranker } = require('./src/lib/reranker/jina.ts');

async function testReranker() {
  console.log('🧪 Testing Jina Reranker Integration...\n');

  const query = "How to implement authentication in a web application?";
  
  const testDocuments = [
    {
      content: "Authentication is the process of verifying user identity. Common methods include passwords, OAuth, and JWT tokens.",
      document_id: "doc1",
      similarity: 0.75,
      metadata: { filename: "auth-guide.md" }
    },
    {
      content: "Web development involves creating websites and web applications using HTML, CSS, and JavaScript.",
      document_id: "doc2", 
      similarity: 0.65,
      metadata: { filename: "web-dev-basics.md" }
    },
    {
      content: "JWT (JSON Web Tokens) are a secure way to transmit information between parties. They're commonly used for authentication.",
      document_id: "doc3",
      similarity: 0.70,
      metadata: { filename: "jwt-tokens.md" }
    },
    {
      content: "Database design principles include normalization, indexing, and proper relationship modeling.",
      document_id: "doc4",
      similarity: 0.60,
      metadata: { filename: "database-design.md" }
    },
    {
      content: "OAuth 2.0 is an authorization framework that enables applications to obtain limited access to user accounts.",
      document_id: "doc5",
      similarity: 0.72,
      metadata: { filename: "oauth-guide.md" }
    }
  ];

  try {
    console.log(`📝 Query: "${query}"`);
    console.log(`📚 Documents to rerank: ${testDocuments.length}\n`);

    console.log('📊 Original ranking (by similarity):');
    testDocuments
      .sort((a, b) => b.similarity - a.similarity)
      .forEach((doc, index) => {
        console.log(`  ${index + 1}. ${doc.metadata.filename} (similarity: ${doc.similarity})`);
        console.log(`     "${doc.content.substring(0, 80)}..."\n`);
      });

    console.log('🔄 Applying Jina reranker...\n');

    const rerankedResults = await jinaReranker.rerankDocuments(query, testDocuments, 3);

    console.log('🎯 Reranked results:');
    rerankedResults.forEach((result, index) => {
      console.log(`  ${index + 1}. ${result.metadata.filename}`);
      console.log(`     Original similarity: ${result.original_similarity}`);
      console.log(`     Rerank score: ${result.rerank_score}`);
      console.log(`     Final score: ${result.final_score}`);
      console.log(`     "${result.content.substring(0, 80)}..."\n`);
    });

    console.log('✅ Reranker test completed successfully!');
    
    // Show usage stats
    const stats = jinaReranker.getUsageStats();
    console.log('\n📈 API Key Usage Stats:');
    Object.entries(stats).forEach(([key, stat]) => {
      console.log(`  ${key}: ${stat.requests} requests, ${stat.tokens} tokens, ${stat.errors} errors`);
    });

  } catch (error) {
    console.error('❌ Reranker test failed:', error.message);
    console.error(error.stack);
  }
}

// Run the test
testReranker();
