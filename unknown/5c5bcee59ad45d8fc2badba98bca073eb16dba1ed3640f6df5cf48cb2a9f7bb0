-- Create orchestration_executions table
CREATE TABLE IF NOT EXISTS public.orchestration_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    custom_api_config_id UUID REFERENCES public.custom_api_configs(id) ON DELETE CASCADE,
    original_prompt TEXT NOT NULL,
    total_steps INTEGER NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE,
    total_duration_ms INTEGER,
    total_tokens INTEGER,
    total_cost DECIMAL(10, 6)
);

-- Create orchestration_steps table
CREATE TABLE IF NOT EXISTS public.orchestration_steps (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    execution_id UUID REFERENCES public.orchestration_executions(id) ON DELETE CASCADE,
    step_number INTEGER NOT NULL,
    role_id TEXT NOT NULL,
    api_key_id UUID REFERENCES public.api_keys(id) ON DELETE SET NULL,
    model_name TEXT,
    prompt TEXT NOT NULL,
    response TEXT,
    status TEXT NOT NULL CHECK (status IN ('waiting', 'pending', 'in_progress', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_ms INTEGER,
    tokens_in INTEGER,
    tokens_out INTEGER,
    cost DECIMAL(10, 6),
    error_message TEXT
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS orchestration_executions_user_id_idx ON public.orchestration_executions(user_id);
CREATE INDEX IF NOT EXISTS orchestration_executions_status_idx ON public.orchestration_executions(status);
CREATE INDEX IF NOT EXISTS orchestration_steps_execution_id_idx ON public.orchestration_steps(execution_id);
CREATE INDEX IF NOT EXISTS orchestration_steps_status_idx ON public.orchestration_steps(status);

-- Add RLS policies
ALTER TABLE public.orchestration_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orchestration_steps ENABLE ROW LEVEL SECURITY;

-- Policy for orchestration_executions
CREATE POLICY "Users can view their own orchestration executions"
    ON public.orchestration_executions
    FOR SELECT
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own orchestration executions"
    ON public.orchestration_executions
    FOR INSERT
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own orchestration executions"
    ON public.orchestration_executions
    FOR UPDATE
    USING (auth.uid() = user_id);

-- Policy for orchestration_steps
CREATE POLICY "Users can view steps for their own orchestration executions"
    ON public.orchestration_steps
    FOR SELECT
    USING (
        execution_id IN (
            SELECT id FROM public.orchestration_executions
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert steps for their own orchestration executions"
    ON public.orchestration_steps
    FOR INSERT
    WITH CHECK (
        execution_id IN (
            SELECT id FROM public.orchestration_executions
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update steps for their own orchestration executions"
    ON public.orchestration_steps
    FOR UPDATE
    USING (
        execution_id IN (
            SELECT id FROM public.orchestration_executions
            WHERE user_id = auth.uid()
        )
    );

-- Allow service role to access these tables
CREATE POLICY "Service role can access all orchestration executions"
    ON public.orchestration_executions
    FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Service role can access all orchestration steps"
    ON public.orchestration_steps
    FOR ALL
    USING (auth.jwt() ->> 'role' = 'service_role');