-- Create synthesis_storage table for persistent chunked synthesis storage
CREATE TABLE synthesis_storage (
    synthesis_id TEXT PRIMARY KEY,
    conversation_id TEXT NOT NULL,
    complete_synthesis TEXT NOT NULL,
    chunks TEXT[] NOT NULL,
    total_chunks INTEGER NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_access_time TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for faster lookups by conversation_id
CREATE INDEX idx_synthesis_storage_conversation_id ON synthesis_storage(conversation_id);

-- Create index for cleanup queries based on last_access_time
CREATE INDEX idx_synthesis_storage_last_access_time ON synthesis_storage(last_access_time);

-- Add RLS (Row Level Security) policies
ALTER TABLE synthesis_storage ENABLE ROW LEVEL SECURITY;

-- Policy to allow all operations for now (can be restricted later if needed)
CREATE POLICY "Allow all operations on synthesis_storage" ON synthesis_storage
    FOR ALL USING (true);

-- Add comment explaining the table purpose
COMMENT ON TABLE synthesis_storage IS 'Stores chunked synthesis responses for continuation across server restarts';
COMMENT ON COLUMN synthesis_storage.synthesis_id IS 'Unique identifier for the synthesis (format: synthesis_{conversation_id}_{timestamp})';
COMMENT ON COLUMN synthesis_storage.conversation_id IS 'ID of the conversation this synthesis belongs to';
COMMENT ON COLUMN synthesis_storage.complete_synthesis IS 'The complete synthesis content before chunking';
COMMENT ON COLUMN synthesis_storage.chunks IS 'Array of pre-chunked synthesis content for streaming';
COMMENT ON COLUMN synthesis_storage.total_chunks IS 'Total number of chunks in the chunks array';
COMMENT ON COLUMN synthesis_storage.created_at IS 'When the synthesis was first stored';
COMMENT ON COLUMN synthesis_storage.last_access_time IS 'Last time this synthesis was accessed (for TTL cleanup)';
