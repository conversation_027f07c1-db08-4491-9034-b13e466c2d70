import { type NextRequest, NextResponse } from 'next/server';
import { ApiKeyAuthMiddleware } from '@/lib/userApiKeys/authMiddleware';
import { AsyncJobManager } from '@/lib/async/jobManager';

const authMiddleware = new ApiKeyAuthMiddleware();
const jobManager = new AsyncJobManager();

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
    // 1. Authenticate using user-generated API key
    const authResult = await authMiddleware.authenticateRequest(request);
    
    if (!authResult.success) {
      return NextResponse.json(
        {
          error: {
            message: authResult.error,
            type: 'authentication_error',
            code: 'invalid_api_key'
          }
        },
        { status: authResult.statusCode || 401 }
      );
    }

    const { userApiKey, userConfig } = authResult;

    // 2. Await params and get job
    const { jobId } = await params;
    const job = await jobManager.getJob(jobId);
    
    if (!job) {
      return NextResponse.json(
        {
          error: {
            message: 'Job not found',
            type: 'not_found_error',
            code: 'job_not_found'
          }
        },
        { status: 404 }
      );
    }

    // 3. Check if user owns this job
    if (job.user_id !== userConfig!.user_id) {
      return NextResponse.json(
        {
          error: {
            message: 'Access denied to this job',
            type: 'permission_error',
            code: 'access_denied'
          }
        },
        { status: 403 }
      );
    }

    // 4. Calculate progress and time estimates
    const now = new Date();
    const createdAt = new Date(job.created_at);
    const elapsedMinutes = Math.floor((now.getTime() - createdAt.getTime()) / 60000);
    
    let estimatedRemainingMinutes = 0;
    if (job.status === 'processing' && job.estimated_completion) {
      const estimatedCompletion = new Date(job.estimated_completion);
      estimatedRemainingMinutes = Math.max(0, Math.floor((estimatedCompletion.getTime() - now.getTime()) / 60000));
    }

    // 5. Return job status
    const response = {
      job_id: job.id,
      status: job.status,
      progress_percentage: job.progress_percentage || 0,
      created_at: job.created_at,
      started_at: job.started_at,
      completed_at: job.completed_at,
      estimated_completion: job.estimated_completion,
      estimated_remaining_minutes: estimatedRemainingMinutes,
      elapsed_minutes: elapsedMinutes,
      roles_detected: job.roles_detected || [],
      error_message: job.error_message,
      webhook_configured: !!job.webhook_url,
      result_available: job.status === 'completed',
      result_url: job.status === 'completed'
        ? `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/external/v1/async/result/${jobId}`
        : null
    };

    return NextResponse.json(response, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
        'Cache-Control': job.status === 'completed' || job.status === 'failed' 
          ? 'public, max-age=3600' // Cache completed/failed jobs for 1 hour
          : 'no-cache' // Don't cache pending/processing jobs
      }
    });

  } catch (error) {
    console.error('Error in async status API:', error);
    return NextResponse.json(
      {
        error: {
          message: 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      },
      { status: 500 }
    );
  }
}

// Handle CORS preflight requests
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
      'Access-Control-Max-Age': '86400',
    },
  });
}
