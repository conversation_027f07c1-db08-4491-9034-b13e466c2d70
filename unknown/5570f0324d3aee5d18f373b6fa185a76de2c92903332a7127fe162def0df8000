-- Complete Features Migration: Temperature Control, Chat History, and Training
-- Run this migration in your Supabase SQL Editor

-- 1. Add temperature column to api_keys table
ALTER TABLE api_keys 
ADD COLUMN IF NOT EXISTS temperature DECIMAL(3,2) DEFAULT 1.0 CHECK (temperature >= 0.0 AND temperature <= 2.0);

-- Add comment for temperature column
COMMENT ON COLUMN api_keys.temperature IS 'Temperature setting for API requests (0.0-2.0)';

-- 2. Create chat_conversations table
CREATE TABLE IF NOT EXISTS chat_conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    custom_api_config_id UUID NOT NULL REFERENCES custom_api_configs(id) ON DELETE CASCADE,
    user_id UUID, -- Will be used when authentication is implemented
    title TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for chat_conversations
CREATE INDEX IF NOT EXISTS idx_chat_conversations_config_id ON chat_conversations(custom_api_config_id);
CREATE INDEX IF NOT EXISTS idx_chat_conversations_user_id ON chat_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_conversations_updated_at ON chat_conversations(updated_at DESC);

-- Add comments for chat_conversations
COMMENT ON TABLE chat_conversations IS 'Chat conversation sessions';
COMMENT ON COLUMN chat_conversations.custom_api_config_id IS 'Reference to the API configuration used for this conversation';
COMMENT ON COLUMN chat_conversations.user_id IS 'User who owns this conversation (for future auth implementation)';
COMMENT ON COLUMN chat_conversations.title IS 'Display title for the conversation';

-- 3. Create chat_messages table
CREATE TABLE IF NOT EXISTS chat_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES chat_conversations(id) ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system', 'error')),
    content JSONB NOT NULL,
    api_key_id UUID REFERENCES api_keys(id) ON DELETE SET NULL,
    model_used TEXT,
    temperature_used DECIMAL(3,2),
    tokens_prompt INTEGER,
    tokens_completion INTEGER,
    cost DECIMAL(10,6),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for chat_messages
CREATE INDEX IF NOT EXISTS idx_chat_messages_conversation_id ON chat_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_chat_messages_role ON chat_messages(role);

-- Add comments for chat_messages
COMMENT ON TABLE chat_messages IS 'Individual messages within chat conversations';
COMMENT ON COLUMN chat_messages.conversation_id IS 'Reference to the parent conversation';
COMMENT ON COLUMN chat_messages.role IS 'Message role: user, assistant, system, or error';
COMMENT ON COLUMN chat_messages.content IS 'Message content in structured format (array of content parts)';
COMMENT ON COLUMN chat_messages.api_key_id IS 'API key used for this message (if applicable)';
COMMENT ON COLUMN chat_messages.model_used IS 'Model that generated this message (for assistant messages)';
COMMENT ON COLUMN chat_messages.temperature_used IS 'Temperature setting used for this message';
COMMENT ON COLUMN chat_messages.tokens_prompt IS 'Input tokens used';
COMMENT ON COLUMN chat_messages.tokens_completion IS 'Output tokens generated';
COMMENT ON COLUMN chat_messages.cost IS 'Cost for this message in USD';

-- 4. Create or update training_jobs table
CREATE TABLE IF NOT EXISTS training_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    custom_api_config_id UUID NOT NULL REFERENCES custom_api_configs(id) ON DELETE CASCADE,
    user_id UUID, -- Will be used when authentication is implemented
    name TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    training_data JSONB,
    parameters JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add missing columns to training_jobs if they don't exist
DO $$
BEGIN
    -- Add result_model_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'training_jobs' AND column_name = 'result_model_id') THEN
        ALTER TABLE training_jobs ADD COLUMN result_model_id TEXT;
    END IF;

    -- Add error_message column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'training_jobs' AND column_name = 'error_message') THEN
        ALTER TABLE training_jobs ADD COLUMN error_message TEXT;
    END IF;

    -- Add started_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'training_jobs' AND column_name = 'started_at') THEN
        ALTER TABLE training_jobs ADD COLUMN started_at TIMESTAMPTZ;
    END IF;

    -- Add completed_at column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'training_jobs' AND column_name = 'completed_at') THEN
        ALTER TABLE training_jobs ADD COLUMN completed_at TIMESTAMPTZ;
    END IF;
END $$;

-- Add indexes for training_jobs
CREATE INDEX IF NOT EXISTS idx_training_jobs_config_id ON training_jobs(custom_api_config_id);
CREATE INDEX IF NOT EXISTS idx_training_jobs_user_id ON training_jobs(user_id);
CREATE INDEX IF NOT EXISTS idx_training_jobs_status ON training_jobs(status);
CREATE INDEX IF NOT EXISTS idx_training_jobs_created_at ON training_jobs(created_at DESC);

-- Add comments for training_jobs (safely)
DO $$
BEGIN
    -- Add table comment
    EXECUTE 'COMMENT ON TABLE training_jobs IS ''Fine-tuning and training job records''';

    -- Add column comments only if columns exist
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_jobs' AND column_name = 'custom_api_config_id') THEN
        EXECUTE 'COMMENT ON COLUMN training_jobs.custom_api_config_id IS ''API configuration used for training''';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_jobs' AND column_name = 'user_id') THEN
        EXECUTE 'COMMENT ON COLUMN training_jobs.user_id IS ''User who created this training job''';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_jobs' AND column_name = 'name') THEN
        EXECUTE 'COMMENT ON COLUMN training_jobs.name IS ''Display name for the training job''';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_jobs' AND column_name = 'status') THEN
        EXECUTE 'COMMENT ON COLUMN training_jobs.status IS ''Current status of the training job''';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_jobs' AND column_name = 'progress_percentage') THEN
        EXECUTE 'COMMENT ON COLUMN training_jobs.progress_percentage IS ''Training progress (0-100)''';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_jobs' AND column_name = 'training_data') THEN
        EXECUTE 'COMMENT ON COLUMN training_jobs.training_data IS ''Training prompts and configuration data''';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_jobs' AND column_name = 'parameters') THEN
        EXECUTE 'COMMENT ON COLUMN training_jobs.parameters IS ''Training parameters (learning rate, epochs, etc.)''';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_jobs' AND column_name = 'result_model_id') THEN
        EXECUTE 'COMMENT ON COLUMN training_jobs.result_model_id IS ''ID of the resulting fine-tuned model''';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_jobs' AND column_name = 'error_message') THEN
        EXECUTE 'COMMENT ON COLUMN training_jobs.error_message IS ''Error message if training failed''';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_jobs' AND column_name = 'started_at') THEN
        EXECUTE 'COMMENT ON COLUMN training_jobs.started_at IS ''When training actually started''';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_jobs' AND column_name = 'completed_at') THEN
        EXECUTE 'COMMENT ON COLUMN training_jobs.completed_at IS ''When training completed or failed''';
    END IF;
END $$;

-- 5. Create or update training_files table
CREATE TABLE IF NOT EXISTS training_files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    training_job_id UUID NOT NULL REFERENCES training_jobs(id) ON DELETE CASCADE,
    original_filename TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    file_type TEXT NOT NULL,
    storage_path TEXT NOT NULL,
    processing_status TEXT NOT NULL DEFAULT 'pending' CHECK (processing_status IN ('pending', 'processing', 'completed', 'failed')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add missing columns to training_files if they don't exist
DO $$
BEGIN
    -- Add extracted_content column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'training_files' AND column_name = 'extracted_content') THEN
        ALTER TABLE training_files ADD COLUMN extracted_content TEXT;
    END IF;

    -- Add metadata column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'training_files' AND column_name = 'metadata') THEN
        ALTER TABLE training_files ADD COLUMN metadata JSONB;
    END IF;
END $$;

-- Add indexes for training_files
CREATE INDEX IF NOT EXISTS idx_training_files_job_id ON training_files(training_job_id);
CREATE INDEX IF NOT EXISTS idx_training_files_status ON training_files(processing_status);
CREATE INDEX IF NOT EXISTS idx_training_files_created_at ON training_files(created_at);

-- Add comments for training_files (safely)
DO $$
BEGIN
    -- Add table comment
    EXECUTE 'COMMENT ON TABLE training_files IS ''Files uploaded for training jobs''';

    -- Add column comments only if columns exist
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_files' AND column_name = 'training_job_id') THEN
        EXECUTE 'COMMENT ON COLUMN training_files.training_job_id IS ''Reference to the parent training job''';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_files' AND column_name = 'original_filename') THEN
        EXECUTE 'COMMENT ON COLUMN training_files.original_filename IS ''Original name of the uploaded file''';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_files' AND column_name = 'file_size') THEN
        EXECUTE 'COMMENT ON COLUMN training_files.file_size IS ''File size in bytes''';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_files' AND column_name = 'file_type') THEN
        EXECUTE 'COMMENT ON COLUMN training_files.file_type IS ''MIME type of the file''';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_files' AND column_name = 'storage_path') THEN
        EXECUTE 'COMMENT ON COLUMN training_files.storage_path IS ''Path where the file is stored''';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_files' AND column_name = 'processing_status') THEN
        EXECUTE 'COMMENT ON COLUMN training_files.processing_status IS ''Status of file processing''';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_files' AND column_name = 'extracted_content') THEN
        EXECUTE 'COMMENT ON COLUMN training_files.extracted_content IS ''Text content extracted from the file''';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'training_files' AND column_name = 'metadata') THEN
        EXECUTE 'COMMENT ON COLUMN training_files.metadata IS ''Additional file metadata (word count, language, etc.)''';
    END IF;
END $$;

-- 6. Create updated_at triggers for tables that need them
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers (safely)
DO $$
BEGIN
    -- Chat conversations trigger
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'chat_conversations') THEN
        DROP TRIGGER IF EXISTS update_chat_conversations_updated_at ON chat_conversations;
        CREATE TRIGGER update_chat_conversations_updated_at
            BEFORE UPDATE ON chat_conversations
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;

    -- Training jobs trigger
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'training_jobs') THEN
        DROP TRIGGER IF EXISTS update_training_jobs_updated_at ON training_jobs;
        CREATE TRIGGER update_training_jobs_updated_at
            BEFORE UPDATE ON training_jobs
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;

    -- Training files trigger
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'training_files') THEN
        DROP TRIGGER IF EXISTS update_training_files_updated_at ON training_files;
        CREATE TRIGGER update_training_files_updated_at
            BEFORE UPDATE ON training_files
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 7. Add RLS policies (when authentication is implemented)
-- Note: These are commented out for now since authentication is not yet implemented
-- Uncomment and modify when user authentication is added

/*
-- Enable RLS on new tables
ALTER TABLE chat_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE training_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE training_files ENABLE ROW LEVEL SECURITY;

-- Chat conversations policies
CREATE POLICY "Users can view their own conversations" ON chat_conversations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own conversations" ON chat_conversations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own conversations" ON chat_conversations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own conversations" ON chat_conversations
    FOR DELETE USING (auth.uid() = user_id);

-- Chat messages policies
CREATE POLICY "Users can view messages in their conversations" ON chat_messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM chat_conversations 
            WHERE id = chat_messages.conversation_id 
            AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create messages in their conversations" ON chat_messages
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM chat_conversations 
            WHERE id = chat_messages.conversation_id 
            AND user_id = auth.uid()
        )
    );

-- Training jobs policies
CREATE POLICY "Users can view their own training jobs" ON training_jobs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own training jobs" ON training_jobs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own training jobs" ON training_jobs
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own training jobs" ON training_jobs
    FOR DELETE USING (auth.uid() = user_id);

-- Training files policies
CREATE POLICY "Users can view files in their training jobs" ON training_files
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM training_jobs 
            WHERE id = training_files.training_job_id 
            AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create files in their training jobs" ON training_files
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM training_jobs 
            WHERE id = training_files.training_job_id 
            AND user_id = auth.uid()
        )
    );
*/

-- Migration completed successfully
SELECT 'Migration completed: Temperature control, chat history, and training features added' as status;
