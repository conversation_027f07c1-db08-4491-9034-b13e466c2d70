import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { jinaEmbeddings } from '@/lib/embeddings/jina';
import { jinaReranker } from '@/lib/reranker/jina';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { query, configId, limit = 8, threshold = 0.5, useReranker = true } = body;  // Improved defaults with reranker

    if (!query || !configId) {
      return NextResponse.json({ 
        error: 'Query and configId are required' 
      }, { status: 400 });
    }

    console.log(`[Document Search] Searching for: "${query}" in config: ${configId} (reranker: ${useReranker})`);

    // Generate embedding for the search query using Jina v3
    const queryEmbedding = await jinaEmbeddings.embedQuery(query);

    // Search for similar document chunks using our custom function
    // Get more results initially if we're going to rerank them
    const initialLimit = useReranker ? Math.min(limit * 3, 50) : limit;

    const { data: results, error } = await supabase.rpc('search_document_chunks', {
      query_embedding: queryEmbedding,
      config_id: configId,
      user_id_param: user.id,
      match_threshold: threshold,
      match_count: initialLimit
    });

    if (error) {
      console.error('[Document Search] Search error:', error);
      return NextResponse.json({
        error: 'Search failed',
        details: error.message
      }, { status: 500 });
    }

    if (!results || results.length === 0) {
      console.log('[Document Search] No results found');
      return NextResponse.json({
        success: true,
        results: [],
        query,
        total_results: 0
      });
    }

    console.log(`[Document Search] Found ${results.length} initial results from embedding search`);

    let finalResults = results;

    // Apply reranking if enabled and we have results
    if (useReranker && results.length > 1) {
      try {
        console.log(`[Document Search] Applying Jina reranker to ${results.length} results`);

        // Prepare documents for reranking
        const documentsToRerank = results.map((result: any) => ({
          content: result.content,
          document_id: result.document_id,
          similarity: result.similarity,
          metadata: result.metadata
        }));

        // Rerank using Jina reranker-m0
        const rerankedResults = await jinaReranker.rerankDocuments(
          query,
          documentsToRerank,
          limit // Final limit after reranking
        );

        // Convert reranked results back to the expected format
        finalResults = rerankedResults.map(reranked => ({
          document_id: reranked.document_id,
          content: reranked.content,
          similarity: reranked.original_similarity,
          rerank_score: reranked.rerank_score,
          final_score: reranked.final_score,
          metadata: reranked.metadata
        }));

        console.log(`[Document Search] Reranking complete. Returning top ${finalResults.length} results`);

      } catch (rerankerError) {
        console.error('[Document Search] Reranker failed, falling back to original results:', rerankerError);
        // Fallback to original results if reranking fails
        finalResults = results.slice(0, limit);
      }
    } else {
      // No reranking, just limit the results
      finalResults = results.slice(0, limit);
      console.log(`[Document Search] Reranking disabled or insufficient results. Returning ${finalResults.length} results`);
    }

    // Get document metadata for the final results
    if (finalResults && finalResults.length > 0) {
      const documentIds = [...new Set(finalResults.map((r: any) => r.document_id))];

      const { data: documents } = await supabase
        .from('documents')
        .select('id, filename, file_type')
        .in('id', documentIds);

      // Enhance results with document metadata
      const enhancedResults = finalResults.map((result: any) => {
        const document = documents?.find(d => d.id === result.document_id);
        return {
          ...result,
          document: document || null
        };
      });

      return NextResponse.json({
        success: true,
        results: enhancedResults,
        query,
        total_results: finalResults.length,
        reranked: useReranker && results.length > 1
      });
    }

    return NextResponse.json({
      success: true,
      results: [],
      query,
      total_results: 0
    });

  } catch (error: any) {
    console.error('[Document Search] Error:', error);
    return NextResponse.json({
      error: 'Search failed',
      details: error.message
    }, { status: 500 });
  }
}
