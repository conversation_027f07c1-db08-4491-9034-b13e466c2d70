import { type NextRequest, NextResponse } from 'next/server';
import { AsyncProcessor } from '@/lib/async/asyncProcessor';

const processor = new AsyncProcessor();

export async function POST(request: NextRequest) {
  try {
    // Verify internal authorization
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    if (token !== process.env.ROKEY_API_ACCESS_TOKEN) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    const { job_id } = await request.json();

    if (!job_id) {
      return NextResponse.json(
        { error: 'job_id is required' },
        { status: 400 }
      );
    }

    // Process the job asynchronously (don't wait for completion)
    processor.processJob(job_id).catch(error => {
      console.error(`Failed to process job ${job_id}:`, error);
    });

    return NextResponse.json({ 
      message: 'Job processing started',
      job_id 
    });

  } catch (error) {
    console.error('Error in internal async process API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Endpoint to process pending jobs (can be called by cron)
export async function GET(request: NextRequest) {
  try {
    // Verify internal authorization
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    if (token !== process.env.ROKEY_API_ACCESS_TOKEN) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    // Process pending jobs
    await processor.processPendingJobs();
    
    // Handle timeouts
    await processor.handleTimeouts();

    return NextResponse.json({ 
      message: 'Pending jobs processed successfully' 
    });

  } catch (error) {
    console.error('Error processing pending jobs:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
