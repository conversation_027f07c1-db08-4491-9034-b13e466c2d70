/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stripe/subscription-status/route";
exports.ids = ["app/api/stripe/subscription-status/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fsubscription-status%2Froute&page=%2Fapi%2Fstripe%2Fsubscription-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fsubscription-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fsubscription-status%2Froute&page=%2Fapi%2Fstripe%2Fsubscription-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fsubscription-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_stripe_subscription_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stripe/subscription-status/route.ts */ \"(rsc)/./src/app/api/stripe/subscription-status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stripe/subscription-status/route\",\n        pathname: \"/api/stripe/subscription-status\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/subscription-status/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\stripe\\\\subscription-status\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_stripe_subscription_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fsubscription-status%2Froute&page=%2Fapi%2Fstripe%2Fsubscription-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fsubscription-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/stripe/subscription-status/route.ts":
/*!*********************************************************!*\
  !*** ./src/app/api/stripe/subscription-status/route.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\nconst supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\nasync function GET(req) {\n    try {\n        const { searchParams } = new URL(req.url);\n        const userId = searchParams.get('userId');\n        console.log('Subscription status API called for user:', userId);\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing userId parameter'\n            }, {\n                status: 400\n            });\n        }\n        // Check for network connectivity issues\n        if (process.env.SUPABASE_SERVER_CONNECTIVITY_ISSUE === 'true') {\n            console.log('Network connectivity issue detected, returning fallback response');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                hasActiveSubscription: true,\n                tier: 'free',\n                status: 'active',\n                currentPeriodEnd: null,\n                cancelAtPeriodEnd: false,\n                isFree: true,\n                fallback: true,\n                message: 'Using fallback due to network connectivity issues'\n            });\n        }\n        // Get user's subscription and profile\n        const { data: subscription, error: subscriptionError } = await supabase.from('subscriptions').select('*').eq('user_id', userId).order('created_at', {\n            ascending: false\n        }).limit(1).single();\n        const { data: profile, error: profileError } = await supabase.from('user_profiles').select('subscription_tier, updated_at').eq('id', userId).single();\n        console.log('GET subscription-status - Data fetched:', {\n            userId,\n            subscriptionFound: !!subscription,\n            subscriptionTier: subscription?.tier,\n            subscriptionStatus: subscription?.status,\n            subscriptionUpdatedAt: subscription?.updated_at,\n            profileTier: profile?.subscription_tier,\n            profileUpdatedAt: profile?.updated_at,\n            subscriptionError: subscriptionError?.message,\n            profileError: profileError?.message\n        });\n        if (profileError) {\n            console.error('Error fetching user profile:', profileError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'User profile not found'\n            }, {\n                status: 404\n            });\n        }\n        // If no subscription found, user is on free tier\n        if (subscriptionError || !subscription) {\n            const tier = profile.subscription_tier || 'free';\n            console.log('GET subscription-status - No active subscription found:', {\n                userId,\n                subscriptionError: subscriptionError?.message,\n                profileTier: profile.subscription_tier,\n                finalTier: tier\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                hasActiveSubscription: tier !== 'free',\n                tier,\n                status: null,\n                currentPeriodEnd: null,\n                cancelAtPeriodEnd: false,\n                isFree: tier === 'free'\n            });\n        }\n        // Check if subscription is active\n        const isActive = subscription.status === 'active';\n        const currentPeriodEnd = new Date(subscription.current_period_end);\n        const isExpired = currentPeriodEnd < new Date();\n        console.log('GET subscription-status - Active subscription found:', {\n            userId,\n            tier: subscription.tier,\n            status: subscription.status,\n            isActive,\n            isExpired,\n            hasActiveSubscription: isActive && !isExpired\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            hasActiveSubscription: isActive && !isExpired,\n            tier: subscription.tier,\n            status: subscription.status,\n            currentPeriodEnd: subscription.current_period_end,\n            currentPeriodStart: subscription.current_period_start,\n            cancelAtPeriodEnd: subscription.cancel_at_period_end,\n            stripeCustomerId: subscription.stripe_customer_id,\n            stripeSubscriptionId: subscription.stripe_subscription_id,\n            isFree: subscription.is_free_tier || false\n        });\n    } catch (error) {\n        console.error('Error fetching subscription status:', error);\n        // If there's a network error, return fallback response\n        if (error instanceof Error && (error.message.includes('fetch failed') || error.message.includes('network'))) {\n            console.log('Network error detected, returning fallback response');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                hasActiveSubscription: true,\n                tier: 'free',\n                status: 'active',\n                currentPeriodEnd: null,\n                cancelAtPeriodEnd: false,\n                isFree: true,\n                fallback: true,\n                message: 'Using fallback due to network error'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(req) {\n    try {\n        const { userId } = await req.json();\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing userId'\n            }, {\n                status: 400\n            });\n        }\n        // Check for network connectivity issues\n        if (process.env.SUPABASE_SERVER_CONNECTIVITY_ISSUE === 'true') {\n            console.log('Network connectivity issue detected, returning fallback usage response');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                tier: 'free',\n                usage: {\n                    configurations: 0,\n                    apiKeys: 0,\n                    apiRequests: 0\n                },\n                limits: getTierLimits('free'),\n                canCreateConfig: true,\n                canCreateApiKey: true,\n                fallback: true,\n                message: 'Using fallback due to network connectivity issues'\n            });\n        }\n        // Get current usage for the user\n        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format\n        const { data: usage, error: usageError } = await supabase.from('usage_tracking').select('*').eq('user_id', userId).eq('month_year', currentMonth).single();\n        // Get user's current tier - check subscriptions table first, then user_profiles\n        // First try to get active subscription\n        const { data: subscription, error: subError } = await supabase.from('subscriptions').select('tier, status, updated_at, stripe_subscription_id').eq('user_id', userId).eq('status', 'active').order('created_at', {\n            ascending: false\n        }).limit(1).single();\n        // If no active subscription, get the most recent subscription regardless of status\n        // This helps handle cases where subscription status might be temporarily in transition\n        let fallbackSubscription = null;\n        if (subError) {\n            const { data: recentSub } = await supabase.from('subscriptions').select('tier, status, updated_at, stripe_subscription_id').eq('user_id', userId).order('updated_at', {\n                ascending: false\n            }).limit(1).single();\n            fallbackSubscription = recentSub;\n        }\n        const { data: profile, error: profError } = await supabase.from('user_profiles').select('subscription_tier, updated_at').eq('id', userId).single();\n        // Also check for ANY subscription (not just active) for debugging\n        const { data: allSubscriptions } = await supabase.from('subscriptions').select('tier, status, updated_at, stripe_subscription_id').eq('user_id', userId).order('created_at', {\n            ascending: false\n        });\n        // Use subscription tier if available and active, otherwise fall back to profile tier\n        // Priority: 1) Active subscription, 2) Recent subscription (if profile matches), 3) Profile tier\n        let tier;\n        if (subscription?.tier && subscription?.status === 'active') {\n            tier = subscription.tier;\n        } else if (fallbackSubscription?.tier && profile?.subscription_tier === fallbackSubscription.tier) {\n            // Use fallback subscription tier only if it matches the profile tier (indicates recent update)\n            tier = fallbackSubscription.tier;\n        } else {\n            tier = profile?.subscription_tier || 'free';\n        }\n        console.log('POST subscription-status - User tier determination:', {\n            userId,\n            activeSubscriptionTier: subscription?.tier,\n            activeSubscriptionStatus: subscription?.status,\n            activeSubscriptionUpdatedAt: subscription?.updated_at,\n            activeSubscriptionId: subscription?.stripe_subscription_id,\n            fallbackSubscriptionTier: fallbackSubscription?.tier,\n            fallbackSubscriptionStatus: fallbackSubscription?.status,\n            fallbackSubscriptionUpdatedAt: fallbackSubscription?.updated_at,\n            profileTier: profile?.subscription_tier,\n            profileUpdatedAt: profile?.updated_at,\n            finalTier: tier,\n            tierDeterminationLogic: subscription?.tier && subscription?.status === 'active' ? 'Using active subscription' : fallbackSubscription?.tier && profile?.subscription_tier === fallbackSubscription.tier ? 'Using fallback subscription (matches profile)' : 'Using profile tier',\n            allSubscriptionsCount: allSubscriptions?.length || 0,\n            allSubscriptions: allSubscriptions?.map((s)=>({\n                    tier: s.tier,\n                    status: s.status,\n                    updatedAt: s.updated_at,\n                    stripeId: s.stripe_subscription_id\n                })),\n            subscriptionError: subError?.message,\n            profileError: profError?.message\n        });\n        // Get configuration and API key counts\n        const { count: configCount } = await supabase.from('custom_api_configs').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        const { count: apiKeyCount } = await supabase.from('api_keys').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        // Note: Workflow features will be added in future updates\n        // Calculate tier limits\n        const limits = getTierLimits(tier);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            tier,\n            usage: {\n                configurations: configCount || 0,\n                apiKeys: apiKeyCount || 0,\n                apiRequests: usage?.api_requests_count || 0\n            },\n            limits,\n            canCreateConfig: (configCount || 0) < limits.configurations,\n            canCreateApiKey: (apiKeyCount || 0) < limits.apiKeysPerConfig\n        });\n    } catch (error) {\n        console.error('Error fetching usage status:', error);\n        // If there's a network error, return fallback response\n        if (error instanceof Error && (error.message.includes('fetch failed') || error.message.includes('network'))) {\n            console.log('Network error detected, returning fallback usage response');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                tier: 'free',\n                usage: {\n                    configurations: 0,\n                    apiKeys: 0,\n                    apiRequests: 0\n                },\n                limits: getTierLimits('free'),\n                canCreateConfig: true,\n                canCreateApiKey: true,\n                fallback: true,\n                message: 'Using fallback due to network error'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nfunction getTierLimits(tier) {\n    switch(tier){\n        case 'free':\n            return {\n                configurations: 1,\n                apiKeysPerConfig: 3,\n                apiRequests: 999999,\n                canUseAdvancedRouting: false,\n                canUseCustomRoles: false,\n                maxCustomRoles: 0,\n                canUsePromptEngineering: false,\n                canUseKnowledgeBase: false,\n                knowledgeBaseDocuments: 0,\n                canUseSemanticCaching: false\n            };\n        case 'starter':\n            return {\n                configurations: 5,\n                apiKeysPerConfig: 5,\n                apiRequests: 999999,\n                canUseAdvancedRouting: true,\n                canUseCustomRoles: true,\n                maxCustomRoles: 3,\n                canUsePromptEngineering: true,\n                canUseKnowledgeBase: false,\n                knowledgeBaseDocuments: 0,\n                canUseSemanticCaching: false\n            };\n        case 'professional':\n            return {\n                configurations: 20,\n                apiKeysPerConfig: 15,\n                apiRequests: 999999,\n                canUseAdvancedRouting: true,\n                canUseCustomRoles: true,\n                maxCustomRoles: 999999,\n                canUsePromptEngineering: true,\n                canUseKnowledgeBase: true,\n                knowledgeBaseDocuments: 5,\n                canUseSemanticCaching: true\n            };\n        case 'enterprise':\n            return {\n                configurations: 999999,\n                apiKeysPerConfig: 999999,\n                apiRequests: 999999,\n                canUseAdvancedRouting: true,\n                canUseCustomRoles: true,\n                maxCustomRoles: 999999,\n                canUsePromptEngineering: true,\n                canUseKnowledgeBase: true,\n                knowledgeBaseDocuments: 999999,\n                canUseSemanticCaching: true\n            };\n        default:\n            return {\n                configurations: 1,\n                apiKeysPerConfig: 3,\n                apiRequests: 999999,\n                canUseAdvancedRouting: false,\n                canUseCustomRoles: false,\n                maxCustomRoles: 0,\n                canUsePromptEngineering: false,\n                canUseKnowledgeBase: false,\n                knowledgeBaseDocuments: 0,\n                canUseSemanticCaching: false\n            };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stripe/subscription-status/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fsubscription-status%2Froute&page=%2Fapi%2Fstripe%2Fsubscription-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fsubscription-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();