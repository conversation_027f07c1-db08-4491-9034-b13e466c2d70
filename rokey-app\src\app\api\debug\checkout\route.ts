import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { action, ...data } = body;
    
    // Log to terminal with timestamp
    const timestamp = new Date().toISOString();
    console.log(`\n🔍 [${timestamp}] CHECKOUT DEBUG - ${action}`);
    console.log('📊 Data:', JSON.stringify(data, null, 2));
    console.log('─'.repeat(80));
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Debug endpoint error:', error);
    return NextResponse.json({ error: 'Debug failed' }, { status: 500 });
  }
}
