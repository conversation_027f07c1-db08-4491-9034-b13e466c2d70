# Apply Orchestration Enhancement Migration

To fix the database schema issue and enable full orchestration tracking, run this SQL in your Supabase SQL Editor:

```sql
-- Add enhanced orchestration columns for multi-role routing
-- Migration: 20250107_add_orchestration_enhancements.sql

-- Add assignment_type column to track how API keys were assigned
ALTER TABLE public.orchestration_steps 
ADD COLUMN IF NOT EXISTS assignment_type TEXT CHECK (assignment_type IN ('assigned', 'general_chat_fallback', 'first_available_fallback', 'none'));

-- Add confidence column to track role confidence scores
ALTER TABLE public.orchestration_steps 
ADD COLUMN IF NOT EXISTS confidence DECIMAL(3,2) DEFAULT 0.8 CHECK (confidence >= 0.0 AND confidence <= 1.0);

-- Add comments for the new columns
COMMENT ON COLUMN public.orchestration_steps.assignment_type IS 'How the API key was assigned: assigned (dedicated), general_chat_fallback, first_available_fallback, or none';
COMMENT ON COLUMN public.orchestration_steps.confidence IS 'Confidence score for the role classification (0.0-1.0)';

-- Add index for assignment_type for analytics
CREATE INDEX IF NOT EXISTS orchestration_steps_assignment_type_idx ON public.orchestration_steps(assignment_type);

-- Add index for confidence for analytics
CREATE INDEX IF NOT EXISTS orchestration_steps_confidence_idx ON public.orchestration_steps(confidence);
```

## What This Fixes:

1. **✅ Eliminates the schema error** - `Could not find the 'assignment_type' column`
2. **✅ Enables assignment tracking** - Track how each role got its API key
3. **✅ Enables confidence tracking** - Track role classification confidence
4. **✅ Adds analytics support** - Indexes for performance monitoring

## After Running This Migration:

Your enhanced multi-role orchestration will work perfectly with full tracking and no database errors!
