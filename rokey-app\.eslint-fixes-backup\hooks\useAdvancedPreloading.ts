'use client';

import { useEffect, useCallback, useRef, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { usePredictiveNavigation } from './usePredictiveNavigation';
import { useRoutePrefetch } from './useRoutePrefetch';

interface PreloadingStrategy {
  immediate: string[];     // Preload immediately
  onIdle: string[];       // Preload when browser is idle
  onHover: string[];      // Preload on hover
  background: string[];   // Background preload with low priority
}

interface PreloadingConfig {
  maxConcurrent: number;
  idleTimeout: number;
  hoverDelay: number;
  backgroundDelay: number;
}

const DEFAULT_CONFIG: PreloadingConfig = {
  maxConcurrent: 3,
  idleTimeout: 2000,
  hoverDelay: 100,
  backgroundDelay: 5000
};

export function useAdvancedPreloading(config: Partial<PreloadingConfig> = {}) {
  const pathname = usePathname();
  const router = useRouter();
  const { predictions, isLearning } = usePredictiveNavigation();
  const { prefetchRoute } = useRoutePrefetch();
  
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const preloadingQueue = useRef<string[]>([]);
  const activePreloads = useRef<Set<string>>(new Set());
  const idleCallbackId = useRef<number | null>(null);

  // Generate intelligent preloading strategy
  const generateStrategy = useCallback((): PreloadingStrategy => {
    const strategy: PreloadingStrategy = {
      immediate: [],
      onIdle: [],
      onHover: [],
      background: []
    };

    // Route-specific strategies
    switch (pathname) {
      case '/dashboard':
        strategy.immediate = ['/playground']; // Most likely next
        strategy.onIdle = ['/my-models', '/logs'];
        strategy.background = ['/routing-setup', '/analytics'];
        break;
        
      case '/my-models':
        strategy.immediate = ['/playground', '/routing-setup'];
        strategy.onIdle = ['/logs'];
        strategy.background = ['/dashboard', '/analytics'];
        break;
        
      case '/playground':
        strategy.immediate = ['/logs']; // Check results
        strategy.onIdle = ['/my-models'];
        strategy.background = ['/dashboard', '/training'];
        break;
        
      case '/logs':
        strategy.immediate = ['/playground']; // Test similar
        strategy.onIdle = ['/analytics'];
        strategy.background = ['/my-models', '/dashboard'];
        break;
        
      case '/routing-setup':
        strategy.immediate = ['/playground']; // Test setup
        strategy.onIdle = ['/my-models'];
        strategy.background = ['/logs', '/dashboard'];
        break;
        
      default:
        strategy.onIdle = ['/dashboard', '/playground'];
        break;
    }

    // Enhance with predictive data
    if (isLearning && predictions.length > 0) {
      // Move predicted routes to higher priority
      predictions.slice(0, 2).forEach(route => {
        if (!strategy.immediate.includes(route)) {
          strategy.immediate.unshift(route);
        }
      });
      
      // Add remaining predictions to idle preloading
      predictions.slice(2).forEach(route => {
        if (!strategy.onIdle.includes(route)) {
          strategy.onIdle.push(route);
        }
      });
    }

    // Remove current route from all strategies
    Object.keys(strategy).forEach(key => {
      strategy[key as keyof PreloadingStrategy] = strategy[key as keyof PreloadingStrategy]
        .filter(route => route !== pathname);
    });

    return strategy;
  }, [pathname, predictions, isLearning]);

  // Preload with concurrency control
  const preloadRoute = useCallback(async (route: string, priority: 'high' | 'medium' | 'low' = 'medium') => {
    if (activePreloads.current.has(route) || activePreloads.current.size >= finalConfig.maxConcurrent) {
      preloadingQueue.current.push(route);
      return;
    }

    activePreloads.current.add(route);
    
    try {
      await prefetchRoute(route, {
        priority: priority === 'medium' ? 'low' : priority,
        delay: priority === 'high' ? 0 : priority === 'medium' ? 100 : 300
      });
      console.log(`🚀 [ADVANCED PRELOAD] Successfully preloaded: ${route} (${priority})`);
    } catch (error) {
      console.warn(`⚠️ [ADVANCED PRELOAD] Failed to preload: ${route}`, error);
    } finally {
      activePreloads.current.delete(route);
      
      // Process queue
      if (preloadingQueue.current.length > 0) {
        const nextRoute = preloadingQueue.current.shift();
        if (nextRoute) {
          setTimeout(() => preloadRoute(nextRoute, 'low'), 100);
        }
      }
    }
  }, [prefetchRoute, finalConfig.maxConcurrent]);

  // Immediate preloading
  useEffect(() => {
    const strategy = generateStrategy();
    
    // Preload immediate routes
    strategy.immediate.forEach((route, index) => {
      setTimeout(() => {
        preloadRoute(route, 'high');
      }, index * 50); // Stagger to avoid overwhelming
    });
  }, [pathname, generateStrategy, preloadRoute]);

  // Idle preloading
  useEffect(() => {
    const strategy = generateStrategy();
    
    const scheduleIdlePreloading = () => {
      if (idleCallbackId.current) {
        cancelIdleCallback(idleCallbackId.current);
      }

      idleCallbackId.current = requestIdleCallback(() => {
        strategy.onIdle.forEach((route, index) => {
          setTimeout(() => {
            preloadRoute(route, 'medium');
          }, index * 200);
        });
      }, { timeout: finalConfig.idleTimeout });
    };

    scheduleIdlePreloading();

    return () => {
      if (idleCallbackId.current) {
        cancelIdleCallback(idleCallbackId.current);
      }
    };
  }, [pathname, generateStrategy, preloadRoute, finalConfig.idleTimeout]);

  // Background preloading
  useEffect(() => {
    const strategy = generateStrategy();
    
    const timer = setTimeout(() => {
      strategy.background.forEach((route, index) => {
        setTimeout(() => {
          preloadRoute(route, 'low');
        }, index * 500);
      });
    }, finalConfig.backgroundDelay);

    return () => clearTimeout(timer);
  }, [pathname, generateStrategy, preloadRoute, finalConfig.backgroundDelay]);

  // Hover preloading helper
  const createHoverPreloader = useCallback((route: string) => {
    return {
      onMouseEnter: () => {
        setTimeout(() => {
          preloadRoute(route, 'high');
        }, finalConfig.hoverDelay);
      }
    };
  }, [preloadRoute, finalConfig.hoverDelay]);

  // Get current preloading status
  const getStatus = useCallback(() => {
    return {
      activePreloads: Array.from(activePreloads.current),
      queuedPreloads: [...preloadingQueue.current],
      strategy: generateStrategy()
    };
  }, [generateStrategy]);

  // Clear all preloading
  const clearPreloading = useCallback(() => {
    activePreloads.current.clear();
    preloadingQueue.current = [];
    if (idleCallbackId.current) {
      cancelIdleCallback(idleCallbackId.current);
      idleCallbackId.current = null;
    }
  }, []);

  return {
    preloadRoute,
    createHoverPreloader,
    getStatus,
    clearPreloading,
    isPreloading: activePreloads.current.size > 0
  };
}

// Hook for monitoring preloading performance
export function usePreloadingMetrics() {
  const [metrics, setMetrics] = useState({
    totalPreloads: 0,
    successfulPreloads: 0,
    failedPreloads: 0,
    averagePreloadTime: 0,
    cacheHitRate: 0
  });

  const recordPreload = useCallback((success: boolean, duration: number) => {
    setMetrics(prev => {
      const newTotal = prev.totalPreloads + 1;
      const newSuccessful = success ? prev.successfulPreloads + 1 : prev.successfulPreloads;
      const newFailed = success ? prev.failedPreloads : prev.failedPreloads + 1;
      
      return {
        totalPreloads: newTotal,
        successfulPreloads: newSuccessful,
        failedPreloads: newFailed,
        averagePreloadTime: (prev.averagePreloadTime * prev.totalPreloads + duration) / newTotal,
        cacheHitRate: (newSuccessful / newTotal) * 100
      };
    });
  }, []);

  const resetMetrics = useCallback(() => {
    setMetrics({
      totalPreloads: 0,
      successfulPreloads: 0,
      failedPreloads: 0,
      averagePreloadTime: 0,
      cacheHitRate: 0
    });
  }, []);

  return {
    metrics,
    recordPreload,
    resetMetrics
  };
}
