'use client';

import React, { useEffect, useState } from 'react';

interface PerformanceMetrics {
  renderCount: number;
  lastRenderTime: number;
  averageRenderTime: number;
  memoryUsage?: number;
}

interface PerformanceMonitorProps {
  componentName: string;
  enabled?: boolean;
  showInUI?: boolean;
}

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ 
  componentName, 
  enabled = process.env.NODE_ENV === 'development',
  showInUI = false 
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderCount: 0,
    lastRenderTime: 0,
    averageRenderTime: 0
  });

  useEffect(() => {
    if (!enabled) return;

    const startTime = performance.now();
    
    setMetrics(prev => {
      const renderTime = performance.now() - startTime;
      const newRenderCount = prev.renderCount + 1;
      const newAverageRenderTime = (prev.averageRenderTime * prev.renderCount + renderTime) / newRenderCount;
      
      const newMetrics = {
        renderCount: newRenderCount,
        lastRenderTime: renderTime,
        averageRenderTime: newAverageRenderTime,
        memoryUsage: (performance as any).memory?.usedJSHeapSize || undefined
      };

      // Log performance metrics to console
      if (newRenderCount % 10 === 0) { // Log every 10 renders
        console.log(`[Performance] ${componentName}:`, {
          renders: newRenderCount,
          lastRender: `${renderTime.toFixed(2)}ms`,
          avgRender: `${newAverageRenderTime.toFixed(2)}ms`,
          memory: newMetrics.memoryUsage ? `${(newMetrics.memoryUsage / 1024 / 1024).toFixed(2)}MB` : 'N/A'
        });
      }

      return newMetrics;
    });
  }, [enabled, componentName]);

  if (!enabled || !showInUI) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white text-xs p-2 rounded font-mono z-50">
      <div>{componentName}</div>
      <div>Renders: {metrics.renderCount}</div>
      <div>Last: {metrics.lastRenderTime.toFixed(2)}ms</div>
      <div>Avg: {metrics.averageRenderTime.toFixed(2)}ms</div>
      {metrics.memoryUsage && (
        <div>Memory: {(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB</div>
      )}
    </div>
  );
};

// Hook for performance monitoring
export const usePerformanceMonitor = (componentName: string, enabled = process.env.NODE_ENV === 'development') => {
  const [renderCount, setRenderCount] = useState(0);

  useEffect(() => {
    if (!enabled) return;
    
    const startTime = performance.now();
    setRenderCount(prev => prev + 1);
    
    return () => {
      const renderTime = performance.now() - startTime;
      if (renderTime > 16) { // Warn if render takes longer than 16ms (60fps threshold)
        console.warn(`[Performance Warning] ${componentName} render took ${renderTime.toFixed(2)}ms`);
      }
    };
  }, [enabled, componentName]);

  return { renderCount };
};

export default PerformanceMonitor;
