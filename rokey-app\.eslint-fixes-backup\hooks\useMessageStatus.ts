'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { ProcessingStage } from '@/components/DynamicStatusIndicator';

interface MessageStatusConfig {
  enableAutoProgression?: boolean;
  stageDurations?: Partial<Record<ProcessingStage, number>>;
  onStageChange?: (stage: ProcessingStage, timestamp: number) => void;
}

interface MessageStatusReturn {
  currentStage: ProcessingStage;
  isActive: boolean;
  stageHistory: Array<{ stage: ProcessingStage; timestamp: number }>;
  startProcessing: () => void;
  updateStage: (stage: ProcessingStage) => void;
  markStreaming: () => void;
  markComplete: () => void;
  markOrchestrationStarted: () => void;
  updateOrchestrationStatus: (status: string) => void;
  reset: () => void;
  getProcessingDuration: () => number;
}

const DEFAULT_STAGE_DURATIONS: Record<ProcessingStage, number> = {
  initializing: 50,
  analyzing: 150,
  routing: 200,
  complexity_analysis: 250,
  role_classification: 300,
  preparing: 150,
  connecting: 200,
  generating: 400,
  typing: 0, // No auto-progression from typing
  finalizing: 100,
  complete: 0
};

export function useMessageStatus(config: MessageStatusConfig = {}): MessageStatusReturn {
  const {
    enableAutoProgression = true,
    stageDurations = {},
    onStageChange
  } = config;

  const [currentStage, setCurrentStage] = useState<ProcessingStage>('initializing');
  const [isActive, setIsActive] = useState(false);
  const [stageHistory, setStageHistory] = useState<Array<{ stage: ProcessingStage; timestamp: number }>>([]);
  
  const startTimeRef = useRef<number>(0);
  const timersRef = useRef<NodeJS.Timeout[]>([]);

  // Merge default durations with custom ones
  const effectiveDurations = { ...DEFAULT_STAGE_DURATIONS, ...stageDurations };

  const clearAllTimers = useCallback(() => {
    timersRef.current.forEach(timer => clearTimeout(timer));
    timersRef.current = [];
  }, []);

  const updateStage = useCallback((stage: ProcessingStage, clearTimers = true) => {
    const timestamp = Date.now();
    console.log(`🎯 Status update: ${stage} at ${timestamp}`);

    // Special handling for connecting stage - don't clear timers to allow auto-progression
    if (stage === 'connecting') {
      console.log('🎯 Connecting stage - keeping auto-progression timers active');
      setCurrentStage(stage);
      setStageHistory(prev => [...prev, { stage, timestamp }]);
      onStageChange?.(stage, timestamp);

      // Set up progression out of connecting after 2 seconds to show other processes
      const connectingTimer = setTimeout(() => {
        console.log('🎯 Auto-progressing from connecting to routing after 2s');
        setCurrentStage('routing');
        setStageHistory(prev => [...prev, { stage: 'routing', timestamp: Date.now() }]);
        onStageChange?.('routing', Date.now());
      }, 2000);

      timersRef.current.push(connectingTimer);
      return;
    }

    // Only clear timers when manually updating (not during auto-progression)
    if (clearTimers) {
      clearAllTimers();
    }

    setCurrentStage(stage);
    setStageHistory(prev => [...prev, { stage, timestamp }]);
    onStageChange?.(stage, timestamp);
  }, [onStageChange, clearAllTimers]);

  const startProcessing = useCallback(() => {
    console.log('🎯 Starting processing - dynamic progression with realistic random timing');
    setIsActive(true);
    startTimeRef.current = Date.now();
    setStageHistory([{ stage: 'initializing', timestamp: Date.now() }]);
    setCurrentStage('initializing');

    // Helper function to add realistic randomness to timing
    const randomDelay = (baseMs: number, variationPercent: number = 30) => {
      const variation = baseMs * (variationPercent / 100);
      const randomOffset = (Math.random() - 0.5) * 2 * variation;
      return Math.max(200, Math.round(baseMs + randomOffset)); // Minimum 200ms
    };

    // Dynamic progression with realistic random timing
    let cumulativeTime = 0;

    // Analyzing: 600-1200ms (simulating message parsing)
    cumulativeTime += randomDelay(900, 35);
    const timer1 = setTimeout(() => {
      console.log('🎯 Auto-progressing to analyzing');
      updateStage('analyzing', false);
    }, cumulativeTime);

    // Complexity Analysis: 800-1600ms (simulating AI complexity assessment)
    cumulativeTime += randomDelay(1200, 40);
    const timer2 = setTimeout(() => {
      console.log('🎯 Auto-progressing to complexity_analysis');
      updateStage('complexity_analysis', false);
    }, cumulativeTime);

    // Role Classification: 1000-2000ms (simulating intelligent role matching)
    cumulativeTime += randomDelay(1500, 35);
    const timer3 = setTimeout(() => {
      console.log('🎯 Auto-progressing to role_classification');
      updateStage('role_classification', false);
    }, cumulativeTime);

    // Preparing: 600-1400ms (simulating model preparation)
    cumulativeTime += randomDelay(1000, 40);
    const timer4 = setTimeout(() => {
      console.log('🎯 Auto-progressing to preparing');
      updateStage('preparing', false);
    }, cumulativeTime);

    // Connecting: 800-1600ms (simulating network connection)
    cumulativeTime += randomDelay(1200, 35);
    const timer5 = setTimeout(() => {
      console.log('🎯 Auto-progressing to connecting');
      updateStage('connecting', false);
    }, cumulativeTime);

    // Routing: 1000-2000ms (simulating API key selection and routing)
    cumulativeTime += randomDelay(1500, 40);
    const timer6 = setTimeout(() => {
      console.log('🎯 Auto-progressing to routing after connecting');
      updateStage('routing', false);
    }, cumulativeTime);

    // Fallback to generating: 800-1600ms (final preparation)
    cumulativeTime += randomDelay(1200, 35);
    const timer7 = setTimeout(() => {
      console.log('🎯 Fallback to generating');
      updateStage('generating', false);
    }, cumulativeTime);

    console.log(`🎯 Total estimated processing time: ${cumulativeTime}ms (${(cumulativeTime/1000).toFixed(1)}s)`);
    timersRef.current.push(timer1, timer2, timer3, timer4, timer5, timer6, timer7);
  }, [onStageChange, updateStage]);

  const markStreaming = useCallback(() => {
    console.log('🎯 markStreaming called - switching to typing');
    clearAllTimers();
    const timestamp = Date.now();
    setCurrentStage('typing');
    setStageHistory(prev => [...prev, { stage: 'typing', timestamp }]);
    onStageChange?.('typing', timestamp);
  }, [clearAllTimers, onStageChange]);

  const markComplete = useCallback(() => {
    clearAllTimers();
    updateStage('complete');
    setIsActive(false);
  }, [clearAllTimers, updateStage]);

  const markOrchestrationStarted = useCallback(() => {
    console.log('🎭 markOrchestrationStarted called - switching to orchestration mode');
    clearAllTimers();
    const timestamp = Date.now();
    setCurrentStage('generating'); // Use generating as base stage for orchestration
    setStageHistory(prev => [...prev, { stage: 'generating', timestamp }]);
    onStageChange?.('generating', timestamp);
  }, [clearAllTimers, onStageChange]);

  const updateOrchestrationStatus = useCallback((status: string) => {
    // This will be used to update the status text dynamically
    // For now, keep the stage as 'generating' but we could extend this
    console.log('🎭 Orchestration status update:', status);
  }, []);

  const reset = useCallback(() => {
    clearAllTimers();
    setCurrentStage('initializing');
    setIsActive(false);
    setStageHistory([]);
    startTimeRef.current = 0;
  }, [clearAllTimers]);

  const getProcessingDuration = useCallback(() => {
    if (startTimeRef.current === 0) return 0;
    return Date.now() - startTimeRef.current;
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearAllTimers();
    };
  }, [clearAllTimers]);

  return {
    currentStage,
    isActive,
    stageHistory,
    startProcessing,
    updateStage,
    markStreaming,
    markComplete,
    markOrchestrationStarted,
    updateOrchestrationStatus,
    reset,
    getProcessingDuration
  };
}

// Enhanced hook that can detect backend stages from response headers or logs
export function useSmartMessageStatus(config: MessageStatusConfig = {}) {
  const baseStatus = useMessageStatus(config);
  const [detectedStages, setDetectedStages] = useState<Set<string>>(new Set());

  // Function to analyze response headers and show appropriate status based on actual backend processes
  const analyzeResponseHeaders = useCallback((headers: Headers) => {
    console.log('🎯 Backend response received - immediately showing generating stage');

    // Look for custom headers that indicate what processes actually ran
    const roleUsed = headers.get('x-rokey-role-used');
    const routingStrategy = headers.get('x-rokey-routing-strategy');
    const complexity = headers.get('x-rokey-complexity-level');
    const provider = headers.get('x-rokey-api-key-provider');

    console.log('🎯 Headers found:', { roleUsed, routingStrategy, complexity, provider });

    // Immediately show generating since backend is about to start streaming
    console.log('🎯 Backend response received - showing generating (AI is working)');
    baseStatus.updateStage('generating');
  }, [baseStatus]);

  // Function to analyze streaming chunks for status indicators
  const analyzeStreamChunk = useCallback((chunk: string) => {
    // Look for backend log patterns in the stream
    if (chunk.includes('[Complexity Classification]') && !detectedStages.has('complexity')) {
      baseStatus.updateStage('complexity_analysis');
      setDetectedStages(prev => new Set([...prev, 'complexity']));
    }

    if (chunk.includes('[Intelligent Role Strategy]') && !detectedStages.has('role')) {
      baseStatus.updateStage('role_classification');
      setDetectedStages(prev => new Set([...prev, 'role']));
    }

    if (chunk.includes('FIRST TOKEN:') && !detectedStages.has('streaming')) {
      baseStatus.markStreaming();
      setDetectedStages(prev => new Set([...prev, 'streaming']));
    }
  }, [baseStatus, detectedStages]);

  const resetWithCleanup = useCallback(() => {
    baseStatus.reset();
    setDetectedStages(new Set());
  }, [baseStatus]);

  return {
    ...baseStatus,
    analyzeResponseHeaders,
    analyzeStreamChunk,
    reset: resetWithCleanup,
    detectedStages: Array.from(detectedStages)
  };
}

// Utility function to get user-friendly stage descriptions
export function getStageDescription(stage: ProcessingStage, context?: { 
  routingStrategy?: string; 
  complexity?: number; 
  role?: string 
}): string {
  const baseDescriptions: Record<ProcessingStage, string> = {
    initializing: 'Getting ready to process your request',
    analyzing: 'Understanding your message and requirements',
    routing: 'Finding the best AI model for your task',
    complexity_analysis: `Analyzing task complexity${context?.complexity ? ` (Level ${context.complexity})` : ''}`,
    role_classification: `Selecting AI specialist${context?.role ? ` (${context.role})` : ''}`,
    preparing: 'Setting up the AI model for your request',
    connecting: 'Establishing connection to the AI service',
    generating: 'AI is thinking and crafting your response',
    typing: 'Streaming the response to you in real-time',
    finalizing: 'Putting the finishing touches on the response',
    complete: 'Response delivered successfully'
  };

  return baseDescriptions[stage];
}

// Performance monitoring utilities
export function logStatusPerformance(stageHistory: Array<{ stage: ProcessingStage; timestamp: number }>) {
  if (stageHistory.length < 2) return;

  console.group('🎯 Message Status Performance');
  
  for (let i = 1; i < stageHistory.length; i++) {
    const current = stageHistory[i];
    const previous = stageHistory[i - 1];
    const duration = current.timestamp - previous.timestamp;
    
    console.log(`${previous.stage} → ${current.stage}: ${duration}ms`);
  }
  
  const totalDuration = stageHistory[stageHistory.length - 1].timestamp - stageHistory[0].timestamp;
  console.log(`Total processing time: ${totalDuration}ms`);
  
  console.groupEnd();
}
