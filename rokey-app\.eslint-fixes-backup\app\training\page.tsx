'use client';

import { useState, useEffect } from 'react';
import { type CustomApiConfig } from '@/types/customApiConfigs';
import { type TrainingJob, type TrainingPrompt } from '@/types/training';


export default function TrainingPage() {
  // Confirmation modal hook


  // State management
  const [customConfigs, setCustomConfigs] = useState<CustomApiConfig[]>([]);
  const [selectedConfigId, setSelectedConfigId] = useState<string>('');
  const [trainingJobs, setTrainingJobs] = useState<TrainingJob[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Prompt engineering form state
  const [trainingPrompts, setTrainingPrompts] = useState<string>('');



  // Load existing training data for a configuration
  const loadExistingTrainingData = async (configId: string) => {
    if (!configId) return;

    try {
      // Load training jobs
      const jobsResponse = await fetch(`/api/training/jobs?custom_api_config_id=${configId}`);
      if (jobsResponse.ok) {
        const jobs = await jobsResponse.json();
        if (jobs.length > 0) {
          const latestJob = jobs[0];

          // Load training prompts
          if (latestJob.training_data?.raw_prompts) {
            setTrainingPrompts(latestJob.training_data.raw_prompts);
          }


        }
      }
    } catch (err: any) {
      console.warn('Failed to load existing training data:', err);
    }
  };

  // Fetch custom API configs on component mount
  useEffect(() => {
    const fetchConfigs = async () => {
      try {
        const response = await fetch('/api/custom-configs');
        if (!response.ok) {
          throw new Error('Failed to fetch configurations');
        }
        const data: CustomApiConfig[] = await response.json();
        setCustomConfigs(data);
        if (data.length > 0) {
          setSelectedConfigId(data[0].id);
          loadExistingTrainingData(data[0].id);
        }
      } catch (err: any) {
        setError(`Failed to load configurations: ${err.message}`);
      }
    };
    fetchConfigs();
  }, []);

  // Load training data when configuration changes
  useEffect(() => {
    if (selectedConfigId) {
      loadExistingTrainingData(selectedConfigId);
    }
  }, [selectedConfigId]);









  // Process training prompts into structured format
  const processTrainingPrompts = (prompts: string) => {
    const processed = {
      system_instructions: '',
      examples: [] as Array<{input: string, output: string}>,
      behavior_guidelines: '',
      general_instructions: ''
    };

    const lines = prompts.split('\n').filter(line => line.trim());

    for (const line of lines) {
      const trimmedLine = line.trim();

      if (trimmedLine.startsWith('SYSTEM:')) {
        processed.system_instructions += trimmedLine.replace('SYSTEM:', '').trim() + '\n';
      } else if (trimmedLine.startsWith('BEHAVIOR:')) {
        processed.behavior_guidelines += trimmedLine.replace('BEHAVIOR:', '').trim() + '\n';
      } else if (trimmedLine.includes('→') || trimmedLine.includes('->')) {
        // Parse example: "User input → Expected response"
        const separator = trimmedLine.includes('→') ? '→' : '->';
        const parts = trimmedLine.split(separator);
        if (parts.length >= 2) {
          const input = parts[0].trim();
          const output = parts.slice(1).join(separator).trim();
          processed.examples.push({ input, output });
        }
      } else if (trimmedLine.length > 0) {
        // General instruction
        processed.general_instructions += trimmedLine + '\n';
      }
    }

    return processed;
  };

  // Handle training job creation or update
  const handleStartTraining = async () => {
    if (!selectedConfigId || !trainingPrompts.trim()) {
      setError('Please select an API configuration and provide training prompts.');
      return;
    }

    // Prevent multiple simultaneous training operations
    if (isLoading) {
      console.warn('[Training] Operation already in progress, ignoring duplicate request');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // Process training prompts
      const processedPrompts = processTrainingPrompts(trainingPrompts);

      // Generate a meaningful name based on content
      const configName = customConfigs.find(c => c.id === selectedConfigId)?.name || 'Unknown Config';

      // Prepare training job data
      const trainingJobData = {
        custom_api_config_id: selectedConfigId,
        name: `${configName} Training - ${new Date().toLocaleDateString()}`,
        description: `Training job for ${configName} with ${processedPrompts.examples.length} examples`,
        training_data: {
          processed_prompts: processedPrompts,
          raw_prompts: trainingPrompts.trim(),
          last_prompt_update: new Date().toISOString()
        },
        parameters: {
          training_type: 'prompt_engineering',
          created_via: 'training_page',
          version: '1.0'
        }
      };

      // Use UPSERT to handle both create and update scenarios
      const response = await fetch('/api/training/jobs/upsert', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(trainingJobData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[Training] Failed to upsert training job:', errorText);
        throw new Error(`Failed to save training job: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      const isUpdate = result.operation === 'updated';

      console.log(`[Training] Successfully ${isUpdate ? 'updated' : 'created'} training job:`, result.id);

      // Show success message based on operation type
      const operationText = isUpdate ? 'updated' : 'enhanced';
      const operationEmoji = isUpdate ? '🔄' : '🎉';

      const successMessage = `${operationEmoji} Prompt Engineering ${isUpdate ? 'updated' : 'completed'} successfully!\n\n` +
        `Your "${configName}" configuration has been ${operationText} with:\n` +
        `• ${processedPrompts.examples.length} training examples\n` +
        `• Custom system instructions and behavior guidelines\n` +
        `\n✨ All future chats using this configuration will automatically:\n` +
        `• Follow your training examples\n` +
        `• Apply your behavior guidelines\n` +
        `• Maintain consistent personality and responses\n\n` +
        `🚀 Try it now in the Playground to see your ${isUpdate ? 'updated' : 'enhanced'} model in action!\n\n` +
        `💡 Your training prompts remain here so you can modify them anytime.`;

      setSuccessMessage(successMessage);

    } catch (err: any) {
      console.error('Error in training operation:', err);
      setError(`Failed to create prompt engineering: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#faf8f5] p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Prompt Engineering</h1>
          <p className="text-lg text-gray-600 max-w-3xl">
            Create custom prompts to enhance your AI models with specific instructions, behavior guidelines, and examples.
          </p>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-xl p-4">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-red-800 text-sm font-medium">{error}</p>
            </div>
          </div>
        )}

        {successMessage && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-xl p-4">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              <p className="text-green-800 text-sm font-medium">{successMessage}</p>
            </div>
          </div>
        )}

        {/* Training Form */}
        <div className="bg-white rounded-2xl shadow-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Create Custom Prompts</h2>

          <div className="space-y-8">
            {/* API Configuration Selection */}
            <div>
              <label htmlFor="configSelect" className="block text-sm font-medium text-gray-700 mb-2">
                Select API Configuration
              </label>
              <select
                id="configSelect"
                value={selectedConfigId}
                onChange={(e) => setSelectedConfigId(e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm"
              >
                <option value="">Choose which model to train...</option>
                {customConfigs.map((config) => (
                  <option key={config.id} value={config.id}>
                    {config.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Training Prompts */}
            <div>
              <label htmlFor="trainingPrompts" className="block text-sm font-medium text-gray-700 mb-2">
                Custom Prompts & Instructions
              </label>
              <textarea
                id="trainingPrompts"
                value={trainingPrompts}
                onChange={(e) => setTrainingPrompts(e.target.value)}
                placeholder={`Enter your training prompts using these formats:

SYSTEM: You are a helpful customer service agent for our company
BEHAVIOR: Always be polite and offer solutions

User asks about returns → I'd be happy to help with your return! Let me check our policy for you.
Customer is frustrated → I understand your frustration. Let me see how I can resolve this for you.

General instructions can be written as regular text.`}
                rows={12}
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm resize-none font-mono"
              />
              <div className="mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3">
                <h4 className="text-sm font-medium text-blue-900 mb-2">Training Format Guide:</h4>
                <ul className="text-xs text-blue-800 space-y-1">
                  <li><strong>SYSTEM:</strong> Core instructions for the AI's role and personality</li>
                  <li><strong>BEHAVIOR:</strong> Guidelines for how the AI should behave</li>
                  <li><strong>Examples:</strong> Use "User input → Expected response" format</li>
                  <li><strong>General:</strong> Any other instructions written as normal text</li>
                </ul>
              </div>
            </div>



            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-6 border-t border-gray-200">
              <div className="flex space-x-3">
                <button
                  type="button"
                  className="btn-secondary"
                  onClick={() => {
                    if (confirm('Clear all training prompts?')) {
                      setTrainingPrompts('');
                    }
                  }}
                >
                  Clear Form
                </button>
              </div>

              <button
                type="button"
                onClick={handleStartTraining}
                disabled={!selectedConfigId || !trainingPrompts.trim() || isLoading}
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Processing Prompts...
                  </>
                ) : (
                  'Save Prompts'
                )}
              </button>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
}