-- Semantic Cache Migration for RouKey
-- Adds semantic caching capabilities using vector embeddings

-- Enable vector extension if not already enabled
CREATE EXTENSION IF NOT EXISTS vector;

-- 1. Create semantic_cache table for storing cached responses with embeddings
CREATE TABLE IF NOT EXISTS public.semantic_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Cache identification
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    custom_api_config_id UUID REFERENCES public.custom_api_configs(id) ON DELETE CASCADE,
    
    -- Request data
    prompt_text TEXT NOT NULL,
    prompt_embedding vector(1024) NOT NULL, -- Jina v3 embeddings (1024 dimensions)
    prompt_hash TEXT NOT NULL, -- SHA-256 hash for exact matching
    
    -- Request metadata
    model_used TEXT NOT NULL,
    provider_used TEXT NOT NULL,
    temperature DECIMAL(3,2),
    max_tokens INTEGER,
    request_metadata JSONB DEFAULT '{}'::jsonb,
    
    -- Cached response
    response_data JSONB NOT NULL,
    response_tokens_prompt INTEGER,
    response_tokens_completion INTEGER,
    response_cost DECIMAL(10,8),
    
    -- Cache metadata
    cache_tier TEXT NOT NULL CHECK (cache_tier IN ('free', 'starter', 'pro', 'enterprise')),
    hit_count INTEGER DEFAULT 0,
    last_hit_at TIMESTAMPTZ,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    
    -- Constraints
    CONSTRAINT semantic_cache_prompt_text_length CHECK (length(prompt_text) <= 50000),
    CONSTRAINT semantic_cache_hit_count_positive CHECK (hit_count >= 0)
);

-- 2. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_semantic_cache_user_config 
ON public.semantic_cache(user_id, custom_api_config_id);

CREATE INDEX IF NOT EXISTS idx_semantic_cache_prompt_hash 
ON public.semantic_cache(prompt_hash);

CREATE INDEX IF NOT EXISTS idx_semantic_cache_expires_at 
ON public.semantic_cache(expires_at);

CREATE INDEX IF NOT EXISTS idx_semantic_cache_tier_created 
ON public.semantic_cache(cache_tier, created_at DESC);

-- Vector similarity index for semantic search
CREATE INDEX IF NOT EXISTS idx_semantic_cache_embedding 
ON public.semantic_cache USING ivfflat (prompt_embedding vector_cosine_ops)
WITH (lists = 100);

-- 3. Create semantic cache analytics table
CREATE TABLE IF NOT EXISTS public.semantic_cache_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Time period
    date DATE NOT NULL,
    hour INTEGER CHECK (hour >= 0 AND hour <= 23),
    
    -- Aggregation keys
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    custom_api_config_id UUID REFERENCES public.custom_api_configs(id) ON DELETE CASCADE,
    cache_tier TEXT NOT NULL,
    
    -- Metrics
    total_requests INTEGER DEFAULT 0,
    cache_hits INTEGER DEFAULT 0,
    cache_misses INTEGER DEFAULT 0,
    cache_hit_rate DECIMAL(5,4), -- Calculated as hits / total_requests
    
    -- Cost savings
    tokens_saved INTEGER DEFAULT 0,
    cost_saved DECIMAL(10,8) DEFAULT 0,
    
    -- Performance
    avg_response_time_ms INTEGER,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    
    -- Unique constraint for aggregation
    UNIQUE(date, hour, user_id, custom_api_config_id, cache_tier)
);

-- 4. Create indexes for analytics
CREATE INDEX IF NOT EXISTS idx_semantic_cache_analytics_date_hour 
ON public.semantic_cache_analytics(date DESC, hour DESC);

CREATE INDEX IF NOT EXISTS idx_semantic_cache_analytics_user_config 
ON public.semantic_cache_analytics(user_id, custom_api_config_id, date DESC);

-- 5. Create function for semantic similarity search
CREATE OR REPLACE FUNCTION public.search_semantic_cache(
    query_embedding vector(1024),
    config_id UUID,
    user_id_param UUID,
    similarity_threshold FLOAT DEFAULT 0.85,
    match_count INTEGER DEFAULT 5
)
RETURNS TABLE (
    id UUID,
    prompt_text TEXT,
    response_data JSONB,
    model_used TEXT,
    provider_used TEXT,
    similarity FLOAT,
    hit_count INTEGER,
    created_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sc.id,
        sc.prompt_text,
        sc.response_data,
        sc.model_used,
        sc.provider_used,
        1 - (sc.prompt_embedding <=> query_embedding) AS similarity,
        sc.hit_count,
        sc.created_at,
        sc.expires_at
    FROM public.semantic_cache sc
    WHERE 
        sc.user_id = user_id_param
        AND sc.custom_api_config_id = config_id
        AND sc.expires_at > now()
        AND 1 - (sc.prompt_embedding <=> query_embedding) >= similarity_threshold
    ORDER BY sc.prompt_embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- 6. Create function for cache cleanup (remove expired entries)
CREATE OR REPLACE FUNCTION public.cleanup_semantic_cache()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.semantic_cache 
    WHERE expires_at < now();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$;

-- 7. Create function to update cache hit count
CREATE OR REPLACE FUNCTION public.increment_cache_hit(cache_id UUID)
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
    UPDATE public.semantic_cache 
    SET 
        hit_count = hit_count + 1,
        last_hit_at = now()
    WHERE id = cache_id;
END;
$$;

-- 8. Add RLS policies
ALTER TABLE public.semantic_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.semantic_cache_analytics ENABLE ROW LEVEL SECURITY;

-- Users can only access their own cache entries
CREATE POLICY "Users can access own semantic cache" ON public.semantic_cache
    FOR ALL USING (auth.uid() = user_id);

-- Users can only access their own analytics
CREATE POLICY "Users can access own cache analytics" ON public.semantic_cache_analytics
    FOR ALL USING (auth.uid() = user_id);

-- 9. Add triggers for updated_at
CREATE TRIGGER handle_updated_at_semantic_cache_analytics 
BEFORE UPDATE ON public.semantic_cache_analytics 
FOR EACH ROW EXECUTE FUNCTION public.moddatetime();

-- 10. Add comments for documentation
COMMENT ON TABLE public.semantic_cache IS 'Stores cached LLM responses with vector embeddings for semantic similarity matching';
COMMENT ON TABLE public.semantic_cache_analytics IS 'Aggregated analytics for semantic cache performance and cost savings';
COMMENT ON FUNCTION public.search_semantic_cache IS 'Searches for semantically similar cached responses using vector similarity';
COMMENT ON FUNCTION public.cleanup_semantic_cache IS 'Removes expired cache entries and returns count of deleted rows';
COMMENT ON FUNCTION public.increment_cache_hit IS 'Increments hit count and updates last hit timestamp for a cache entry';
