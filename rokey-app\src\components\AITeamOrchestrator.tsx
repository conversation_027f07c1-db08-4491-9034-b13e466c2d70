'use client';

import React, { useState, useEffect } from 'react';
import { useOrchestrationStream } from '@/hooks/useOrchestrationStream';
import { OrchestrationProgress } from './OrchestrationProgress';
import { OrchestrationNarrator } from './OrchestrationNarrator';
import { ModelStatusCard } from './ModelStatusCard';
import { 
  CpuChipIcon, 
  SparklesIcon, 
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface AITeamOrchestratorProps {
  executionId: string;
  onComplete?: (result: string) => void;
  onError?: (error: string) => void;
}

interface ModelStatus {
  roleId: string;
  modelName: string;
  status: 'waiting' | 'assigned' | 'working' | 'completed' | 'failed';
  progress: number;
  output?: string;
  duration?: number;
  quality?: number;
  isStreaming?: boolean;
}

export const AITeamOrchestrator: React.FC<AITeamOrchestratorProps> = ({
  executionId,
  onComplete,
  onError
}) => {
  console.log('🎬 [AITeamOrchestrator] Component rendered with executionId:', executionId);

  const [modelStatuses, setModelStatuses] = useState<ModelStatus[]>([]);
  const [currentNarration, setCurrentNarration] = useState<string>('🚀 Initializing AI team orchestration...');
  const [overallProgress, setOverallProgress] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [finalResult, setFinalResult] = useState<string>('');
  const [streamingResult, setStreamingResult] = useState<string>(''); // Track streaming response
  const [orchestrationPhase, setOrchestrationPhase] = useState<'planning' | 'executing' | 'synthesizing' | 'complete'>('planning');
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);

  const [synthesisStreamUrl, setSynthesisStreamUrl] = useState<string | null>(null);
  const [orchestrationStreamUrl, setOrchestrationStreamUrl] = useState<string | null>(null);
  const [initialEventReceived, setInitialEventReceived] = useState(false);

  // Use the orchestration stream hook
  const {
    events,
    isConnected,
    error: streamError,
    lastEvent
  } = useOrchestrationStream(executionId, synthesisStreamUrl || orchestrationStreamUrl || undefined);

  console.log('🎬 [AITeamOrchestrator] Stream status:', {
    isConnected,
    streamError,
    eventsCount: events.length,
    lastEventType: lastEvent?.type
  });

  // Polling fallback to check for completion
  const checkCompletion = async () => {
    try {
      const response = await fetch(`/api/orchestration/status/${executionId}`);
      if (response.ok) {
        const data = await response.json();

        if (data.execution.status === 'completed' && data.execution.finalResponse && !isComplete) {
          console.log('[AITeamOrchestrator] Polling detected completion, setting final result');
          setFinalResult(data.execution.finalResponse);
          setIsComplete(true);
          setOrchestrationPhase('complete');
          setCurrentNarration('🎉 AI team collaboration complete!');
          setStreamingResult(''); // Clear streaming result

          if (onComplete) {
            onComplete(data.execution.finalResponse);
          }

          // Clear polling
          if (pollingInterval) {
            clearInterval(pollingInterval);
            setPollingInterval(null);
          }
        } else if (data.execution.status === 'in_progress' && !isConnected && !pollingInterval) {
          // If we're not connected but the orchestration is in progress, start polling
          console.log('[AITeamOrchestrator] Detected in-progress orchestration, starting polling fallback');
          const interval = setInterval(checkCompletion, 3000);
          setPollingInterval(interval);
        }
      }
    } catch (error) {
      console.error('[AITeamOrchestrator] Polling error:', error);
    }
  };

  // Handle initial connection to the orchestration stream
  useEffect(() => {
    if (!orchestrationStreamUrl && executionId) {
      // Construct the stream URL
      const origin = window.location.origin;
      const url = `${origin}/api/orchestration/stream/${executionId}`;
      console.log(`[AITeamOrchestrator] Setting initial orchestration stream URL: ${url}`);
      setOrchestrationStreamUrl(url);
    }
  }, [executionId, orchestrationStreamUrl]);

  // Process incoming events
  useEffect(() => {
    if (!lastEvent) return;

    const event = lastEvent;
    
    // Handle the initial orchestration_started event that contains stream URLs
    if (event.type === 'orchestration_started' && !initialEventReceived) {
      setInitialEventReceived(true);
      
      // Update the stream URL if provided in the event
      if (event.data.stream_url) {
        console.log(`[AITeamOrchestrator] Updating to main orchestration stream: ${event.data.stream_url}`);
        setOrchestrationStreamUrl(event.data.stream_url);
      }
      
      // Set initial narration
      setCurrentNarration(event.data.message || '🎬 AI team is assembling...');
      setOrchestrationPhase('planning');
      return;
    }
    
    // Process other events
    switch (event.type) {
      case 'orchestration_started':
        setCurrentNarration(event.data.commentary || '🎬 AI team is assembling...');
        setOrchestrationPhase('planning');
        break;

      case 'task_decomposed':
        setCurrentNarration(event.data.commentary || '📋 Breaking down the task into specialized steps...');
        // Initialize model statuses from the decomposition
        if (event.data.steps) {
          const statuses: ModelStatus[] = event.data.steps.map((step: any) => ({
            roleId: step.roleId,
            modelName: step.modelName || 'Unknown Model',
            status: 'waiting',
            progress: 0
          }));
          setModelStatuses(statuses);
        }
        break;

      case 'step_assigned':
        setCurrentNarration(event.data.commentary || `📋 Assigning ${event.role_id} specialist...`);
        setModelStatuses(prev => prev.map(model => 
          model.roleId === event.role_id 
            ? { ...model, status: 'assigned', modelName: event.model_name || model.modelName }
            : model
        ));
        break;

      case 'step_started':
        setCurrentNarration(event.data.commentary || `🚀 ${event.role_id} is starting work...`);
        setModelStatuses(prev => prev.map(model => 
          model.roleId === event.role_id 
            ? { ...model, status: 'working', progress: 0.1 }
            : model
        ));
        setOrchestrationPhase('executing');
        break;

      case 'step_progress':
        setCurrentNarration(event.data.commentary || `⚡ ${event.role_id} is making progress...`);
        setModelStatuses(prev => prev.map(model =>
          model.roleId === event.role_id
            ? {
                ...model,
                progress: event.data.progress || 0.5,
                output: event.data.partialOutput || model.output
              }
            : model
        ));
        break;

      case 'step_streaming':
        setCurrentNarration(event.data.commentary || `💭 ${event.role_id} is thinking...`);
        setModelStatuses(prev => prev.map(model =>
          model.roleId === event.role_id
            ? {
                ...model,
                progress: event.data.progress || 0.6,
                output: event.data.partialResponse || model.output,
                isStreaming: true
              }
            : model
        ));
        break;

      case 'step_completed':
        setCurrentNarration(event.data.commentary || `✅ ${event.role_id} completed their work!`);
        setModelStatuses(prev => prev.map(model => 
          model.roleId === event.role_id 
            ? { 
                ...model, 
                status: 'completed', 
                progress: 1.0,
                output: event.data.output,
                duration: event.data.duration,
                quality: event.data.quality
              }
            : model
        ));
        break;

      case 'step_failed':
        setCurrentNarration(event.data.commentary || `❌ ${event.role_id} encountered an issue...`);
        setModelStatuses(prev => prev.map(model => 
          model.roleId === event.role_id 
            ? { ...model, status: 'failed', progress: 0 }
            : model
        ));
        if (onError) {
          onError(event.data.error || 'Step failed');
        }
        break;

      case 'synthesis_started':
        setCurrentNarration(event.data.commentary || '🧩 Combining all specialist outputs...');
        setOrchestrationPhase('synthesizing');
        
        // If we have a direct stream URL, update it
        if (event.data.directStreamUrl) {
          console.log(`[AITeamOrchestrator] Updating to direct synthesis stream: ${event.data.directStreamUrl}`);
          setSynthesisStreamUrl(event.data.directStreamUrl);
        }

        // Start polling as fallback in case stream disconnects during synthesis
        if (!pollingInterval) {
          const interval = setInterval(checkCompletion, 3000); // Check every 3 seconds
          setPollingInterval(interval);
          console.log('[AITeamOrchestrator] Started polling fallback for synthesis completion');
        }
        break;

      case 'synthesis_progress':
        setCurrentNarration(event.data.commentary || '🎨 Weaving outputs together...');
        break;

      case 'synthesis_streaming':
        setCurrentNarration(event.data.commentary || '🎨 Streaming response...');
        // Update the streaming result with the partial content
        if (event.data.partialResult) {
          setStreamingResult(event.data.partialResult);
        }
        break;

      case 'orchestration_completed':
        setCurrentNarration(event.data.commentary || '🎉 AI team collaboration complete!');
        setOrchestrationPhase('complete');
        setIsComplete(true);
        setStreamingResult(''); // Clear streaming result
        if (event.data.finalResult) {
          setFinalResult(event.data.finalResult);
          if (onComplete) {
            onComplete(event.data.finalResult);
          }
        }

        // Clear polling since we got the completion event
        if (pollingInterval) {
          clearInterval(pollingInterval);
          setPollingInterval(null);
        }
        break;

      case 'moderator_commentary':
        setCurrentNarration(event.data.commentary || '🤖 Moderator is coordinating...');
        break;
    }

    // Update overall progress
    const completedModels = modelStatuses.filter(m => m.status === 'completed').length;
    const totalModels = modelStatuses.length;
    if (totalModels > 0) {
      setOverallProgress((completedModels / totalModels) * 100);
    }
  }, [lastEvent, modelStatuses.length, onComplete, onError]);

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [pollingInterval]);

  // Handle stream errors
  useEffect(() => {
    if (streamError && onError) {
      onError(streamError);
    }
  }, [streamError, onError]);

  const getPhaseIcon = () => {
    switch (orchestrationPhase) {
      case 'planning':
        return <ClockIcon className="w-6 h-6 text-blue-500" />;
      case 'executing':
        return <CpuChipIcon className="w-6 h-6 text-orange-500 animate-pulse" />;
      case 'synthesizing':
        return <SparklesIcon className="w-6 h-6 text-purple-500 animate-spin" />;
      case 'complete':
        return <CheckCircleIcon className="w-6 h-6 text-green-500" />;
      default:
        return <ClockIcon className="w-6 h-6 text-gray-500" />;
    }
  };

  const getPhaseLabel = () => {
    switch (orchestrationPhase) {
      case 'planning':
        return 'Planning & Assignment';
      case 'executing':
        return 'AI Team Execution';
      case 'synthesizing':
        return 'Synthesizing Results';
      case 'complete':
        return 'Complete';
      default:
        return 'Initializing';
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded-xl shadow-lg">
      {/* Debug Info */}
      <div className="mb-4 bg-blue-50 border border-blue-200 rounded-lg p-3 text-xs text-blue-800">
        <strong>🔧 Debug:</strong> ExecutionId: {executionId} | Connected: {isConnected ? '✅' : '❌'} |
        Events: {events.length} | Phase: {orchestrationPhase} | Error: {streamError || 'None'}
      </div>

      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          {getPhaseIcon()}
          <div>
            <h2 className="text-xl font-bold text-gray-900">AI Team Orchestration</h2>
            <p className="text-sm text-gray-600">{getPhaseLabel()}</p>
          </div>
        </div>
        
        {/* Connection Status */}
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-sm text-gray-600">
            {isConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
      </div>

      {/* Overall Progress */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700">Overall Progress</span>
          <span className="text-sm text-gray-600">{Math.round(overallProgress)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500"
            style={{ width: `${overallProgress}%` }}
          />
        </div>
      </div>

      {/* Live Narration */}
      <OrchestrationNarrator 
        currentNarration={currentNarration}
        phase={orchestrationPhase}
      />

      {/* Model Status Cards */}
      {modelStatuses.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">AI Specialists</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {modelStatuses.map((model, index) => (
              <ModelStatusCard
                key={`${model.roleId}-${index}`}
                roleId={model.roleId}
                modelName={model.modelName}
                status={model.status}
                progress={model.progress}
                output={model.output}
                duration={model.duration}
                quality={model.quality}
              />
            ))}
          </div>
        </div>
      )}

      {/* Progress Visualization */}
      <OrchestrationProgress 
        modelStatuses={modelStatuses}
        currentPhase={orchestrationPhase}
        overallProgress={overallProgress}
      />

      {/* Streaming Result */}
      {orchestrationPhase === 'synthesizing' && streamingResult && (
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">
            🎨 Streaming Response...
          </h3>
          <div className="text-sm text-blue-800 whitespace-pre-wrap">
            {streamingResult}
            <span className="animate-pulse">|</span>
          </div>
        </div>
      )}

      {/* Final Result */}
      {isComplete && finalResult && (
        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <h3 className="text-lg font-semibold text-green-900 mb-2">
            🎉 Orchestration Complete!
          </h3>
          <div className="text-sm text-green-800 whitespace-pre-wrap">
            {finalResult}
          </div>
        </div>
      )}

      {/* Error State */}
      {streamError && (
        <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />
            <h3 className="text-lg font-semibold text-red-900">Connection Error</h3>
          </div>
          <p className="text-sm text-red-800 mt-1">{streamError}</p>
        </div>
      )}
    </div>
  );
};
